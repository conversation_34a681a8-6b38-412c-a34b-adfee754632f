#!/bin/bash

# Run database migrations on Cloud Run
# Usage: ./migrate.sh [PROJECT_ID] [REGION]

set -e

PROJECT_ID=${1:-"your-project-id"}
REGION=${2:-"us-central1"}
JOB_NAME="nextjs-migrate-job"

echo "🗄️  Running database migrations..."
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Create migration job if it doesn't exist
echo "📋 Creating migration job..."

# Check if job exists
if ! gcloud run jobs describe $JOB_NAME --region $REGION >/dev/null 2>&1; then
    echo "✨ Creating new migration job..."
    
    # Get the latest image from the service
    SERVICE_IMAGE=$(gcloud run services describe nextjs-cloudsql-app --region $REGION --format 'value(spec.template.spec.template.spec.containers[0].image)')
    
    gcloud run jobs create $JOB_NAME \
        --image $SERVICE_IMAGE \
        --region $REGION \
        --task-timeout 600 \
        --memory 1Gi \
        --cpu 1 \
        --max-retries 3 \
        --parallelism 1 \
        --task-count 1 \
        --set-env-vars NODE_ENV=production \
        --set-secrets DATABASE_URL=database-url:latest \
        --set-secrets DB_USER=db-user:latest \
        --set-secrets DB_PASS=db-password:latest \
        --set-secrets DB_NAME=db-name:latest \
        --set-secrets INSTANCE_CONNECTION_NAME=instance-connection-name:latest \
        --command npx \
        --args prisma,migrate,deploy
else
    echo "↻ Migration job already exists, updating..."
    
    # Update existing job
    SERVICE_IMAGE=$(gcloud run services describe nextjs-cloudsql-app --region $REGION --format 'value(spec.template.spec.template.spec.containers[0].image)')
    
    gcloud run jobs update $JOB_NAME \
        --image $SERVICE_IMAGE \
        --region $REGION
fi

# Execute the migration job
echo "🚀 Executing migration job..."
gcloud run jobs execute $JOB_NAME --region $REGION --wait

echo "✅ Database migration completed!"

# Optional: Generate Prisma client job
echo ""
echo "📋 Creating Prisma generate job..."
GENERATE_JOB_NAME="nextjs-generate-job"

if ! gcloud run jobs describe $GENERATE_JOB_NAME --region $REGION >/dev/null 2>&1; then
    echo "✨ Creating new Prisma generate job..."
    
    gcloud run jobs create $GENERATE_JOB_NAME \
        --image $SERVICE_IMAGE \
        --region $REGION \
        --task-timeout 300 \
        --memory 512Mi \
        --cpu 1 \
        --max-retries 2 \
        --parallelism 1 \
        --task-count 1 \
        --set-env-vars NODE_ENV=production \
        --command npx \
        --args prisma,generate
else
    echo "↻ Prisma generate job already exists, updating..."
    gcloud run jobs update $GENERATE_JOB_NAME \
        --image $SERVICE_IMAGE \
        --region $REGION
fi

echo "🚀 Executing Prisma generate job..."
gcloud run jobs execute $GENERATE_JOB_NAME --region $REGION --wait

echo "✅ Prisma client generation completed!"
echo ""
echo "🔗 Useful commands:"
echo "  View migration logs: gcloud run jobs executions logs $JOB_NAME --region $REGION"
echo "  List jobs: gcloud run jobs list --region $REGION"
echo "  Delete job: gcloud run jobs delete $JOB_NAME --region $REGION"
