# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/database_name"

# Google Cloud SQL Configuration
INSTANCE_CONNECTION_NAME="your-project:region:instance-name"
DB_USER="your-db-user"
DB_PASS="your-db-password"
DB_NAME="your-database-name"

# Google Cloud Configuration
GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"

# Next.js Configuration
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3000"

# Cloud Run Configuration (for production)
PORT=8080
