#!/bin/bash

# Deploy Next.js app to Google Cloud Run
# Usage: ./deploy.sh [PROJECT_ID] [REGION] [INSTANCE_CONNECTION_NAME]

set -e

# Default values
PROJECT_ID=${1:-"your-project-id"}
REGION=${2:-"us-central1"}
INSTANCE_CONNECTION_NAME=${3:-"your-project:us-central1:your-instance"}
SERVICE_NAME="nextjs-cloudsql-app"

echo "🚀 Deploying Next.js app to Cloud Run..."
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Instance Connection: $INSTANCE_CONNECTION_NAME"

# Set the project
echo "📋 Setting project..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable secretmanager.googleapis.com

# Build and push the image
echo "🏗️  Building and pushing Docker image..."
IMAGE_URL="gcr.io/$PROJECT_ID/$SERVICE_NAME"
docker build -t $IMAGE_URL .
docker push $IMAGE_URL

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
  --image $IMAGE_URL \
  --region $REGION \
  --platform managed \
  --allow-unauthenticated \
  --port 8080 \
  --memory 1Gi \
  --cpu 1 \
  --max-instances 10 \
  --set-env-vars NODE_ENV=production \
  --add-cloudsql-instances $INSTANCE_CONNECTION_NAME

echo "✅ Deployment completed!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
echo "🌐 Service URL: $SERVICE_URL"

echo ""
echo "📝 Next steps:"
echo "1. Set up your Cloud SQL database secrets in Secret Manager"
echo "2. Update the Cloud Run service with database secrets"
echo "3. Run database migrations: gcloud run jobs execute migrate-job --region $REGION"
echo ""
echo "🔗 Useful commands:"
echo "  View logs: gcloud run services logs tail $SERVICE_NAME --region $REGION"
echo "  Update service: gcloud run services update $SERVICE_NAME --region $REGION"
echo "  Delete service: gcloud run services delete $SERVICE_NAME --region $REGION"
