-- Initialize database for Next.js Cloud SQL App
CREATE DATABASE IF NOT EXISTS nextjs_cloudsql_db;

-- Create user if not exists
CREATE USER IF NOT EXISTS 'nextjs_user'@'%' IDENTIFIED BY 'nextjs_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON nextjs_cloudsql_db.* TO 'nextjs_user'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the database
USE nextjs_cloudsql_db;

-- Create sample data (will be created by Prisma migrations)
-- This is just for reference
