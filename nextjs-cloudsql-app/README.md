# Next.js + Google Cloud SQL Application

A modern, scalable web application built with Next.js, TypeScript, Prisma ORM, and Google Cloud SQL, designed for deployment on Google Cloud Run.

## 🚀 Features

- **Next.js 15** with App Router and TypeScript
- **Google Cloud SQL** integration with MySQL
- **Prisma ORM** for type-safe database operations
- **RESTful API** with comprehensive CRUD operations
- **Docker** containerization for consistent deployments
- **Google Cloud Run** ready with automatic scaling
- **Health checks** and monitoring endpoints
- **Database migrations** and seeding
- **Responsive UI** with Tailwind CSS

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v18 or higher)
- [Docker](https://www.docker.com/) and Docker Compose
- [Google Cloud CLI](https://cloud.google.com/sdk/docs/install)
- A Google Cloud Project with billing enabled

## 🛠️ Local Development Setup

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd nextjs-cloudsql-app
npm install
```

### 2. Environment Configuration

Copy the environment example file:

```bash
cp .env.example .env.local
```

Update `.env.local` with your database configuration:

```env
DATABASE_URL="mysql://root:password@localhost:3306/nextjs_cloudsql_db"
INSTANCE_CONNECTION_NAME="your-project:us-central1:your-instance"
DB_USER="root"
DB_PASS="your-password"
DB_NAME="nextjs_cloudsql_db"
```

### 3. Database Setup

#### Option A: Using Docker (Recommended for local development)

```bash
# Start MySQL database
docker-compose up mysql -d

# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed the database
npm run db:seed
```

#### Option B: Using Cloud SQL Proxy

```bash
# Download and start Cloud SQL Proxy
./cloud_sql_proxy -instances=YOUR_INSTANCE_CONNECTION_NAME=tcp:3306

# In another terminal
npm run db:generate
npm run db:migrate
npm run db:seed
```

### 4. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🐳 Docker Development

### Local Development with Docker

```bash
# Start development environment
npm run docker:dev

# Or start production environment
npm run docker:prod
```

### Build Production Image

```bash
npm run docker:build
npm run docker:run
```

## ☁️ Google Cloud Deployment

### 1. Prerequisites

- Google Cloud Project with billing enabled
- Cloud SQL instance created
- Required APIs enabled:
  - Cloud Run API
  - Cloud SQL Admin API
  - Secret Manager API
  - Cloud Build API

### 2. Create Cloud SQL Instance

```bash
# Create Cloud SQL instance
gcloud sql instances create your-instance \
  --database-version=MYSQL_8_0 \
  --tier=db-f1-micro \
  --region=us-central1

# Create database
gcloud sql databases create nextjs_cloudsql_db --instance=your-instance

# Create database user
gcloud sql users create nextjs_user \
  --instance=your-instance \
  --password=your-secure-password
```

### 3. Setup Secrets

Use the provided script to setup secrets in Google Secret Manager:

```bash
npm run setup-secrets
```

Or manually create secrets:

```bash
# Create secrets
echo -n "mysql://user:pass@localhost/db?host=/cloudsql/project:region:instance" | \
  gcloud secrets create database-url --data-file=-

echo -n "your-db-user" | gcloud secrets create db-user --data-file=-
echo -n "your-db-password" | gcloud secrets create db-password --data-file=-
echo -n "your-db-name" | gcloud secrets create db-name --data-file=-
```

### 4. Deploy to Cloud Run

#### Option A: Using the Deploy Script (Recommended)

```bash
npm run deploy YOUR_PROJECT_ID us-central1 YOUR_INSTANCE_CONNECTION_NAME
```

#### Option B: Manual Deployment

```bash
# Build and push image
docker build -t gcr.io/YOUR_PROJECT_ID/nextjs-cloudsql-app .
docker push gcr.io/YOUR_PROJECT_ID/nextjs-cloudsql-app

# Deploy to Cloud Run
gcloud run deploy nextjs-cloudsql-app \
  --image gcr.io/YOUR_PROJECT_ID/nextjs-cloudsql-app \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --add-cloudsql-instances YOUR_INSTANCE_CONNECTION_NAME \
  --set-secrets DATABASE_URL=database-url:latest
```

### 5. Run Database Migrations

```bash
npm run migrate-cloud YOUR_PROJECT_ID us-central1
```

### 6. Verify Deployment

Visit your Cloud Run service URL and check:
- Health endpoint: `https://your-service-url/api/health`
- API endpoints: `https://your-service-url/api/users`

## 📚 API Documentation

### Health Check
- `GET /api/health` - Check application and database health

### Users API
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user
- `GET /api/users/[id]` - Get user by ID
- `PUT /api/users/[id]` - Update user
- `DELETE /api/users/[id]` - Delete user

### Posts API
- `GET /api/posts` - Get all posts (supports `?published=true&authorId=1`)
- `POST /api/posts` - Create a new post
- `GET /api/posts/[id]` - Get post by ID
- `PUT /api/posts/[id]` - Update post
- `DELETE /api/posts/[id]` - Delete post

### Example API Usage

```bash
# Create a user
curl -X POST https://your-service-url/api/users \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "name": "Test User"}'

# Create a post
curl -X POST https://your-service-url/api/posts \
  -H "Content-Type: application/json" \
  -d '{"title": "My Post", "content": "Post content", "authorId": 1, "published": true}'

# Get all published posts
curl https://your-service-url/api/posts?published=true
```

## 🛠️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build production application
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:migrate` - Run database migrations (dev)
- `npm run db:deploy` - Deploy migrations (production)
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with sample data
- `npm run docker:build` - Build Docker image
- `npm run docker:run` - Run Docker container
- `npm run docker:dev` - Start development with Docker Compose
- `npm run docker:prod` - Start production with Docker Compose
- `npm run deploy` - Deploy to Cloud Run
- `npm run setup-secrets` - Setup Google Cloud secrets
- `npm run migrate-cloud` - Run migrations on Cloud Run

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check if Cloud SQL instance is running
gcloud sql instances list

# Test connection with Cloud SQL Proxy
./cloud_sql_proxy -instances=YOUR_INSTANCE_CONNECTION_NAME=tcp:3306

# Check secrets
gcloud secrets versions access latest --secret="database-url"
```

#### Cloud Run Deployment Issues
```bash
# Check service logs
gcloud run services logs tail nextjs-cloudsql-app --region us-central1

# Check service status
gcloud run services describe nextjs-cloudsql-app --region us-central1

# Update service with new image
gcloud run services update nextjs-cloudsql-app \
  --image gcr.io/YOUR_PROJECT_ID/nextjs-cloudsql-app:latest \
  --region us-central1
```

#### Migration Issues
```bash
# Reset database (development only)
npm run db:migrate -- --reset

# Check migration status
npx prisma migrate status

# Generate new migration
npx prisma migrate dev --name your-migration-name
```

### Performance Optimization

1. **Database Connection Pooling**: Already configured in `src/lib/db.ts`
2. **Image Optimization**: Use Next.js Image component
3. **Caching**: Implement Redis for session/data caching
4. **CDN**: Use Google Cloud CDN for static assets

## 📁 Project Structure

```
nextjs-cloudsql-app/
├── src/
│   ├── app/
│   │   ├── api/          # API routes
│   │   ├── globals.css   # Global styles
│   │   ├── layout.tsx    # Root layout
│   │   └── page.tsx      # Home page
│   └── lib/
│       └── db.ts         # Database configuration
├── prisma/
│   ├── schema.prisma     # Database schema
│   └── seed.ts          # Database seeding
├── public/              # Static assets
├── Dockerfile           # Production Docker image
├── Dockerfile.dev       # Development Docker image
├── docker-compose.yml   # Docker Compose configuration
├── cloudbuild.yaml      # Google Cloud Build configuration
├── deploy.sh           # Deployment script
├── setup-secrets.sh    # Secrets setup script
├── migrate.sh          # Migration script
└── README.md           # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework
- [Prisma](https://www.prisma.io/) - Next-generation ORM
- [Google Cloud](https://cloud.google.com/) - Cloud platform
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework

## 📞 Support

If you have any questions or need help, please:

1. Check the [troubleshooting section](#-troubleshooting)
2. Search existing [GitHub issues](https://github.com/your-repo/issues)
3. Create a new issue with detailed information

---

**Happy coding! 🚀**
