const CHUNK_PUBLIC_PATH = "server/app/api/health/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__da4a5b3b._.js");
runtime.loadChunk("server/chunks/node_modules_next_81e99d42._.js");
runtime.loadChunk("server/chunks/node_modules_google-auth-library_99bf3341._.js");
runtime.loadChunk("server/chunks/node_modules_@googleapis_sqladmin_build_0d26eeb8._.js");
runtime.loadChunk("server/chunks/node_modules_mysql2_a3ea85f4._.js");
runtime.loadChunk("server/chunks/node_modules_iconv-lite_1d546d44._.js");
runtime.loadChunk("server/chunks/node_modules_aws-ssl-profiles_lib_a90e16a3._.js");
runtime.loadChunk("server/chunks/node_modules_2b227e6f._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__62c16917._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/health/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/health/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/health/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
