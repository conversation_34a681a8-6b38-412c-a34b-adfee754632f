{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/crypto/shared.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2025 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fromArrayBufferToHex = fromArrayBufferToHex;\n/**\n * Converts an ArrayBuffer to a hexadecimal string.\n * @param arrayBuffer The ArrayBuffer to convert to hexadecimal string.\n * @return The hexadecimal encoding of the ArrayBuffer.\n */\nfunction fromArrayBufferToHex(arrayBuffer) {\n    // Convert buffer to byte array.\n    const byteArray = Array.from(new Uint8Array(arrayBuffer));\n    // Convert bytes to hex string.\n    return byteArray\n        .map(byte => {\n        return byte.toString(16).padStart(2, '0');\n    })\n        .join('');\n}\n//# sourceMappingURL=shared.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,oBAAoB,GAAG;AAC/B;;;;CAIC,GACD,SAAS,qBAAqB,WAAW;IACrC,gCAAgC;IAChC,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,WAAW;IAC5C,+BAA+B;IAC/B,OAAO,UACF,GAAG,CAAC,CAAA;QACL,OAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;IACzC,GACK,IAAI,CAAC;AACd,EACA,kCAAkC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/crypto/browser/crypto.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2019 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/* global window */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BrowserCrypto = void 0;\n// This file implements crypto functions we need using in-browser\n// SubtleCrypto interface `window.crypto.subtle`.\nconst base64js = require(\"base64-js\");\nconst shared_1 = require(\"../shared\");\nclass BrowserCrypto {\n    constructor() {\n        if (typeof window === 'undefined' ||\n            window.crypto === undefined ||\n            window.crypto.subtle === undefined) {\n            throw new Error(\"SubtleCrypto not found. Make sure it's an https:// website.\");\n        }\n    }\n    async sha256DigestBase64(str) {\n        // SubtleCrypto digest() method is async, so we must make\n        // this method async as well.\n        // To calculate SHA256 digest using SubtleCrypto, we first\n        // need to convert an input string to an ArrayBuffer:\n        const inputBuffer = new TextEncoder().encode(str);\n        // Result is ArrayBuffer as well.\n        const outputBuffer = await window.crypto.subtle.digest('SHA-256', inputBuffer);\n        return base64js.fromByteArray(new Uint8Array(outputBuffer));\n    }\n    randomBytesBase64(count) {\n        const array = new Uint8Array(count);\n        window.crypto.getRandomValues(array);\n        return base64js.fromByteArray(array);\n    }\n    static padBase64(base64) {\n        // base64js requires padding, so let's add some '='\n        while (base64.length % 4 !== 0) {\n            base64 += '=';\n        }\n        return base64;\n    }\n    async verify(pubkey, data, signature) {\n        const algo = {\n            name: 'RSASSA-PKCS1-v1_5',\n            hash: { name: 'SHA-256' },\n        };\n        const dataArray = new TextEncoder().encode(data);\n        const signatureArray = base64js.toByteArray(BrowserCrypto.padBase64(signature));\n        const cryptoKey = await window.crypto.subtle.importKey('jwk', pubkey, algo, true, ['verify']);\n        // SubtleCrypto's verify method is async so we must make\n        // this method async as well.\n        const result = await window.crypto.subtle.verify(algo, cryptoKey, signatureArray, dataArray);\n        return result;\n    }\n    async sign(privateKey, data) {\n        const algo = {\n            name: 'RSASSA-PKCS1-v1_5',\n            hash: { name: 'SHA-256' },\n        };\n        const dataArray = new TextEncoder().encode(data);\n        const cryptoKey = await window.crypto.subtle.importKey('jwk', privateKey, algo, true, ['sign']);\n        // SubtleCrypto's sign method is async so we must make\n        // this method async as well.\n        const result = await window.crypto.subtle.sign(algo, cryptoKey, dataArray);\n        return base64js.fromByteArray(new Uint8Array(result));\n    }\n    decodeBase64StringUtf8(base64) {\n        const uint8array = base64js.toByteArray(BrowserCrypto.padBase64(base64));\n        const result = new TextDecoder().decode(uint8array);\n        return result;\n    }\n    encodeBase64StringUtf8(text) {\n        const uint8array = new TextEncoder().encode(text);\n        const result = base64js.fromByteArray(uint8array);\n        return result;\n    }\n    /**\n     * Computes the SHA-256 hash of the provided string.\n     * @param str The plain text string to hash.\n     * @return A promise that resolves with the SHA-256 hash of the provided\n     *   string in hexadecimal encoding.\n     */\n    async sha256DigestHex(str) {\n        // SubtleCrypto digest() method is async, so we must make\n        // this method async as well.\n        // To calculate SHA256 digest using SubtleCrypto, we first\n        // need to convert an input string to an ArrayBuffer:\n        const inputBuffer = new TextEncoder().encode(str);\n        // Result is ArrayBuffer as well.\n        const outputBuffer = await window.crypto.subtle.digest('SHA-256', inputBuffer);\n        return (0, shared_1.fromArrayBufferToHex)(outputBuffer);\n    }\n    /**\n     * Computes the HMAC hash of a message using the provided crypto key and the\n     * SHA-256 algorithm.\n     * @param key The secret crypto key in utf-8 or ArrayBuffer format.\n     * @param msg The plain text message.\n     * @return A promise that resolves with the HMAC-SHA256 hash in ArrayBuffer\n     *   format.\n     */\n    async signWithHmacSha256(key, msg) {\n        // Convert key, if provided in ArrayBuffer format, to string.\n        const rawKey = typeof key === 'string'\n            ? key\n            : String.fromCharCode(...new Uint16Array(key));\n        const enc = new TextEncoder();\n        const cryptoKey = await window.crypto.subtle.importKey('raw', enc.encode(rawKey), {\n            name: 'HMAC',\n            hash: {\n                name: 'SHA-256',\n            },\n        }, false, ['sign']);\n        return window.crypto.subtle.sign('HMAC', cryptoKey, enc.encode(msg));\n    }\n}\nexports.BrowserCrypto = BrowserCrypto;\n//# sourceMappingURL=crypto.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,iBAAiB,GACjB,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,iEAAiE;AACjE,iDAAiD;AACjD,MAAM;AACN,MAAM;AACN,MAAM;IACF,aAAc;QACV,wCAEwC;YACpC,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,mBAAmB,GAAG,EAAE;QAC1B,yDAAyD;QACzD,6BAA6B;QAC7B,0DAA0D;QAC1D,qDAAqD;QACrD,MAAM,cAAc,IAAI,cAAc,MAAM,CAAC;QAC7C,iCAAiC;QACjC,MAAM,eAAe,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW;QAClE,OAAO,SAAS,aAAa,CAAC,IAAI,WAAW;IACjD;IACA,kBAAkB,KAAK,EAAE;QACrB,MAAM,QAAQ,IAAI,WAAW;QAC7B,OAAO,MAAM,CAAC,eAAe,CAAC;QAC9B,OAAO,SAAS,aAAa,CAAC;IAClC;IACA,OAAO,UAAU,MAAM,EAAE;QACrB,mDAAmD;QACnD,MAAO,OAAO,MAAM,GAAG,MAAM,EAAG;YAC5B,UAAU;QACd;QACA,OAAO;IACX;IACA,MAAM,OAAO,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE;QAClC,MAAM,OAAO;YACT,MAAM;YACN,MAAM;gBAAE,MAAM;YAAU;QAC5B;QACA,MAAM,YAAY,IAAI,cAAc,MAAM,CAAC;QAC3C,MAAM,iBAAiB,SAAS,WAAW,CAAC,cAAc,SAAS,CAAC;QACpE,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,QAAQ,MAAM,MAAM;YAAC;SAAS;QAC5F,wDAAwD;QACxD,6BAA6B;QAC7B,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,WAAW,gBAAgB;QAClF,OAAO;IACX;IACA,MAAM,KAAK,UAAU,EAAE,IAAI,EAAE;QACzB,MAAM,OAAO;YACT,MAAM;YACN,MAAM;gBAAE,MAAM;YAAU;QAC5B;QACA,MAAM,YAAY,IAAI,cAAc,MAAM,CAAC;QAC3C,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,YAAY,MAAM,MAAM;YAAC;SAAO;QAC9F,sDAAsD;QACtD,6BAA6B;QAC7B,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,WAAW;QAChE,OAAO,SAAS,aAAa,CAAC,IAAI,WAAW;IACjD;IACA,uBAAuB,MAAM,EAAE;QAC3B,MAAM,aAAa,SAAS,WAAW,CAAC,cAAc,SAAS,CAAC;QAChE,MAAM,SAAS,IAAI,cAAc,MAAM,CAAC;QACxC,OAAO;IACX;IACA,uBAAuB,IAAI,EAAE;QACzB,MAAM,aAAa,IAAI,cAAc,MAAM,CAAC;QAC5C,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO;IACX;IACA;;;;;KAKC,GACD,MAAM,gBAAgB,GAAG,EAAE;QACvB,yDAAyD;QACzD,6BAA6B;QAC7B,0DAA0D;QAC1D,qDAAqD;QACrD,MAAM,cAAc,IAAI,cAAc,MAAM,CAAC;QAC7C,iCAAiC;QACjC,MAAM,eAAe,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW;QAClE,OAAO,CAAC,GAAG,SAAS,oBAAoB,EAAE;IAC9C;IACA;;;;;;;KAOC,GACD,MAAM,mBAAmB,GAAG,EAAE,GAAG,EAAE;QAC/B,6DAA6D;QAC7D,MAAM,SAAS,OAAO,QAAQ,WACxB,MACA,OAAO,YAAY,IAAI,IAAI,YAAY;QAC7C,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,MAAM,CAAC,SAAS;YAC9E,MAAM;YACN,MAAM;gBACF,MAAM;YACV;QACJ,GAAG,OAAO;YAAC;SAAO;QAClB,OAAO,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,WAAW,IAAI,MAAM,CAAC;IACnE;AACJ;AACA,QAAQ,aAAa,GAAG,eACxB,kCAAkC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/crypto/node/crypto.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2019 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NodeCrypto = void 0;\nconst crypto = require(\"crypto\");\nclass NodeCrypto {\n    async sha256DigestBase64(str) {\n        return crypto.createHash('sha256').update(str).digest('base64');\n    }\n    randomBytesBase64(count) {\n        return crypto.randomBytes(count).toString('base64');\n    }\n    async verify(pubkey, data, signature) {\n        const verifier = crypto.createVerify('RSA-SHA256');\n        verifier.update(data);\n        verifier.end();\n        return verifier.verify(pubkey, signature, 'base64');\n    }\n    async sign(privateKey, data) {\n        const signer = crypto.createSign('RSA-SHA256');\n        signer.update(data);\n        signer.end();\n        return signer.sign(privateKey, 'base64');\n    }\n    decodeBase64StringUtf8(base64) {\n        return Buffer.from(base64, 'base64').toString('utf-8');\n    }\n    encodeBase64StringUtf8(text) {\n        return Buffer.from(text, 'utf-8').toString('base64');\n    }\n    /**\n     * Computes the SHA-256 hash of the provided string.\n     * @param str The plain text string to hash.\n     * @return A promise that resolves with the SHA-256 hash of the provided\n     *   string in hexadecimal encoding.\n     */\n    async sha256DigestHex(str) {\n        return crypto.createHash('sha256').update(str).digest('hex');\n    }\n    /**\n     * Computes the HMAC hash of a message using the provided crypto key and the\n     * SHA-256 algorithm.\n     * @param key The secret crypto key in utf-8 or ArrayBuffer format.\n     * @param msg The plain text message.\n     * @return A promise that resolves with the HMAC-SHA256 hash in ArrayBuffer\n     *   format.\n     */\n    async signWithHmacSha256(key, msg) {\n        const cryptoKey = typeof key === 'string' ? key : toBuffer(key);\n        return toArrayBuffer(crypto.createHmac('sha256', cryptoKey).update(msg).digest());\n    }\n}\nexports.NodeCrypto = NodeCrypto;\n/**\n * Converts a Node.js Buffer to an ArrayBuffer.\n * https://stackoverflow.com/questions/8609289/convert-a-binary-nodejs-buffer-to-javascript-arraybuffer\n * @param buffer The Buffer input to covert.\n * @return The ArrayBuffer representation of the input.\n */\nfunction toArrayBuffer(buffer) {\n    return buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength);\n}\n/**\n * Converts an ArrayBuffer to a Node.js Buffer.\n * @param arrayBuffer The ArrayBuffer input to covert.\n * @return The Buffer representation of the input.\n */\nfunction toBuffer(arrayBuffer) {\n    return Buffer.from(arrayBuffer);\n}\n//# sourceMappingURL=crypto.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,KAAK;AAC1B,MAAM;AACN,MAAM;IACF,MAAM,mBAAmB,GAAG,EAAE;QAC1B,OAAO,OAAO,UAAU,CAAC,UAAU,MAAM,CAAC,KAAK,MAAM,CAAC;IAC1D;IACA,kBAAkB,KAAK,EAAE;QACrB,OAAO,OAAO,WAAW,CAAC,OAAO,QAAQ,CAAC;IAC9C;IACA,MAAM,OAAO,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE;QAClC,MAAM,WAAW,OAAO,YAAY,CAAC;QACrC,SAAS,MAAM,CAAC;QAChB,SAAS,GAAG;QACZ,OAAO,SAAS,MAAM,CAAC,QAAQ,WAAW;IAC9C;IACA,MAAM,KAAK,UAAU,EAAE,IAAI,EAAE;QACzB,MAAM,SAAS,OAAO,UAAU,CAAC;QACjC,OAAO,MAAM,CAAC;QACd,OAAO,GAAG;QACV,OAAO,OAAO,IAAI,CAAC,YAAY;IACnC;IACA,uBAAuB,MAAM,EAAE;QAC3B,OAAO,OAAO,IAAI,CAAC,QAAQ,UAAU,QAAQ,CAAC;IAClD;IACA,uBAAuB,IAAI,EAAE;QACzB,OAAO,OAAO,IAAI,CAAC,MAAM,SAAS,QAAQ,CAAC;IAC/C;IACA;;;;;KAKC,GACD,MAAM,gBAAgB,GAAG,EAAE;QACvB,OAAO,OAAO,UAAU,CAAC,UAAU,MAAM,CAAC,KAAK,MAAM,CAAC;IAC1D;IACA;;;;;;;KAOC,GACD,MAAM,mBAAmB,GAAG,EAAE,GAAG,EAAE;QAC/B,MAAM,YAAY,OAAO,QAAQ,WAAW,MAAM,SAAS;QAC3D,OAAO,cAAc,OAAO,UAAU,CAAC,UAAU,WAAW,MAAM,CAAC,KAAK,MAAM;IAClF;AACJ;AACA,QAAQ,UAAU,GAAG;AACrB;;;;;CAKC,GACD,SAAS,cAAc,MAAM;IACzB,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,UAAU,EAAE,OAAO,UAAU,GAAG,OAAO,UAAU;AACvF;AACA;;;;CAIC,GACD,SAAS,SAAS,WAAW;IACzB,OAAO,OAAO,IAAI,CAAC;AACvB,EACA,kCAAkC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/crypto/crypto.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2019 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/* global window */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createCrypto = createCrypto;\nexports.hasBrowserCrypto = hasBrowserCrypto;\nconst crypto_1 = require(\"./browser/crypto\");\nconst crypto_2 = require(\"./node/crypto\");\n__exportStar(require(\"./shared\"), exports);\n// Crypto interface will provide required crypto functions.\n// Use `createCrypto()` factory function to create an instance\n// of Crypto. It will either use Node.js `crypto` module, or\n// use browser's SubtleCrypto interface. Since most of the\n// SubtleCrypto methods return promises, we must make those\n// methods return promises here as well, even though in Node.js\n// they are synchronous.\nfunction createCrypto() {\n    if (hasBrowserCrypto()) {\n        return new crypto_1.BrowserCrypto();\n    }\n    return new crypto_2.NodeCrypto();\n}\nfunction hasBrowserCrypto() {\n    return (typeof window !== 'undefined' &&\n        typeof window.crypto !== 'undefined' &&\n        typeof window.crypto.subtle !== 'undefined');\n}\n//# sourceMappingURL=crypto.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,iBAAiB,GACjB,IAAI,kBAAkB,6DAAS,0DAAK,eAAe,IAAK,CAAC,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1F,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QACjF,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAC9D;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AACjC,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACtB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AAChB,CAAE;AACF,IAAI,eAAe,6DAAS,0DAAK,YAAY,IAAK,SAAS,CAAC,EAAE,QAAO;IACjE,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAS,IAAI,gBAAgB,UAAS,GAAG;AAC3H;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG;AACvB,QAAQ,gBAAgB,GAAG;AAC3B,MAAM;AACN,MAAM;AACN,wIAAkC;AAClC,2DAA2D;AAC3D,8DAA8D;AAC9D,4DAA4D;AAC5D,0DAA0D;AAC1D,2DAA2D;AAC3D,+DAA+D;AAC/D,wBAAwB;AACxB,SAAS;IACL,IAAI;;IAGJ,OAAO,IAAI,SAAS,UAAU;AAClC;AACA,SAAS;IACL,OAAQ,gBAAkB,eACtB,OAAO,OAAO,MAAM,KAAK,eACzB,OAAO,OAAO,MAAM,CAAC,MAAM,KAAK;AACxC,EACA,kCAAkC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/util.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2023 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.LRUCache = void 0;\nexports.snakeToCamel = snakeToCamel;\nexports.originalOrCamelOptions = originalOrCamelOptions;\nexports.removeUndefinedValuesInObject = removeUndefinedValuesInObject;\nexports.isValidFile = isValidFile;\nexports.getWellKnownCertificateConfigFileLocation = getWellKnownCertificateConfigFileLocation;\nconst fs = require(\"fs\");\nconst os = require(\"os\");\nconst path = require(\"path\");\nconst WELL_KNOWN_CERTIFICATE_CONFIG_FILE = 'certificate_config.json';\nconst CLOUDSDK_CONFIG_DIRECTORY = 'gcloud';\n/**\n * Returns the camel case of a provided string.\n *\n * @remarks\n *\n * Match any `_` and not `_` pair, then return the uppercase of the not `_`\n * character.\n *\n * @param str the string to convert\n * @returns the camelCase'd string\n */\nfunction snakeToCamel(str) {\n    return str.replace(/([_][^_])/g, match => match.slice(1).toUpperCase());\n}\n/**\n * Get the value of `obj[key]` or `obj[camelCaseKey]`, with a preference\n * for original, non-camelCase key.\n *\n * @param obj object to lookup a value in\n * @returns a `get` function for getting `obj[key || snakeKey]`, if available\n */\nfunction originalOrCamelOptions(obj) {\n    /**\n     *\n     * @param key an index of object, preferably snake_case\n     * @returns the value `obj[key || snakeKey]`, if available\n     */\n    function get(key) {\n        const o = (obj || {});\n        return o[key] ?? o[snakeToCamel(key)];\n    }\n    return { get };\n}\n/**\n * A simple LRU cache utility.\n * Not meant for external usage.\n *\n * @experimental\n */\nclass LRUCache {\n    capacity;\n    /**\n     * Maps are in order. Thus, the older item is the first item.\n     *\n     * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map}\n     */\n    #cache = new Map();\n    maxAge;\n    constructor(options) {\n        this.capacity = options.capacity;\n        this.maxAge = options.maxAge;\n    }\n    /**\n     * Moves the key to the end of the cache.\n     *\n     * @param key the key to move\n     * @param value the value of the key\n     */\n    #moveToEnd(key, value) {\n        this.#cache.delete(key);\n        this.#cache.set(key, {\n            value,\n            lastAccessed: Date.now(),\n        });\n    }\n    /**\n     * Add an item to the cache.\n     *\n     * @param key the key to upsert\n     * @param value the value of the key\n     */\n    set(key, value) {\n        this.#moveToEnd(key, value);\n        this.#evict();\n    }\n    /**\n     * Get an item from the cache.\n     *\n     * @param key the key to retrieve\n     */\n    get(key) {\n        const item = this.#cache.get(key);\n        if (!item)\n            return;\n        this.#moveToEnd(key, item.value);\n        this.#evict();\n        return item.value;\n    }\n    /**\n     * Maintain the cache based on capacity and TTL.\n     */\n    #evict() {\n        const cutoffDate = this.maxAge ? Date.now() - this.maxAge : 0;\n        /**\n         * Because we know Maps are in order, this item is both the\n         * last item in the list (capacity) and oldest (maxAge).\n         */\n        let oldestItem = this.#cache.entries().next();\n        while (!oldestItem.done &&\n            (this.#cache.size > this.capacity || // too many\n                oldestItem.value[1].lastAccessed < cutoffDate) // too old\n        ) {\n            this.#cache.delete(oldestItem.value[0]);\n            oldestItem = this.#cache.entries().next();\n        }\n    }\n}\nexports.LRUCache = LRUCache;\n// Given and object remove fields where value is undefined.\nfunction removeUndefinedValuesInObject(object) {\n    Object.entries(object).forEach(([key, value]) => {\n        if (value === undefined || value === 'undefined') {\n            delete object[key];\n        }\n    });\n    return object;\n}\n/**\n * Helper to check if a path points to a valid file.\n */\nasync function isValidFile(filePath) {\n    try {\n        const stats = await fs.promises.lstat(filePath);\n        return stats.isFile();\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * Determines the well-known gcloud location for the certificate config file.\n * @returns The platform-specific path to the configuration file.\n * @internal\n */\nfunction getWellKnownCertificateConfigFileLocation() {\n    const configDir = process.env.CLOUDSDK_CONFIG ||\n        (_isWindows()\n            ? path.join(process.env.APPDATA || '', CLOUDSDK_CONFIG_DIRECTORY)\n            : path.join(process.env.HOME || '', '.config', CLOUDSDK_CONFIG_DIRECTORY));\n    return path.join(configDir, WELL_KNOWN_CERTIFICATE_CONFIG_FILE);\n}\n/**\n * Checks if the current operating system is Windows.\n * @returns True if the OS is Windows, false otherwise.\n * @internal\n */\nfunction _isWindows() {\n    return os.platform().startsWith('win');\n}\n//# sourceMappingURL=util.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,KAAK;AACxB,QAAQ,YAAY,GAAG;AACvB,QAAQ,sBAAsB,GAAG;AACjC,QAAQ,6BAA6B,GAAG;AACxC,QAAQ,WAAW,GAAG;AACtB,QAAQ,yCAAyC,GAAG;AACpD,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,qCAAqC;AAC3C,MAAM,4BAA4B;AAClC;;;;;;;;;;CAUC,GACD,SAAS,aAAa,GAAG;IACrB,OAAO,IAAI,OAAO,CAAC,cAAc,CAAA,QAAS,MAAM,KAAK,CAAC,GAAG,WAAW;AACxE;AACA;;;;;;CAMC,GACD,SAAS,uBAAuB,GAAG;IAC/B;;;;KAIC,GACD,SAAS,IAAI,GAAG;QACZ,MAAM,IAAK,OAAO,CAAC;QACnB,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,aAAa,KAAK;IACzC;IACA,OAAO;QAAE;IAAI;AACjB;AACA;;;;;CAKC,GACD,MAAM;IACF,SAAS;IACT;;;;KAIC,GACD,CAAA,KAAM,GAAG,IAAI,MAAM;IACnB,OAAO;IACP,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;QAChC,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;IAChC;IACA;;;;;KAKC,GACD,CAAA,SAAU,CAAC,GAAG,EAAE,KAAK;QACjB,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC;QACnB,IAAI,CAAC,CAAA,KAAM,CAAC,GAAG,CAAC,KAAK;YACjB;YACA,cAAc,KAAK,GAAG;QAC1B;IACJ;IACA;;;;;KAKC,GACD,IAAI,GAAG,EAAE,KAAK,EAAE;QACZ,IAAI,CAAC,CAAA,SAAU,CAAC,KAAK;QACrB,IAAI,CAAC,CAAA,KAAM;IACf;IACA;;;;KAIC,GACD,IAAI,GAAG,EAAE;QACL,MAAM,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,MACD;QACJ,IAAI,CAAC,CAAA,SAAU,CAAC,KAAK,KAAK,KAAK;QAC/B,IAAI,CAAC,CAAA,KAAM;QACX,OAAO,KAAK,KAAK;IACrB;IACA;;KAEC,GACD,CAAA,KAAM;QACF,MAAM,aAAa,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC,MAAM,GAAG;QAC5D;;;SAGC,GACD,IAAI,aAAa,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO,GAAG,IAAI;QAC3C,MAAO,CAAC,WAAW,IAAI,IACnB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,WAAW;QAC5C,WAAW,KAAK,CAAC,EAAE,CAAC,YAAY,GAAG,UAAU,EAAE,UAAU;SAC/D;YACE,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,WAAW,KAAK,CAAC,EAAE;YACtC,aAAa,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO,GAAG,IAAI;QAC3C;IACJ;AACJ;AACA,QAAQ,QAAQ,GAAG;AACnB,2DAA2D;AAC3D,SAAS,8BAA8B,MAAM;IACzC,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACxC,IAAI,UAAU,aAAa,UAAU,aAAa;YAC9C,OAAO,MAAM,CAAC,IAAI;QACtB;IACJ;IACA,OAAO;AACX;AACA;;CAEC,GACD,eAAe,YAAY,QAAQ;IAC/B,IAAI;QACA,MAAM,QAAQ,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;QACtC,OAAO,MAAM,MAAM;IACvB,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AACA;;;;CAIC,GACD,SAAS;IACL,MAAM,YAAY,QAAQ,GAAG,CAAC,eAAe,IACzC,CAAC,eACK,KAAK,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,IAAI,IAAI,6BACrC,KAAK,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,IAAI,WAAW,0BAA0B;IACjF,OAAO,KAAK,IAAI,CAAC,WAAW;AAChC;AACA;;;;CAIC,GACD,SAAS;IACL,OAAO,GAAG,QAAQ,GAAG,UAAU,CAAC;AACpC,EACA,gCAAgC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/shared.cjs"], "sourcesContent": ["\"use strict\";\n// Copyright 2023 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.USER_AGENT = exports.PRODUCT_NAME = exports.pkg = void 0;\nconst pkg = require('../../package.json');\nexports.pkg = pkg;\nconst PRODUCT_NAME = 'google-api-nodejs-client';\nexports.PRODUCT_NAME = PRODUCT_NAME;\nconst USER_AGENT = `${PRODUCT_NAME}/${pkg.version}`;\nexports.USER_AGENT = USER_AGENT;\n//# sourceMappingURL=shared.cjs.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,QAAQ,YAAY,GAAG,QAAQ,GAAG,GAAG,KAAK;AAC/D,MAAM;AACN,QAAQ,GAAG,GAAG;AACd,MAAM,eAAe;AACrB,QAAQ,YAAY,GAAG;AACvB,MAAM,aAAa,GAAG,aAAa,CAAC,EAAE,IAAI,OAAO,EAAE;AACnD,QAAQ,UAAU,GAAG,YACrB,mCAAmC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/authclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2012 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthClient = exports.DEFAULT_EAGER_REFRESH_THRESHOLD_MILLIS = exports.DEFAULT_UNIVERSE = void 0;\nconst events_1 = require(\"events\");\nconst gaxios_1 = require(\"gaxios\");\nconst util_1 = require(\"../util\");\nconst google_logging_utils_1 = require(\"google-logging-utils\");\nconst shared_cjs_1 = require(\"../shared.cjs\");\n/**\n * The default cloud universe\n *\n * @see {@link AuthJSONOptions.universe_domain}\n */\nexports.DEFAULT_UNIVERSE = 'googleapis.com';\n/**\n * The default {@link AuthClientOptions.eagerRefreshThresholdMillis}\n */\nexports.DEFAULT_EAGER_REFRESH_THRESHOLD_MILLIS = 5 * 60 * 1000;\n/**\n * The base of all Auth Clients.\n */\nclass AuthClient extends events_1.EventEmitter {\n    apiKey;\n    projectId;\n    /**\n     * The quota project ID. The quota project can be used by client libraries for the billing purpose.\n     * See {@link https://cloud.google.com/docs/quota Working with quotas}\n     */\n    quotaProjectId;\n    /**\n     * The {@link Gaxios `Gaxios`} instance used for making requests.\n     */\n    transporter;\n    credentials = {};\n    eagerRefreshThresholdMillis = exports.DEFAULT_EAGER_REFRESH_THRESHOLD_MILLIS;\n    forceRefreshOnFailure = false;\n    universeDomain = exports.DEFAULT_UNIVERSE;\n    /**\n     * Symbols that can be added to GaxiosOptions to specify the method name that is\n     * making an RPC call, for logging purposes, as well as a string ID that can be\n     * used to correlate calls and responses.\n     */\n    static RequestMethodNameSymbol = Symbol('request method name');\n    static RequestLogIdSymbol = Symbol('request log id');\n    constructor(opts = {}) {\n        super();\n        const options = (0, util_1.originalOrCamelOptions)(opts);\n        // Shared auth options\n        this.apiKey = opts.apiKey;\n        this.projectId = options.get('project_id') ?? null;\n        this.quotaProjectId = options.get('quota_project_id');\n        this.credentials = options.get('credentials') ?? {};\n        this.universeDomain = options.get('universe_domain') ?? exports.DEFAULT_UNIVERSE;\n        // Shared client options\n        this.transporter = opts.transporter ?? new gaxios_1.Gaxios(opts.transporterOptions);\n        if (options.get('useAuthRequestParameters') !== false) {\n            this.transporter.interceptors.request.add(AuthClient.DEFAULT_REQUEST_INTERCEPTOR);\n            this.transporter.interceptors.response.add(AuthClient.DEFAULT_RESPONSE_INTERCEPTOR);\n        }\n        if (opts.eagerRefreshThresholdMillis) {\n            this.eagerRefreshThresholdMillis = opts.eagerRefreshThresholdMillis;\n        }\n        this.forceRefreshOnFailure = opts.forceRefreshOnFailure ?? false;\n    }\n    /**\n     * A {@link fetch `fetch`} compliant API for {@link AuthClient}.\n     *\n     * @see {@link AuthClient.request} for the classic method.\n     *\n     * @remarks\n     *\n     * This is useful as a drop-in replacement for `fetch` API usage.\n     *\n     * @example\n     *\n     * ```ts\n     * const authClient = new AuthClient();\n     * const fetchWithAuthClient: typeof fetch = (...args) => authClient.fetch(...args);\n     * await fetchWithAuthClient('https://example.com');\n     * ```\n     *\n     * @param args `fetch` API or {@link Gaxios.fetch `Gaxios#fetch`} parameters\n     * @returns the {@link GaxiosResponse} with Gaxios-added properties\n     */\n    fetch(...args) {\n        // Up to 2 parameters in either overload\n        const input = args[0];\n        const init = args[1];\n        let url = undefined;\n        const headers = new Headers();\n        // prepare URL\n        if (typeof input === 'string') {\n            url = new URL(input);\n        }\n        else if (input instanceof URL) {\n            url = input;\n        }\n        else if (input && input.url) {\n            url = new URL(input.url);\n        }\n        // prepare headers\n        if (input && typeof input === 'object' && 'headers' in input) {\n            gaxios_1.Gaxios.mergeHeaders(headers, input.headers);\n        }\n        if (init) {\n            gaxios_1.Gaxios.mergeHeaders(headers, new Headers(init.headers));\n        }\n        // prepare request\n        if (typeof input === 'object' && !(input instanceof URL)) {\n            // input must have been a non-URL object\n            return this.request({ ...init, ...input, headers, url });\n        }\n        else {\n            // input must have been a string or URL\n            return this.request({ ...init, headers, url });\n        }\n    }\n    /**\n     * Sets the auth credentials.\n     */\n    setCredentials(credentials) {\n        this.credentials = credentials;\n    }\n    /**\n     * Append additional headers, e.g., x-goog-user-project, shared across the\n     * classes inheriting AuthClient. This method should be used by any method\n     * that overrides getRequestMetadataAsync(), which is a shared helper for\n     * setting request information in both gRPC and HTTP API calls.\n     *\n     * @param headers object to append additional headers to.\n     */\n    addSharedMetadataHeaders(headers) {\n        // quota_project_id, stored in application_default_credentials.json, is set in\n        // the x-goog-user-project header, to indicate an alternate account for\n        // billing and quota:\n        if (!headers.has('x-goog-user-project') && // don't override a value the user sets.\n            this.quotaProjectId) {\n            headers.set('x-goog-user-project', this.quotaProjectId);\n        }\n        return headers;\n    }\n    /**\n     * Adds the `x-goog-user-project` and `authorization` headers to the target Headers\n     * object, if they exist on the source.\n     *\n     * @param target the headers to target\n     * @param source the headers to source from\n     * @returns the target headers\n     */\n    addUserProjectAndAuthHeaders(target, source) {\n        const xGoogUserProject = source.get('x-goog-user-project');\n        const authorizationHeader = source.get('authorization');\n        if (xGoogUserProject) {\n            target.set('x-goog-user-project', xGoogUserProject);\n        }\n        if (authorizationHeader) {\n            target.set('authorization', authorizationHeader);\n        }\n        return target;\n    }\n    static log = (0, google_logging_utils_1.log)('auth');\n    static DEFAULT_REQUEST_INTERCEPTOR = {\n        resolved: async (config) => {\n            // Set `x-goog-api-client`, if not already set\n            if (!config.headers.has('x-goog-api-client')) {\n                const nodeVersion = process.version.replace(/^v/, '');\n                config.headers.set('x-goog-api-client', `gl-node/${nodeVersion}`);\n            }\n            // Set `User-Agent`\n            const userAgent = config.headers.get('User-Agent');\n            if (!userAgent) {\n                config.headers.set('User-Agent', shared_cjs_1.USER_AGENT);\n            }\n            else if (!userAgent.includes(`${shared_cjs_1.PRODUCT_NAME}/`)) {\n                config.headers.set('User-Agent', `${userAgent} ${shared_cjs_1.USER_AGENT}`);\n            }\n            try {\n                const symbols = config;\n                const methodName = symbols[AuthClient.RequestMethodNameSymbol];\n                // This doesn't need to be very unique or interesting, it's just an aid for\n                // matching requests to responses.\n                const logId = `${Math.floor(Math.random() * 1000)}`;\n                symbols[AuthClient.RequestLogIdSymbol] = logId;\n                // Boil down the object we're printing out.\n                const logObject = {\n                    url: config.url,\n                    headers: config.headers,\n                };\n                if (methodName) {\n                    AuthClient.log.info('%s [%s] request %j', methodName, logId, logObject);\n                }\n                else {\n                    AuthClient.log.info('[%s] request %j', logId, logObject);\n                }\n            }\n            catch (e) {\n                // Logging must not create new errors; swallow them all.\n            }\n            return config;\n        },\n    };\n    static DEFAULT_RESPONSE_INTERCEPTOR = {\n        resolved: async (response) => {\n            try {\n                const symbols = response.config;\n                const methodName = symbols[AuthClient.RequestMethodNameSymbol];\n                const logId = symbols[AuthClient.RequestLogIdSymbol];\n                if (methodName) {\n                    AuthClient.log.info('%s [%s] response %j', methodName, logId, response.data);\n                }\n                else {\n                    AuthClient.log.info('[%s] response %j', logId, response.data);\n                }\n            }\n            catch (e) {\n                // Logging must not create new errors; swallow them all.\n            }\n            return response;\n        },\n        rejected: async (error) => {\n            try {\n                const symbols = error.config;\n                const methodName = symbols[AuthClient.RequestMethodNameSymbol];\n                const logId = symbols[AuthClient.RequestLogIdSymbol];\n                if (methodName) {\n                    AuthClient.log.info('%s [%s] error %j', methodName, logId, error.response?.data);\n                }\n                else {\n                    AuthClient.log.error('[%s] error %j', logId, error.response?.data);\n                }\n            }\n            catch (e) {\n                // Logging must not create new errors; swallow them all.\n            }\n            // Re-throw the error.\n            throw error;\n        },\n    };\n    /**\n     * Sets the method name that is making a Gaxios request, so that logging may tag\n     * log lines with the operation.\n     * @param config A Gaxios request config\n     * @param methodName The method name making the call\n     */\n    static setMethodName(config, methodName) {\n        try {\n            const symbols = config;\n            symbols[AuthClient.RequestMethodNameSymbol] = methodName;\n        }\n        catch (e) {\n            // Logging must not create new errors; swallow them all.\n        }\n    }\n    /**\n     * Retry config for Auth-related requests.\n     *\n     * @remarks\n     *\n     * This is not a part of the default {@link AuthClient.transporter transporter/gaxios}\n     * config as some downstream APIs would prefer if customers explicitly enable retries,\n     * such as GCS.\n     */\n    static get RETRY_CONFIG() {\n        return {\n            retry: true,\n            retryConfig: {\n                httpMethodsToRetry: ['GET', 'PUT', 'POST', 'HEAD', 'OPTIONS', 'DELETE'],\n            },\n        };\n    }\n}\nexports.AuthClient = AuthClient;\n//# sourceMappingURL=authclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,QAAQ,sCAAsC,GAAG,QAAQ,gBAAgB,GAAG,KAAK;AACtG,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;;CAIC,GACD,QAAQ,gBAAgB,GAAG;AAC3B;;CAEC,GACD,QAAQ,sCAAsC,GAAG,IAAI,KAAK;AAC1D;;CAEC,GACD,MAAM,mBAAmB,SAAS,YAAY;IAC1C,OAAO;IACP,UAAU;IACV;;;KAGC,GACD,eAAe;IACf;;KAEC,GACD,YAAY;IACZ,cAAc,CAAC,EAAE;IACjB,8BAA8B,QAAQ,sCAAsC,CAAC;IAC7E,wBAAwB,MAAM;IAC9B,iBAAiB,QAAQ,gBAAgB,CAAC;IAC1C;;;;KAIC,GACD,OAAO,0BAA0B,OAAO,uBAAuB;IAC/D,OAAO,qBAAqB,OAAO,kBAAkB;IACrD,YAAY,OAAO,CAAC,CAAC,CAAE;QACnB,KAAK;QACL,MAAM,UAAU,CAAC,GAAG,OAAO,sBAAsB,EAAE;QACnD,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,iBAAiB;QAC9C,IAAI,CAAC,cAAc,GAAG,QAAQ,GAAG,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,QAAQ,GAAG,CAAC,kBAAkB,CAAC;QAClD,IAAI,CAAC,cAAc,GAAG,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,gBAAgB;QAChF,wBAAwB;QACxB,IAAI,CAAC,WAAW,GAAG,KAAK,WAAW,IAAI,IAAI,SAAS,MAAM,CAAC,KAAK,kBAAkB;QAClF,IAAI,QAAQ,GAAG,CAAC,gCAAgC,OAAO;YACnD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,2BAA2B;YAChF,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,4BAA4B;QACtF;QACA,IAAI,KAAK,2BAA2B,EAAE;YAClC,IAAI,CAAC,2BAA2B,GAAG,KAAK,2BAA2B;QACvE;QACA,IAAI,CAAC,qBAAqB,GAAG,KAAK,qBAAqB,IAAI;IAC/D;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,MAAM,GAAG,IAAI,EAAE;QACX,wCAAwC;QACxC,MAAM,QAAQ,IAAI,CAAC,EAAE;QACrB,MAAM,OAAO,IAAI,CAAC,EAAE;QACpB,IAAI,MAAM;QACV,MAAM,UAAU,IAAI;QACpB,cAAc;QACd,IAAI,OAAO,UAAU,UAAU;YAC3B,MAAM,IAAI,IAAI;QAClB,OACK,IAAI,iBAAiB,KAAK;YAC3B,MAAM;QACV,OACK,IAAI,SAAS,MAAM,GAAG,EAAE;YACzB,MAAM,IAAI,IAAI,MAAM,GAAG;QAC3B;QACA,kBAAkB;QAClB,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;YAC1D,SAAS,MAAM,CAAC,YAAY,CAAC,SAAS,MAAM,OAAO;QACvD;QACA,IAAI,MAAM;YACN,SAAS,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI,QAAQ,KAAK,OAAO;QAClE;QACA,kBAAkB;QAClB,IAAI,OAAO,UAAU,YAAY,CAAC,CAAC,iBAAiB,GAAG,GAAG;YACtD,wCAAwC;YACxC,OAAO,IAAI,CAAC,OAAO,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,KAAK;gBAAE;gBAAS;YAAI;QAC1D,OACK;YACD,uCAAuC;YACvC,OAAO,IAAI,CAAC,OAAO,CAAC;gBAAE,GAAG,IAAI;gBAAE;gBAAS;YAAI;QAChD;IACJ;IACA;;KAEC,GACD,eAAe,WAAW,EAAE;QACxB,IAAI,CAAC,WAAW,GAAG;IACvB;IACA;;;;;;;KAOC,GACD,yBAAyB,OAAO,EAAE;QAC9B,8EAA8E;QAC9E,uEAAuE;QACvE,qBAAqB;QACrB,IAAI,CAAC,QAAQ,GAAG,CAAC,0BAA0B,wCAAwC;QAC/E,IAAI,CAAC,cAAc,EAAE;YACrB,QAAQ,GAAG,CAAC,uBAAuB,IAAI,CAAC,cAAc;QAC1D;QACA,OAAO;IACX;IACA;;;;;;;KAOC,GACD,6BAA6B,MAAM,EAAE,MAAM,EAAE;QACzC,MAAM,mBAAmB,OAAO,GAAG,CAAC;QACpC,MAAM,sBAAsB,OAAO,GAAG,CAAC;QACvC,IAAI,kBAAkB;YAClB,OAAO,GAAG,CAAC,uBAAuB;QACtC;QACA,IAAI,qBAAqB;YACrB,OAAO,GAAG,CAAC,iBAAiB;QAChC;QACA,OAAO;IACX;IACA,OAAO,MAAM,CAAC,GAAG,uBAAuB,GAAG,EAAE,QAAQ;IACrD,OAAO,8BAA8B;QACjC,UAAU,OAAO;YACb,8CAA8C;YAC9C,IAAI,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,sBAAsB;gBAC1C,MAAM,cAAc,QAAQ,OAAO,CAAC,OAAO,CAAC,MAAM;gBAClD,OAAO,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,QAAQ,EAAE,aAAa;YACpE;YACA,mBAAmB;YACnB,MAAM,YAAY,OAAO,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,WAAW;gBACZ,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,aAAa,UAAU;YAC5D,OACK,IAAI,CAAC,UAAU,QAAQ,CAAC,GAAG,aAAa,YAAY,CAAC,CAAC,CAAC,GAAG;gBAC3D,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,UAAU,CAAC,EAAE,aAAa,UAAU,EAAE;YAC9E;YACA,IAAI;gBACA,MAAM,UAAU;gBAChB,MAAM,aAAa,OAAO,CAAC,WAAW,uBAAuB,CAAC;gBAC9D,2EAA2E;gBAC3E,kCAAkC;gBAClC,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBACnD,OAAO,CAAC,WAAW,kBAAkB,CAAC,GAAG;gBACzC,2CAA2C;gBAC3C,MAAM,YAAY;oBACd,KAAK,OAAO,GAAG;oBACf,SAAS,OAAO,OAAO;gBAC3B;gBACA,IAAI,YAAY;oBACZ,WAAW,GAAG,CAAC,IAAI,CAAC,sBAAsB,YAAY,OAAO;gBACjE,OACK;oBACD,WAAW,GAAG,CAAC,IAAI,CAAC,mBAAmB,OAAO;gBAClD;YACJ,EACA,OAAO,GAAG;YACN,wDAAwD;YAC5D;YACA,OAAO;QACX;IACJ,EAAE;IACF,OAAO,+BAA+B;QAClC,UAAU,OAAO;YACb,IAAI;gBACA,MAAM,UAAU,SAAS,MAAM;gBAC/B,MAAM,aAAa,OAAO,CAAC,WAAW,uBAAuB,CAAC;gBAC9D,MAAM,QAAQ,OAAO,CAAC,WAAW,kBAAkB,CAAC;gBACpD,IAAI,YAAY;oBACZ,WAAW,GAAG,CAAC,IAAI,CAAC,uBAAuB,YAAY,OAAO,SAAS,IAAI;gBAC/E,OACK;oBACD,WAAW,GAAG,CAAC,IAAI,CAAC,oBAAoB,OAAO,SAAS,IAAI;gBAChE;YACJ,EACA,OAAO,GAAG;YACN,wDAAwD;YAC5D;YACA,OAAO;QACX;QACA,UAAU,OAAO;YACb,IAAI;gBACA,MAAM,UAAU,MAAM,MAAM;gBAC5B,MAAM,aAAa,OAAO,CAAC,WAAW,uBAAuB,CAAC;gBAC9D,MAAM,QAAQ,OAAO,CAAC,WAAW,kBAAkB,CAAC;gBACpD,IAAI,YAAY;oBACZ,WAAW,GAAG,CAAC,IAAI,CAAC,oBAAoB,YAAY,OAAO,MAAM,QAAQ,EAAE;gBAC/E,OACK;oBACD,WAAW,GAAG,CAAC,KAAK,CAAC,iBAAiB,OAAO,MAAM,QAAQ,EAAE;gBACjE;YACJ,EACA,OAAO,GAAG;YACN,wDAAwD;YAC5D;YACA,sBAAsB;YACtB,MAAM;QACV;IACJ,EAAE;IACF;;;;;KAKC,GACD,OAAO,cAAc,MAAM,EAAE,UAAU,EAAE;QACrC,IAAI;YACA,MAAM,UAAU;YAChB,OAAO,CAAC,WAAW,uBAAuB,CAAC,GAAG;QAClD,EACA,OAAO,GAAG;QACN,wDAAwD;QAC5D;IACJ;IACA;;;;;;;;KAQC,GACD,WAAW,eAAe;QACtB,OAAO;YACH,OAAO;YACP,aAAa;gBACT,oBAAoB;oBAAC;oBAAO;oBAAO;oBAAQ;oBAAQ;oBAAW;iBAAS;YAC3E;QACJ;IACJ;AACJ;AACA,QAAQ,UAAU,GAAG,YACrB,sCAAsC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/loginticket.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2014 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.LoginTicket = void 0;\nclass LoginTicket {\n    envelope;\n    payload;\n    /**\n     * Create a simple class to extract user ID from an ID Token\n     *\n     * @param {string} env Envelope of the jwt\n     * @param {TokenPayload} pay Payload of the jwt\n     * @constructor\n     */\n    constructor(env, pay) {\n        this.envelope = env;\n        this.payload = pay;\n    }\n    getEnvelope() {\n        return this.envelope;\n    }\n    getPayload() {\n        return this.payload;\n    }\n    /**\n     * Create a simple class to extract user ID from an ID Token\n     *\n     * @return The user ID\n     */\n    getUserId() {\n        const payload = this.getPayload();\n        if (payload && payload.sub) {\n            return payload.sub;\n        }\n        return null;\n    }\n    /**\n     * Returns attributes from the login ticket.  This can contain\n     * various information about the user session.\n     *\n     * @return The envelope and payload\n     */\n    getAttributes() {\n        return { envelope: this.getEnvelope(), payload: this.getPayload() };\n    }\n}\nexports.LoginTicket = LoginTicket;\n//# sourceMappingURL=loginticket.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;IACF,SAAS;IACT,QAAQ;IACR;;;;;;KAMC,GACD,YAAY,GAAG,EAAE,GAAG,CAAE;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,cAAc;QACV,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,aAAa;QACT,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;;KAIC,GACD,YAAY;QACR,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAI,WAAW,QAAQ,GAAG,EAAE;YACxB,OAAO,QAAQ,GAAG;QACtB;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,gBAAgB;QACZ,OAAO;YAAE,UAAU,IAAI,CAAC,WAAW;YAAI,SAAS,IAAI,CAAC,UAAU;QAAG;IACtE;AACJ;AACA,QAAQ,WAAW,GAAG,aACtB,uCAAuC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/oauth2client.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2019 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.OAuth2Client = exports.ClientAuthentication = exports.CertificateFormat = exports.CodeChallengeMethod = void 0;\nconst gaxios_1 = require(\"gaxios\");\nconst querystring = require(\"querystring\");\nconst stream = require(\"stream\");\nconst formatEcdsa = require(\"ecdsa-sig-formatter\");\nconst util_1 = require(\"../util\");\nconst crypto_1 = require(\"../crypto/crypto\");\nconst authclient_1 = require(\"./authclient\");\nconst loginticket_1 = require(\"./loginticket\");\nvar CodeChallengeMethod;\n(function (CodeChallengeMethod) {\n    CodeChallengeMethod[\"Plain\"] = \"plain\";\n    CodeChallengeMethod[\"S256\"] = \"S256\";\n})(CodeChallengeMethod || (exports.CodeChallengeMethod = CodeChallengeMethod = {}));\nvar CertificateFormat;\n(function (CertificateFormat) {\n    CertificateFormat[\"PEM\"] = \"PEM\";\n    CertificateFormat[\"JWK\"] = \"JWK\";\n})(CertificateFormat || (exports.CertificateFormat = CertificateFormat = {}));\n/**\n * The client authentication type. Supported values are basic, post, and none.\n * https://datatracker.ietf.org/doc/html/rfc7591#section-2\n */\nvar ClientAuthentication;\n(function (ClientAuthentication) {\n    ClientAuthentication[\"ClientSecretPost\"] = \"ClientSecretPost\";\n    ClientAuthentication[\"ClientSecretBasic\"] = \"ClientSecretBasic\";\n    ClientAuthentication[\"None\"] = \"None\";\n})(ClientAuthentication || (exports.ClientAuthentication = ClientAuthentication = {}));\nclass OAuth2Client extends authclient_1.AuthClient {\n    redirectUri;\n    certificateCache = {};\n    certificateExpiry = null;\n    certificateCacheFormat = CertificateFormat.PEM;\n    refreshTokenPromises = new Map();\n    endpoints;\n    issuers;\n    clientAuthentication;\n    // TODO: refactor tests to make this private\n    _clientId;\n    // TODO: refactor tests to make this private\n    _clientSecret;\n    refreshHandler;\n    /**\n     * An OAuth2 Client for Google APIs.\n     *\n     * @param options The OAuth2 Client Options. Passing an `clientId` directly is **@DEPRECATED**.\n     * @param clientSecret **@DEPRECATED**. Provide a {@link OAuth2ClientOptions `OAuth2ClientOptions`} object in the first parameter instead.\n     * @param redirectUri **@DEPRECATED**. Provide a {@link OAuth2ClientOptions `OAuth2ClientOptions`} object in the first parameter instead.\n     */\n    constructor(options = {}, \n    /**\n     * @deprecated - provide a {@link OAuth2ClientOptions `OAuth2ClientOptions`} object in the first parameter instead\n     */\n    clientSecret, \n    /**\n     * @deprecated - provide a {@link OAuth2ClientOptions `OAuth2ClientOptions`} object in the first parameter instead\n     */\n    redirectUri) {\n        super(typeof options === 'object' ? options : {});\n        if (typeof options !== 'object') {\n            options = {\n                clientId: options,\n                clientSecret,\n                redirectUri,\n            };\n        }\n        this._clientId = options.clientId || options.client_id;\n        this._clientSecret = options.clientSecret || options.client_secret;\n        this.redirectUri = options.redirectUri || options.redirect_uris?.[0];\n        this.endpoints = {\n            tokenInfoUrl: 'https://oauth2.googleapis.com/tokeninfo',\n            oauth2AuthBaseUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n            oauth2TokenUrl: 'https://oauth2.googleapis.com/token',\n            oauth2RevokeUrl: 'https://oauth2.googleapis.com/revoke',\n            oauth2FederatedSignonPemCertsUrl: 'https://www.googleapis.com/oauth2/v1/certs',\n            oauth2FederatedSignonJwkCertsUrl: 'https://www.googleapis.com/oauth2/v3/certs',\n            oauth2IapPublicKeyUrl: 'https://www.gstatic.com/iap/verify/public_key',\n            ...options.endpoints,\n        };\n        this.clientAuthentication =\n            options.clientAuthentication || ClientAuthentication.ClientSecretPost;\n        this.issuers = options.issuers || [\n            'accounts.google.com',\n            'https://accounts.google.com',\n            this.universeDomain,\n        ];\n    }\n    /**\n     * @deprecated use instance's {@link OAuth2Client.endpoints}\n     */\n    static GOOGLE_TOKEN_INFO_URL = 'https://oauth2.googleapis.com/tokeninfo';\n    /**\n     * Clock skew - five minutes in seconds\n     */\n    static CLOCK_SKEW_SECS_ = 300;\n    /**\n     * The default max Token Lifetime is one day in seconds\n     */\n    static DEFAULT_MAX_TOKEN_LIFETIME_SECS_ = 86400;\n    /**\n     * Generates URL for consent page landing.\n     * @param opts Options.\n     * @return URL to consent page.\n     */\n    generateAuthUrl(opts = {}) {\n        if (opts.code_challenge_method && !opts.code_challenge) {\n            throw new Error('If a code_challenge_method is provided, code_challenge must be included.');\n        }\n        opts.response_type = opts.response_type || 'code';\n        opts.client_id = opts.client_id || this._clientId;\n        opts.redirect_uri = opts.redirect_uri || this.redirectUri;\n        // Allow scopes to be passed either as array or a string\n        if (Array.isArray(opts.scope)) {\n            opts.scope = opts.scope.join(' ');\n        }\n        const rootUrl = this.endpoints.oauth2AuthBaseUrl.toString();\n        return (rootUrl +\n            '?' +\n            querystring.stringify(opts));\n    }\n    generateCodeVerifier() {\n        // To make the code compatible with browser SubtleCrypto we need to make\n        // this method async.\n        throw new Error('generateCodeVerifier is removed, please use generateCodeVerifierAsync instead.');\n    }\n    /**\n     * Convenience method to automatically generate a code_verifier, and its\n     * resulting SHA256. If used, this must be paired with a S256\n     * code_challenge_method.\n     *\n     * For a full example see:\n     * https://github.com/googleapis/google-auth-library-nodejs/blob/main/samples/oauth2-codeVerifier.js\n     */\n    async generateCodeVerifierAsync() {\n        // base64 encoding uses 6 bits per character, and we want to generate128\n        // characters. 6*128/8 = 96.\n        const crypto = (0, crypto_1.createCrypto)();\n        const randomString = crypto.randomBytesBase64(96);\n        // The valid characters in the code_verifier are [A-Z]/[a-z]/[0-9]/\n        // \"-\"/\".\"/\"_\"/\"~\". Base64 encoded strings are pretty close, so we're just\n        // swapping out a few chars.\n        const codeVerifier = randomString\n            .replace(/\\+/g, '~')\n            .replace(/=/g, '_')\n            .replace(/\\//g, '-');\n        // Generate the base64 encoded SHA256\n        const unencodedCodeChallenge = await crypto.sha256DigestBase64(codeVerifier);\n        // We need to use base64UrlEncoding instead of standard base64\n        const codeChallenge = unencodedCodeChallenge\n            .split('=')[0]\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_');\n        return { codeVerifier, codeChallenge };\n    }\n    getToken(codeOrOptions, callback) {\n        const options = typeof codeOrOptions === 'string' ? { code: codeOrOptions } : codeOrOptions;\n        if (callback) {\n            this.getTokenAsync(options).then(r => callback(null, r.tokens, r.res), e => callback(e, null, e.response));\n        }\n        else {\n            return this.getTokenAsync(options);\n        }\n    }\n    async getTokenAsync(options) {\n        const url = this.endpoints.oauth2TokenUrl.toString();\n        const headers = new Headers();\n        const values = {\n            client_id: options.client_id || this._clientId,\n            code_verifier: options.codeVerifier,\n            code: options.code,\n            grant_type: 'authorization_code',\n            redirect_uri: options.redirect_uri || this.redirectUri,\n        };\n        if (this.clientAuthentication === ClientAuthentication.ClientSecretBasic) {\n            const basic = Buffer.from(`${this._clientId}:${this._clientSecret}`);\n            headers.set('authorization', `Basic ${basic.toString('base64')}`);\n        }\n        if (this.clientAuthentication === ClientAuthentication.ClientSecretPost) {\n            values.client_secret = this._clientSecret;\n        }\n        const opts = {\n            ...OAuth2Client.RETRY_CONFIG,\n            method: 'POST',\n            url,\n            data: new URLSearchParams((0, util_1.removeUndefinedValuesInObject)(values)),\n            headers,\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'getTokenAsync');\n        const res = await this.transporter.request(opts);\n        const tokens = res.data;\n        if (res.data && res.data.expires_in) {\n            tokens.expiry_date = new Date().getTime() + res.data.expires_in * 1000;\n            delete tokens.expires_in;\n        }\n        this.emit('tokens', tokens);\n        return { tokens, res };\n    }\n    /**\n     * Refreshes the access token.\n     * @param refresh_token Existing refresh token.\n     * @private\n     */\n    async refreshToken(refreshToken) {\n        if (!refreshToken) {\n            return this.refreshTokenNoCache(refreshToken);\n        }\n        // If a request to refresh using the same token has started,\n        // return the same promise.\n        if (this.refreshTokenPromises.has(refreshToken)) {\n            return this.refreshTokenPromises.get(refreshToken);\n        }\n        const p = this.refreshTokenNoCache(refreshToken).then(r => {\n            this.refreshTokenPromises.delete(refreshToken);\n            return r;\n        }, e => {\n            this.refreshTokenPromises.delete(refreshToken);\n            throw e;\n        });\n        this.refreshTokenPromises.set(refreshToken, p);\n        return p;\n    }\n    async refreshTokenNoCache(refreshToken) {\n        if (!refreshToken) {\n            throw new Error('No refresh token is set.');\n        }\n        const url = this.endpoints.oauth2TokenUrl.toString();\n        const data = {\n            refresh_token: refreshToken,\n            client_id: this._clientId,\n            client_secret: this._clientSecret,\n            grant_type: 'refresh_token',\n        };\n        let res;\n        try {\n            const opts = {\n                ...OAuth2Client.RETRY_CONFIG,\n                method: 'POST',\n                url,\n                data: new URLSearchParams((0, util_1.removeUndefinedValuesInObject)(data)),\n            };\n            authclient_1.AuthClient.setMethodName(opts, 'refreshTokenNoCache');\n            // request for new token\n            res = await this.transporter.request(opts);\n        }\n        catch (e) {\n            if (e instanceof gaxios_1.GaxiosError &&\n                e.message === 'invalid_grant' &&\n                e.response?.data &&\n                /ReAuth/i.test(e.response.data.error_description)) {\n                e.message = JSON.stringify(e.response.data);\n            }\n            throw e;\n        }\n        const tokens = res.data;\n        // TODO: de-duplicate this code from a few spots\n        if (res.data && res.data.expires_in) {\n            tokens.expiry_date = new Date().getTime() + res.data.expires_in * 1000;\n            delete tokens.expires_in;\n        }\n        this.emit('tokens', tokens);\n        return { tokens, res };\n    }\n    refreshAccessToken(callback) {\n        if (callback) {\n            this.refreshAccessTokenAsync().then(r => callback(null, r.credentials, r.res), callback);\n        }\n        else {\n            return this.refreshAccessTokenAsync();\n        }\n    }\n    async refreshAccessTokenAsync() {\n        const r = await this.refreshToken(this.credentials.refresh_token);\n        const tokens = r.tokens;\n        tokens.refresh_token = this.credentials.refresh_token;\n        this.credentials = tokens;\n        return { credentials: this.credentials, res: r.res };\n    }\n    getAccessToken(callback) {\n        if (callback) {\n            this.getAccessTokenAsync().then(r => callback(null, r.token, r.res), callback);\n        }\n        else {\n            return this.getAccessTokenAsync();\n        }\n    }\n    async getAccessTokenAsync() {\n        const shouldRefresh = !this.credentials.access_token || this.isTokenExpiring();\n        if (shouldRefresh) {\n            if (!this.credentials.refresh_token) {\n                if (this.refreshHandler) {\n                    const refreshedAccessToken = await this.processAndValidateRefreshHandler();\n                    if (refreshedAccessToken?.access_token) {\n                        this.setCredentials(refreshedAccessToken);\n                        return { token: this.credentials.access_token };\n                    }\n                }\n                else {\n                    throw new Error('No refresh token or refresh handler callback is set.');\n                }\n            }\n            const r = await this.refreshAccessTokenAsync();\n            if (!r.credentials || (r.credentials && !r.credentials.access_token)) {\n                throw new Error('Could not refresh access token.');\n            }\n            return { token: r.credentials.access_token, res: r.res };\n        }\n        else {\n            return { token: this.credentials.access_token };\n        }\n    }\n    /**\n     * The main authentication interface.  It takes an optional url which when\n     * present is the endpoint being accessed, and returns a Promise which\n     * resolves with authorization header fields.\n     *\n     * In OAuth2Client, the result has the form:\n     * { authorization: 'Bearer <access_token_value>' }\n     */\n    async getRequestHeaders(url) {\n        const headers = (await this.getRequestMetadataAsync(url)).headers;\n        return headers;\n    }\n    async getRequestMetadataAsync(url) {\n        url;\n        const thisCreds = this.credentials;\n        if (!thisCreds.access_token &&\n            !thisCreds.refresh_token &&\n            !this.apiKey &&\n            !this.refreshHandler) {\n            throw new Error('No access, refresh token, API key or refresh handler callback is set.');\n        }\n        if (thisCreds.access_token && !this.isTokenExpiring()) {\n            thisCreds.token_type = thisCreds.token_type || 'Bearer';\n            const headers = new Headers({\n                authorization: thisCreds.token_type + ' ' + thisCreds.access_token,\n            });\n            return { headers: this.addSharedMetadataHeaders(headers) };\n        }\n        // If refreshHandler exists, call processAndValidateRefreshHandler().\n        if (this.refreshHandler) {\n            const refreshedAccessToken = await this.processAndValidateRefreshHandler();\n            if (refreshedAccessToken?.access_token) {\n                this.setCredentials(refreshedAccessToken);\n                const headers = new Headers({\n                    authorization: 'Bearer ' + this.credentials.access_token,\n                });\n                return { headers: this.addSharedMetadataHeaders(headers) };\n            }\n        }\n        if (this.apiKey) {\n            return { headers: new Headers({ 'X-Goog-Api-Key': this.apiKey }) };\n        }\n        let r = null;\n        let tokens = null;\n        try {\n            r = await this.refreshToken(thisCreds.refresh_token);\n            tokens = r.tokens;\n        }\n        catch (err) {\n            const e = err;\n            if (e.response &&\n                (e.response.status === 403 || e.response.status === 404)) {\n                e.message = `Could not refresh access token: ${e.message}`;\n            }\n            throw e;\n        }\n        const credentials = this.credentials;\n        credentials.token_type = credentials.token_type || 'Bearer';\n        tokens.refresh_token = credentials.refresh_token;\n        this.credentials = tokens;\n        const headers = new Headers({\n            authorization: credentials.token_type + ' ' + tokens.access_token,\n        });\n        return { headers: this.addSharedMetadataHeaders(headers), res: r.res };\n    }\n    /**\n     * Generates an URL to revoke the given token.\n     * @param token The existing token to be revoked.\n     *\n     * @deprecated use instance method {@link OAuth2Client.getRevokeTokenURL}\n     */\n    static getRevokeTokenUrl(token) {\n        return new OAuth2Client().getRevokeTokenURL(token).toString();\n    }\n    /**\n     * Generates a URL to revoke the given token.\n     *\n     * @param token The existing token to be revoked.\n     */\n    getRevokeTokenURL(token) {\n        const url = new URL(this.endpoints.oauth2RevokeUrl);\n        url.searchParams.append('token', token);\n        return url;\n    }\n    revokeToken(token, callback) {\n        const opts = {\n            ...OAuth2Client.RETRY_CONFIG,\n            url: this.getRevokeTokenURL(token).toString(),\n            method: 'POST',\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'revokeToken');\n        if (callback) {\n            this.transporter\n                .request(opts)\n                .then(r => callback(null, r), callback);\n        }\n        else {\n            return this.transporter.request(opts);\n        }\n    }\n    revokeCredentials(callback) {\n        if (callback) {\n            this.revokeCredentialsAsync().then(res => callback(null, res), callback);\n        }\n        else {\n            return this.revokeCredentialsAsync();\n        }\n    }\n    async revokeCredentialsAsync() {\n        const token = this.credentials.access_token;\n        this.credentials = {};\n        if (token) {\n            return this.revokeToken(token);\n        }\n        else {\n            throw new Error('No access token to revoke.');\n        }\n    }\n    request(opts, callback) {\n        if (callback) {\n            this.requestAsync(opts).then(r => callback(null, r), e => {\n                return callback(e, e.response);\n            });\n        }\n        else {\n            return this.requestAsync(opts);\n        }\n    }\n    async requestAsync(opts, reAuthRetried = false) {\n        try {\n            const r = await this.getRequestMetadataAsync();\n            opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers);\n            this.addUserProjectAndAuthHeaders(opts.headers, r.headers);\n            if (this.apiKey) {\n                opts.headers.set('X-Goog-Api-Key', this.apiKey);\n            }\n            return await this.transporter.request(opts);\n        }\n        catch (e) {\n            const res = e.response;\n            if (res) {\n                const statusCode = res.status;\n                // Retry the request for metadata if the following criteria are true:\n                // - We haven't already retried.  It only makes sense to retry once.\n                // - The response was a 401 or a 403\n                // - The request didn't send a readableStream\n                // - An access_token and refresh_token were available, but either no\n                //   expiry_date was available or the forceRefreshOnFailure flag is set.\n                //   The absent expiry_date case can happen when developers stash the\n                //   access_token and refresh_token for later use, but the access_token\n                //   fails on the first try because it's expired. Some developers may\n                //   choose to enable forceRefreshOnFailure to mitigate time-related\n                //   errors.\n                // Or the following criteria are true:\n                // - We haven't already retried.  It only makes sense to retry once.\n                // - The response was a 401 or a 403\n                // - The request didn't send a readableStream\n                // - No refresh_token was available\n                // - An access_token and a refreshHandler callback were available, but\n                //   either no expiry_date was available or the forceRefreshOnFailure\n                //   flag is set. The access_token fails on the first try because it's\n                //   expired. Some developers may choose to enable forceRefreshOnFailure\n                //   to mitigate time-related errors.\n                const mayRequireRefresh = this.credentials &&\n                    this.credentials.access_token &&\n                    this.credentials.refresh_token &&\n                    (!this.credentials.expiry_date || this.forceRefreshOnFailure);\n                const mayRequireRefreshWithNoRefreshToken = this.credentials &&\n                    this.credentials.access_token &&\n                    !this.credentials.refresh_token &&\n                    (!this.credentials.expiry_date || this.forceRefreshOnFailure) &&\n                    this.refreshHandler;\n                const isReadableStream = res.config.data instanceof stream.Readable;\n                const isAuthErr = statusCode === 401 || statusCode === 403;\n                if (!reAuthRetried &&\n                    isAuthErr &&\n                    !isReadableStream &&\n                    mayRequireRefresh) {\n                    await this.refreshAccessTokenAsync();\n                    return this.requestAsync(opts, true);\n                }\n                else if (!reAuthRetried &&\n                    isAuthErr &&\n                    !isReadableStream &&\n                    mayRequireRefreshWithNoRefreshToken) {\n                    const refreshedAccessToken = await this.processAndValidateRefreshHandler();\n                    if (refreshedAccessToken?.access_token) {\n                        this.setCredentials(refreshedAccessToken);\n                    }\n                    return this.requestAsync(opts, true);\n                }\n            }\n            throw e;\n        }\n    }\n    verifyIdToken(options, callback) {\n        // This function used to accept two arguments instead of an options object.\n        // Check the types to help users upgrade with less pain.\n        // This check can be removed after a 2.0 release.\n        if (callback && typeof callback !== 'function') {\n            throw new Error('This method accepts an options object as the first parameter, which includes the idToken, audience, and maxExpiry.');\n        }\n        if (callback) {\n            this.verifyIdTokenAsync(options).then(r => callback(null, r), callback);\n        }\n        else {\n            return this.verifyIdTokenAsync(options);\n        }\n    }\n    async verifyIdTokenAsync(options) {\n        if (!options.idToken) {\n            throw new Error('The verifyIdToken method requires an ID Token');\n        }\n        const response = await this.getFederatedSignonCertsAsync();\n        const login = await this.verifySignedJwtWithCertsAsync(options.idToken, response.certs, options.audience, this.issuers, options.maxExpiry);\n        return login;\n    }\n    /**\n     * Obtains information about the provisioned access token.  Especially useful\n     * if you want to check the scopes that were provisioned to a given token.\n     *\n     * @param accessToken Required.  The Access Token for which you want to get\n     * user info.\n     */\n    async getTokenInfo(accessToken) {\n        const { data } = await this.transporter.request({\n            ...OAuth2Client.RETRY_CONFIG,\n            method: 'POST',\n            headers: {\n                'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',\n                authorization: `Bearer ${accessToken}`,\n            },\n            url: this.endpoints.tokenInfoUrl.toString(),\n        });\n        const info = Object.assign({\n            expiry_date: new Date().getTime() + data.expires_in * 1000,\n            scopes: data.scope.split(' '),\n        }, data);\n        delete info.expires_in;\n        delete info.scope;\n        return info;\n    }\n    getFederatedSignonCerts(callback) {\n        if (callback) {\n            this.getFederatedSignonCertsAsync().then(r => callback(null, r.certs, r.res), callback);\n        }\n        else {\n            return this.getFederatedSignonCertsAsync();\n        }\n    }\n    async getFederatedSignonCertsAsync() {\n        const nowTime = new Date().getTime();\n        const format = (0, crypto_1.hasBrowserCrypto)()\n            ? CertificateFormat.JWK\n            : CertificateFormat.PEM;\n        if (this.certificateExpiry &&\n            nowTime < this.certificateExpiry.getTime() &&\n            this.certificateCacheFormat === format) {\n            return { certs: this.certificateCache, format };\n        }\n        let res;\n        let url;\n        switch (format) {\n            case CertificateFormat.PEM:\n                url = this.endpoints.oauth2FederatedSignonPemCertsUrl.toString();\n                break;\n            case CertificateFormat.JWK:\n                url = this.endpoints.oauth2FederatedSignonJwkCertsUrl.toString();\n                break;\n            default:\n                throw new Error(`Unsupported certificate format ${format}`);\n        }\n        try {\n            const opts = {\n                ...OAuth2Client.RETRY_CONFIG,\n                url,\n            };\n            authclient_1.AuthClient.setMethodName(opts, 'getFederatedSignonCertsAsync');\n            res = await this.transporter.request(opts);\n        }\n        catch (e) {\n            if (e instanceof Error) {\n                e.message = `Failed to retrieve verification certificates: ${e.message}`;\n            }\n            throw e;\n        }\n        const cacheControl = res?.headers.get('cache-control');\n        let cacheAge = -1;\n        if (cacheControl) {\n            const maxAge = /max-age=(?<maxAge>[0-9]+)/.exec(cacheControl)?.groups\n                ?.maxAge;\n            if (maxAge) {\n                // Cache results with max-age (in seconds)\n                cacheAge = Number(maxAge) * 1000; // milliseconds\n            }\n        }\n        let certificates = {};\n        switch (format) {\n            case CertificateFormat.PEM:\n                certificates = res.data;\n                break;\n            case CertificateFormat.JWK:\n                for (const key of res.data.keys) {\n                    certificates[key.kid] = key;\n                }\n                break;\n            default:\n                throw new Error(`Unsupported certificate format ${format}`);\n        }\n        const now = new Date();\n        this.certificateExpiry =\n            cacheAge === -1 ? null : new Date(now.getTime() + cacheAge);\n        this.certificateCache = certificates;\n        this.certificateCacheFormat = format;\n        return { certs: certificates, format, res };\n    }\n    getIapPublicKeys(callback) {\n        if (callback) {\n            this.getIapPublicKeysAsync().then(r => callback(null, r.pubkeys, r.res), callback);\n        }\n        else {\n            return this.getIapPublicKeysAsync();\n        }\n    }\n    async getIapPublicKeysAsync() {\n        let res;\n        const url = this.endpoints.oauth2IapPublicKeyUrl.toString();\n        try {\n            const opts = {\n                ...OAuth2Client.RETRY_CONFIG,\n                url,\n            };\n            authclient_1.AuthClient.setMethodName(opts, 'getIapPublicKeysAsync');\n            res = await this.transporter.request(opts);\n        }\n        catch (e) {\n            if (e instanceof Error) {\n                e.message = `Failed to retrieve verification certificates: ${e.message}`;\n            }\n            throw e;\n        }\n        return { pubkeys: res.data, res };\n    }\n    verifySignedJwtWithCerts() {\n        // To make the code compatible with browser SubtleCrypto we need to make\n        // this method async.\n        throw new Error('verifySignedJwtWithCerts is removed, please use verifySignedJwtWithCertsAsync instead.');\n    }\n    /**\n     * Verify the id token is signed with the correct certificate\n     * and is from the correct audience.\n     * @param jwt The jwt to verify (The ID Token in this case).\n     * @param certs The array of certs to test the jwt against.\n     * @param requiredAudience The audience to test the jwt against.\n     * @param issuers The allowed issuers of the jwt (Optional).\n     * @param maxExpiry The max expiry the certificate can be (Optional).\n     * @return Returns a promise resolving to LoginTicket on verification.\n     */\n    async verifySignedJwtWithCertsAsync(jwt, certs, requiredAudience, issuers, maxExpiry) {\n        const crypto = (0, crypto_1.createCrypto)();\n        if (!maxExpiry) {\n            maxExpiry = OAuth2Client.DEFAULT_MAX_TOKEN_LIFETIME_SECS_;\n        }\n        const segments = jwt.split('.');\n        if (segments.length !== 3) {\n            throw new Error('Wrong number of segments in token: ' + jwt);\n        }\n        const signed = segments[0] + '.' + segments[1];\n        let signature = segments[2];\n        let envelope;\n        let payload;\n        try {\n            envelope = JSON.parse(crypto.decodeBase64StringUtf8(segments[0]));\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                err.message = `Can't parse token envelope: ${segments[0]}': ${err.message}`;\n            }\n            throw err;\n        }\n        if (!envelope) {\n            throw new Error(\"Can't parse token envelope: \" + segments[0]);\n        }\n        try {\n            payload = JSON.parse(crypto.decodeBase64StringUtf8(segments[1]));\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                err.message = `Can't parse token payload '${segments[0]}`;\n            }\n            throw err;\n        }\n        if (!payload) {\n            throw new Error(\"Can't parse token payload: \" + segments[1]);\n        }\n        if (!Object.prototype.hasOwnProperty.call(certs, envelope.kid)) {\n            // If this is not present, then there's no reason to attempt verification\n            throw new Error('No pem found for envelope: ' + JSON.stringify(envelope));\n        }\n        const cert = certs[envelope.kid];\n        if (envelope.alg === 'ES256') {\n            signature = formatEcdsa.joseToDer(signature, 'ES256').toString('base64');\n        }\n        const verified = await crypto.verify(cert, signed, signature);\n        if (!verified) {\n            throw new Error('Invalid token signature: ' + jwt);\n        }\n        if (!payload.iat) {\n            throw new Error('No issue time in token: ' + JSON.stringify(payload));\n        }\n        if (!payload.exp) {\n            throw new Error('No expiration time in token: ' + JSON.stringify(payload));\n        }\n        const iat = Number(payload.iat);\n        if (isNaN(iat))\n            throw new Error('iat field using invalid format');\n        const exp = Number(payload.exp);\n        if (isNaN(exp))\n            throw new Error('exp field using invalid format');\n        const now = new Date().getTime() / 1000;\n        if (exp >= now + maxExpiry) {\n            throw new Error('Expiration time too far in future: ' + JSON.stringify(payload));\n        }\n        const earliest = iat - OAuth2Client.CLOCK_SKEW_SECS_;\n        const latest = exp + OAuth2Client.CLOCK_SKEW_SECS_;\n        if (now < earliest) {\n            throw new Error('Token used too early, ' +\n                now +\n                ' < ' +\n                earliest +\n                ': ' +\n                JSON.stringify(payload));\n        }\n        if (now > latest) {\n            throw new Error('Token used too late, ' +\n                now +\n                ' > ' +\n                latest +\n                ': ' +\n                JSON.stringify(payload));\n        }\n        if (issuers && issuers.indexOf(payload.iss) < 0) {\n            throw new Error('Invalid issuer, expected one of [' +\n                issuers +\n                '], but got ' +\n                payload.iss);\n        }\n        // Check the audience matches if we have one\n        if (typeof requiredAudience !== 'undefined' && requiredAudience !== null) {\n            const aud = payload.aud;\n            let audVerified = false;\n            // If the requiredAudience is an array, check if it contains token\n            // audience\n            if (requiredAudience.constructor === Array) {\n                audVerified = requiredAudience.indexOf(aud) > -1;\n            }\n            else {\n                audVerified = aud === requiredAudience;\n            }\n            if (!audVerified) {\n                throw new Error('Wrong recipient, payload audience != requiredAudience');\n            }\n        }\n        return new loginticket_1.LoginTicket(envelope, payload);\n    }\n    /**\n     * Returns a promise that resolves with AccessTokenResponse type if\n     * refreshHandler is defined.\n     * If not, nothing is returned.\n     */\n    async processAndValidateRefreshHandler() {\n        if (this.refreshHandler) {\n            const accessTokenResponse = await this.refreshHandler();\n            if (!accessTokenResponse.access_token) {\n                throw new Error('No access token is returned by the refreshHandler callback.');\n            }\n            return accessTokenResponse;\n        }\n        return;\n    }\n    /**\n     * Returns true if a token is expired or will expire within\n     * eagerRefreshThresholdMillismilliseconds.\n     * If there is no expiry time, assumes the token is not expired or expiring.\n     */\n    isTokenExpiring() {\n        const expiryDate = this.credentials.expiry_date;\n        return expiryDate\n            ? expiryDate <= new Date().getTime() + this.eagerRefreshThresholdMillis\n            : false;\n    }\n}\nexports.OAuth2Client = OAuth2Client;\n//# sourceMappingURL=oauth2client.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,QAAQ,oBAAoB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,mBAAmB,GAAG,KAAK;AACrH,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,mBAAmB;IAC1B,mBAAmB,CAAC,QAAQ,GAAG;IAC/B,mBAAmB,CAAC,OAAO,GAAG;AAClC,CAAC,EAAE,uBAAuB,CAAC,QAAQ,mBAAmB,GAAG,sBAAsB,CAAC,CAAC;AACjF,IAAI;AACJ,CAAC,SAAU,iBAAiB;IACxB,iBAAiB,CAAC,MAAM,GAAG;IAC3B,iBAAiB,CAAC,MAAM,GAAG;AAC/B,CAAC,EAAE,qBAAqB,CAAC,QAAQ,iBAAiB,GAAG,oBAAoB,CAAC,CAAC;AAC3E;;;CAGC,GACD,IAAI;AACJ,CAAC,SAAU,oBAAoB;IAC3B,oBAAoB,CAAC,mBAAmB,GAAG;IAC3C,oBAAoB,CAAC,oBAAoB,GAAG;IAC5C,oBAAoB,CAAC,OAAO,GAAG;AACnC,CAAC,EAAE,wBAAwB,CAAC,QAAQ,oBAAoB,GAAG,uBAAuB,CAAC,CAAC;AACpF,MAAM,qBAAqB,aAAa,UAAU;IAC9C,YAAY;IACZ,mBAAmB,CAAC,EAAE;IACtB,oBAAoB,KAAK;IACzB,yBAAyB,kBAAkB,GAAG,CAAC;IAC/C,uBAAuB,IAAI,MAAM;IACjC,UAAU;IACV,QAAQ;IACR,qBAAqB;IACrB,4CAA4C;IAC5C,UAAU;IACV,4CAA4C;IAC5C,cAAc;IACd,eAAe;IACf;;;;;;KAMC,GACD,YAAY,UAAU,CAAC,CAAC,EACxB;;KAEC,GACD,YAAY,EACZ;;KAEC,GACD,WAAW,CAAE;QACT,KAAK,CAAC,OAAO,YAAY,WAAW,UAAU,CAAC;QAC/C,IAAI,OAAO,YAAY,UAAU;YAC7B,UAAU;gBACN,UAAU;gBACV;gBACA;YACJ;QACJ;QACA,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ,IAAI,QAAQ,SAAS;QACtD,IAAI,CAAC,aAAa,GAAG,QAAQ,YAAY,IAAI,QAAQ,aAAa;QAClE,IAAI,CAAC,WAAW,GAAG,QAAQ,WAAW,IAAI,QAAQ,aAAa,EAAE,CAAC,EAAE;QACpE,IAAI,CAAC,SAAS,GAAG;YACb,cAAc;YACd,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;YACjB,kCAAkC;YAClC,kCAAkC;YAClC,uBAAuB;YACvB,GAAG,QAAQ,SAAS;QACxB;QACA,IAAI,CAAC,oBAAoB,GACrB,QAAQ,oBAAoB,IAAI,qBAAqB,gBAAgB;QACzE,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO,IAAI;YAC9B;YACA;YACA,IAAI,CAAC,cAAc;SACtB;IACL;IACA;;KAEC,GACD,OAAO,wBAAwB,0CAA0C;IACzE;;KAEC,GACD,OAAO,mBAAmB,IAAI;IAC9B;;KAEC,GACD,OAAO,mCAAmC,MAAM;IAChD;;;;KAIC,GACD,gBAAgB,OAAO,CAAC,CAAC,EAAE;QACvB,IAAI,KAAK,qBAAqB,IAAI,CAAC,KAAK,cAAc,EAAE;YACpD,MAAM,IAAI,MAAM;QACpB;QACA,KAAK,aAAa,GAAG,KAAK,aAAa,IAAI;QAC3C,KAAK,SAAS,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS;QACjD,KAAK,YAAY,GAAG,KAAK,YAAY,IAAI,IAAI,CAAC,WAAW;QACzD,wDAAwD;QACxD,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,GAAG;YAC3B,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC;QACjC;QACA,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,QAAQ;QACzD,OAAQ,UACJ,MACA,YAAY,SAAS,CAAC;IAC9B;IACA,uBAAuB;QACnB,wEAAwE;QACxE,qBAAqB;QACrB,MAAM,IAAI,MAAM;IACpB;IACA;;;;;;;KAOC,GACD,MAAM,4BAA4B;QAC9B,wEAAwE;QACxE,4BAA4B;QAC5B,MAAM,SAAS,CAAC,GAAG,SAAS,YAAY;QACxC,MAAM,eAAe,OAAO,iBAAiB,CAAC;QAC9C,mEAAmE;QACnE,0EAA0E;QAC1E,4BAA4B;QAC5B,MAAM,eAAe,aAChB,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,MAAM,KACd,OAAO,CAAC,OAAO;QACpB,qCAAqC;QACrC,MAAM,yBAAyB,MAAM,OAAO,kBAAkB,CAAC;QAC/D,8DAA8D;QAC9D,MAAM,gBAAgB,uBACjB,KAAK,CAAC,IAAI,CAAC,EAAE,CACb,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO;QACpB,OAAO;YAAE;YAAc;QAAc;IACzC;IACA,SAAS,aAAa,EAAE,QAAQ,EAAE;QAC9B,MAAM,UAAU,OAAO,kBAAkB,WAAW;YAAE,MAAM;QAAc,IAAI;QAC9E,IAAI,UAAU;YACV,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,EAAE,MAAM,EAAE,EAAE,GAAG,GAAG,CAAA,IAAK,SAAS,GAAG,MAAM,EAAE,QAAQ;QAC5G,OACK;YACD,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B;IACJ;IACA,MAAM,cAAc,OAAO,EAAE;QACzB,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ;QAClD,MAAM,UAAU,IAAI;QACpB,MAAM,SAAS;YACX,WAAW,QAAQ,SAAS,IAAI,IAAI,CAAC,SAAS;YAC9C,eAAe,QAAQ,YAAY;YACnC,MAAM,QAAQ,IAAI;YAClB,YAAY;YACZ,cAAc,QAAQ,YAAY,IAAI,IAAI,CAAC,WAAW;QAC1D;QACA,IAAI,IAAI,CAAC,oBAAoB,KAAK,qBAAqB,iBAAiB,EAAE;YACtE,MAAM,QAAQ,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE;YACnE,QAAQ,GAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,QAAQ,CAAC,WAAW;QACpE;QACA,IAAI,IAAI,CAAC,oBAAoB,KAAK,qBAAqB,gBAAgB,EAAE;YACrE,OAAO,aAAa,GAAG,IAAI,CAAC,aAAa;QAC7C;QACA,MAAM,OAAO;YACT,GAAG,aAAa,YAAY;YAC5B,QAAQ;YACR;YACA,MAAM,IAAI,gBAAgB,CAAC,GAAG,OAAO,6BAA6B,EAAE;YACpE;QACJ;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,MAAM,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC3C,MAAM,SAAS,IAAI,IAAI;QACvB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjC,OAAO,WAAW,GAAG,IAAI,OAAO,OAAO,KAAK,IAAI,IAAI,CAAC,UAAU,GAAG;YAClE,OAAO,OAAO,UAAU;QAC5B;QACA,IAAI,CAAC,IAAI,CAAC,UAAU;QACpB,OAAO;YAAE;YAAQ;QAAI;IACzB;IACA;;;;KAIC,GACD,MAAM,aAAa,YAAY,EAAE;QAC7B,IAAI,CAAC,cAAc;YACf,OAAO,IAAI,CAAC,mBAAmB,CAAC;QACpC;QACA,4DAA4D;QAC5D,2BAA2B;QAC3B,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,eAAe;YAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC;QACzC;QACA,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,cAAc,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YACjC,OAAO;QACX,GAAG,CAAA;YACC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YACjC,MAAM;QACV;QACA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,cAAc;QAC5C,OAAO;IACX;IACA,MAAM,oBAAoB,YAAY,EAAE;QACpC,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ;QAClD,MAAM,OAAO;YACT,eAAe;YACf,WAAW,IAAI,CAAC,SAAS;YACzB,eAAe,IAAI,CAAC,aAAa;YACjC,YAAY;QAChB;QACA,IAAI;QACJ,IAAI;YACA,MAAM,OAAO;gBACT,GAAG,aAAa,YAAY;gBAC5B,QAAQ;gBACR;gBACA,MAAM,IAAI,gBAAgB,CAAC,GAAG,OAAO,6BAA6B,EAAE;YACxE;YACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;YAC5C,wBAAwB;YACxB,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QACzC,EACA,OAAO,GAAG;YACN,IAAI,aAAa,SAAS,WAAW,IACjC,EAAE,OAAO,KAAK,mBACd,EAAE,QAAQ,EAAE,QACZ,UAAU,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB,GAAG;gBACnD,EAAE,OAAO,GAAG,KAAK,SAAS,CAAC,EAAE,QAAQ,CAAC,IAAI;YAC9C;YACA,MAAM;QACV;QACA,MAAM,SAAS,IAAI,IAAI;QACvB,gDAAgD;QAChD,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjC,OAAO,WAAW,GAAG,IAAI,OAAO,OAAO,KAAK,IAAI,IAAI,CAAC,UAAU,GAAG;YAClE,OAAO,OAAO,UAAU;QAC5B;QACA,IAAI,CAAC,IAAI,CAAC,UAAU;QACpB,OAAO;YAAE;YAAQ;QAAI;IACzB;IACA,mBAAmB,QAAQ,EAAE;QACzB,IAAI,UAAU;YACV,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,EAAE,WAAW,EAAE,EAAE,GAAG,GAAG;QACnF,OACK;YACD,OAAO,IAAI,CAAC,uBAAuB;QACvC;IACJ;IACA,MAAM,0BAA0B;QAC5B,MAAM,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa;QAChE,MAAM,SAAS,EAAE,MAAM;QACvB,OAAO,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;QACrD,IAAI,CAAC,WAAW,GAAG;QACnB,OAAO;YAAE,aAAa,IAAI,CAAC,WAAW;YAAE,KAAK,EAAE,GAAG;QAAC;IACvD;IACA,eAAe,QAAQ,EAAE;QACrB,IAAI,UAAU;YACV,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG,GAAG;QACzE,OACK;YACD,OAAO,IAAI,CAAC,mBAAmB;QACnC;IACJ;IACA,MAAM,sBAAsB;QACxB,MAAM,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe;QAC5E,IAAI,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;gBACjC,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,MAAM,uBAAuB,MAAM,IAAI,CAAC,gCAAgC;oBACxE,IAAI,sBAAsB,cAAc;wBACpC,IAAI,CAAC,cAAc,CAAC;wBACpB,OAAO;4BAAE,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY;wBAAC;oBAClD;gBACJ,OACK;oBACD,MAAM,IAAI,MAAM;gBACpB;YACJ;YACA,MAAM,IAAI,MAAM,IAAI,CAAC,uBAAuB;YAC5C,IAAI,CAAC,EAAE,WAAW,IAAK,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,CAAC,YAAY,EAAG;gBAClE,MAAM,IAAI,MAAM;YACpB;YACA,OAAO;gBAAE,OAAO,EAAE,WAAW,CAAC,YAAY;gBAAE,KAAK,EAAE,GAAG;YAAC;QAC3D,OACK;YACD,OAAO;gBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY;YAAC;QAClD;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,kBAAkB,GAAG,EAAE;QACzB,MAAM,UAAU,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO;QACjE,OAAO;IACX;IACA,MAAM,wBAAwB,GAAG,EAAE;QAC/B;QACA,MAAM,YAAY,IAAI,CAAC,WAAW;QAClC,IAAI,CAAC,UAAU,YAAY,IACvB,CAAC,UAAU,aAAa,IACxB,CAAC,IAAI,CAAC,MAAM,IACZ,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,UAAU,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YACnD,UAAU,UAAU,GAAG,UAAU,UAAU,IAAI;YAC/C,MAAM,UAAU,IAAI,QAAQ;gBACxB,eAAe,UAAU,UAAU,GAAG,MAAM,UAAU,YAAY;YACtE;YACA,OAAO;gBAAE,SAAS,IAAI,CAAC,wBAAwB,CAAC;YAAS;QAC7D;QACA,qEAAqE;QACrE,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,uBAAuB,MAAM,IAAI,CAAC,gCAAgC;YACxE,IAAI,sBAAsB,cAAc;gBACpC,IAAI,CAAC,cAAc,CAAC;gBACpB,MAAM,UAAU,IAAI,QAAQ;oBACxB,eAAe,YAAY,IAAI,CAAC,WAAW,CAAC,YAAY;gBAC5D;gBACA,OAAO;oBAAE,SAAS,IAAI,CAAC,wBAAwB,CAAC;gBAAS;YAC7D;QACJ;QACA,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO;gBAAE,SAAS,IAAI,QAAQ;oBAAE,kBAAkB,IAAI,CAAC,MAAM;gBAAC;YAAG;QACrE;QACA,IAAI,IAAI;QACR,IAAI,SAAS;QACb,IAAI;YACA,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,aAAa;YACnD,SAAS,EAAE,MAAM;QACrB,EACA,OAAO,KAAK;YACR,MAAM,IAAI;YACV,IAAI,EAAE,QAAQ,IACV,CAAC,EAAE,QAAQ,CAAC,MAAM,KAAK,OAAO,EAAE,QAAQ,CAAC,MAAM,KAAK,GAAG,GAAG;gBAC1D,EAAE,OAAO,GAAG,CAAC,gCAAgC,EAAE,EAAE,OAAO,EAAE;YAC9D;YACA,MAAM;QACV;QACA,MAAM,cAAc,IAAI,CAAC,WAAW;QACpC,YAAY,UAAU,GAAG,YAAY,UAAU,IAAI;QACnD,OAAO,aAAa,GAAG,YAAY,aAAa;QAChD,IAAI,CAAC,WAAW,GAAG;QACnB,MAAM,UAAU,IAAI,QAAQ;YACxB,eAAe,YAAY,UAAU,GAAG,MAAM,OAAO,YAAY;QACrE;QACA,OAAO;YAAE,SAAS,IAAI,CAAC,wBAAwB,CAAC;YAAU,KAAK,EAAE,GAAG;QAAC;IACzE;IACA;;;;;KAKC,GACD,OAAO,kBAAkB,KAAK,EAAE;QAC5B,OAAO,IAAI,eAAe,iBAAiB,CAAC,OAAO,QAAQ;IAC/D;IACA;;;;KAIC,GACD,kBAAkB,KAAK,EAAE;QACrB,MAAM,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe;QAClD,IAAI,YAAY,CAAC,MAAM,CAAC,SAAS;QACjC,OAAO;IACX;IACA,YAAY,KAAK,EAAE,QAAQ,EAAE;QACzB,MAAM,OAAO;YACT,GAAG,aAAa,YAAY;YAC5B,KAAK,IAAI,CAAC,iBAAiB,CAAC,OAAO,QAAQ;YAC3C,QAAQ;QACZ;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,IAAI,UAAU;YACV,IAAI,CAAC,WAAW,CACX,OAAO,CAAC,MACR,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI;QACtC,OACK;YACD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QACpC;IACJ;IACA,kBAAkB,QAAQ,EAAE;QACxB,IAAI,UAAU;YACV,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAA,MAAO,SAAS,MAAM,MAAM;QACnE,OACK;YACD,OAAO,IAAI,CAAC,sBAAsB;QACtC;IACJ;IACA,MAAM,yBAAyB;QAC3B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,YAAY;QAC3C,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,OAAO;YACP,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B,OACK;YACD,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,QAAQ,IAAI,EAAE,QAAQ,EAAE;QACpB,IAAI,UAAU;YACV,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI,CAAA;gBACjD,OAAO,SAAS,GAAG,EAAE,QAAQ;YACjC;QACJ,OACK;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B;IACJ;IACA,MAAM,aAAa,IAAI,EAAE,gBAAgB,KAAK,EAAE;QAC5C,IAAI;YACA,MAAM,IAAI,MAAM,IAAI,CAAC,uBAAuB;YAC5C,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO;YACxD,IAAI,CAAC,4BAA4B,CAAC,KAAK,OAAO,EAAE,EAAE,OAAO;YACzD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,KAAK,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,MAAM;YAClD;YACA,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC1C,EACA,OAAO,GAAG;YACN,MAAM,MAAM,EAAE,QAAQ;YACtB,IAAI,KAAK;gBACL,MAAM,aAAa,IAAI,MAAM;gBAC7B,qEAAqE;gBACrE,oEAAoE;gBACpE,oCAAoC;gBACpC,6CAA6C;gBAC7C,oEAAoE;gBACpE,wEAAwE;gBACxE,qEAAqE;gBACrE,uEAAuE;gBACvE,qEAAqE;gBACrE,oEAAoE;gBACpE,YAAY;gBACZ,sCAAsC;gBACtC,oEAAoE;gBACpE,oCAAoC;gBACpC,6CAA6C;gBAC7C,mCAAmC;gBACnC,sEAAsE;gBACtE,qEAAqE;gBACrE,sEAAsE;gBACtE,wEAAwE;gBACxE,qCAAqC;gBACrC,MAAM,oBAAoB,IAAI,CAAC,WAAW,IACtC,IAAI,CAAC,WAAW,CAAC,YAAY,IAC7B,IAAI,CAAC,WAAW,CAAC,aAAa,IAC9B,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB;gBAChE,MAAM,sCAAsC,IAAI,CAAC,WAAW,IACxD,IAAI,CAAC,WAAW,CAAC,YAAY,IAC7B,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,IAC/B,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,KAC5D,IAAI,CAAC,cAAc;gBACvB,MAAM,mBAAmB,IAAI,MAAM,CAAC,IAAI,YAAY,OAAO,QAAQ;gBACnE,MAAM,YAAY,eAAe,OAAO,eAAe;gBACvD,IAAI,CAAC,iBACD,aACA,CAAC,oBACD,mBAAmB;oBACnB,MAAM,IAAI,CAAC,uBAAuB;oBAClC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;gBACnC,OACK,IAAI,CAAC,iBACN,aACA,CAAC,oBACD,qCAAqC;oBACrC,MAAM,uBAAuB,MAAM,IAAI,CAAC,gCAAgC;oBACxE,IAAI,sBAAsB,cAAc;wBACpC,IAAI,CAAC,cAAc,CAAC;oBACxB;oBACA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;gBACnC;YACJ;YACA,MAAM;QACV;IACJ;IACA,cAAc,OAAO,EAAE,QAAQ,EAAE;QAC7B,2EAA2E;QAC3E,wDAAwD;QACxD,iDAAiD;QACjD,IAAI,YAAY,OAAO,aAAa,YAAY;YAC5C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,UAAU;YACV,IAAI,CAAC,kBAAkB,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI;QAClE,OACK;YACD,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC;IACJ;IACA,MAAM,mBAAmB,OAAO,EAAE;QAC9B,IAAI,CAAC,QAAQ,OAAO,EAAE;YAClB,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,4BAA4B;QACxD,MAAM,QAAQ,MAAM,IAAI,CAAC,6BAA6B,CAAC,QAAQ,OAAO,EAAE,SAAS,KAAK,EAAE,QAAQ,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,SAAS;QACzI,OAAO;IACX;IACA;;;;;;KAMC,GACD,MAAM,aAAa,WAAW,EAAE;QAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAC5C,GAAG,aAAa,YAAY;YAC5B,QAAQ;YACR,SAAS;gBACL,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,aAAa;YAC1C;YACA,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ;QAC7C;QACA,MAAM,OAAO,OAAO,MAAM,CAAC;YACvB,aAAa,IAAI,OAAO,OAAO,KAAK,KAAK,UAAU,GAAG;YACtD,QAAQ,KAAK,KAAK,CAAC,KAAK,CAAC;QAC7B,GAAG;QACH,OAAO,KAAK,UAAU;QACtB,OAAO,KAAK,KAAK;QACjB,OAAO;IACX;IACA,wBAAwB,QAAQ,EAAE;QAC9B,IAAI,UAAU;YACV,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG,GAAG;QAClF,OACK;YACD,OAAO,IAAI,CAAC,4BAA4B;QAC5C;IACJ;IACA,MAAM,+BAA+B;QACjC,MAAM,UAAU,IAAI,OAAO,OAAO;QAClC,MAAM,SAAS,CAAC,GAAG,SAAS,gBAAgB,MACtC,kBAAkB,GAAG,GACrB,kBAAkB,GAAG;QAC3B,IAAI,IAAI,CAAC,iBAAiB,IACtB,UAAU,IAAI,CAAC,iBAAiB,CAAC,OAAO,MACxC,IAAI,CAAC,sBAAsB,KAAK,QAAQ;YACxC,OAAO;gBAAE,OAAO,IAAI,CAAC,gBAAgB;gBAAE;YAAO;QAClD;QACA,IAAI;QACJ,IAAI;QACJ,OAAQ;YACJ,KAAK,kBAAkB,GAAG;gBACtB,MAAM,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,QAAQ;gBAC9D;YACJ,KAAK,kBAAkB,GAAG;gBACtB,MAAM,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,QAAQ;gBAC9D;YACJ;gBACI,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,QAAQ;QAClE;QACA,IAAI;YACA,MAAM,OAAO;gBACT,GAAG,aAAa,YAAY;gBAC5B;YACJ;YACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;YAC5C,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QACzC,EACA,OAAO,GAAG;YACN,IAAI,aAAa,OAAO;gBACpB,EAAE,OAAO,GAAG,CAAC,8CAA8C,EAAE,EAAE,OAAO,EAAE;YAC5E;YACA,MAAM;QACV;QACA,MAAM,eAAe,KAAK,QAAQ,IAAI;QACtC,IAAI,WAAW,CAAC;QAChB,IAAI,cAAc;YACd,MAAM,SAAS,4BAA4B,IAAI,CAAC,eAAe,QACzD;YACN,IAAI,QAAQ;gBACR,0CAA0C;gBAC1C,WAAW,OAAO,UAAU,MAAM,eAAe;YACrD;QACJ;QACA,IAAI,eAAe,CAAC;QACpB,OAAQ;YACJ,KAAK,kBAAkB,GAAG;gBACtB,eAAe,IAAI,IAAI;gBACvB;YACJ,KAAK,kBAAkB,GAAG;gBACtB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,CAAE;oBAC7B,YAAY,CAAC,IAAI,GAAG,CAAC,GAAG;gBAC5B;gBACA;YACJ;gBACI,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,QAAQ;QAClE;QACA,MAAM,MAAM,IAAI;QAChB,IAAI,CAAC,iBAAiB,GAClB,aAAa,CAAC,IAAI,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK;QACtD,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,sBAAsB,GAAG;QAC9B,OAAO;YAAE,OAAO;YAAc;YAAQ;QAAI;IAC9C;IACA,iBAAiB,QAAQ,EAAE;QACvB,IAAI,UAAU;YACV,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,GAAG;QAC7E,OACK;YACD,OAAO,IAAI,CAAC,qBAAqB;QACrC;IACJ;IACA,MAAM,wBAAwB;QAC1B,IAAI;QACJ,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,QAAQ;QACzD,IAAI;YACA,MAAM,OAAO;gBACT,GAAG,aAAa,YAAY;gBAC5B;YACJ;YACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;YAC5C,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QACzC,EACA,OAAO,GAAG;YACN,IAAI,aAAa,OAAO;gBACpB,EAAE,OAAO,GAAG,CAAC,8CAA8C,EAAE,EAAE,OAAO,EAAE;YAC5E;YACA,MAAM;QACV;QACA,OAAO;YAAE,SAAS,IAAI,IAAI;YAAE;QAAI;IACpC;IACA,2BAA2B;QACvB,wEAAwE;QACxE,qBAAqB;QACrB,MAAM,IAAI,MAAM;IACpB;IACA;;;;;;;;;KASC,GACD,MAAM,8BAA8B,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE;QAClF,MAAM,SAAS,CAAC,GAAG,SAAS,YAAY;QACxC,IAAI,CAAC,WAAW;YACZ,YAAY,aAAa,gCAAgC;QAC7D;QACA,MAAM,WAAW,IAAI,KAAK,CAAC;QAC3B,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB,MAAM,IAAI,MAAM,wCAAwC;QAC5D;QACA,MAAM,SAAS,QAAQ,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAE;QAC9C,IAAI,YAAY,QAAQ,CAAC,EAAE;QAC3B,IAAI;QACJ,IAAI;QACJ,IAAI;YACA,WAAW,KAAK,KAAK,CAAC,OAAO,sBAAsB,CAAC,QAAQ,CAAC,EAAE;QACnE,EACA,OAAO,KAAK;YACR,IAAI,eAAe,OAAO;gBACtB,IAAI,OAAO,GAAG,CAAC,4BAA4B,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,OAAO,EAAE;YAC/E;YACA,MAAM;QACV;QACA,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,MAAM,iCAAiC,QAAQ,CAAC,EAAE;QAChE;QACA,IAAI;YACA,UAAU,KAAK,KAAK,CAAC,OAAO,sBAAsB,CAAC,QAAQ,CAAC,EAAE;QAClE,EACA,OAAO,KAAK;YACR,IAAI,eAAe,OAAO;gBACtB,IAAI,OAAO,GAAG,CAAC,2BAA2B,EAAE,QAAQ,CAAC,EAAE,EAAE;YAC7D;YACA,MAAM;QACV;QACA,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,MAAM,gCAAgC,QAAQ,CAAC,EAAE;QAC/D;QACA,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,SAAS,GAAG,GAAG;YAC5D,yEAAyE;YACzE,MAAM,IAAI,MAAM,gCAAgC,KAAK,SAAS,CAAC;QACnE;QACA,MAAM,OAAO,KAAK,CAAC,SAAS,GAAG,CAAC;QAChC,IAAI,SAAS,GAAG,KAAK,SAAS;YAC1B,YAAY,YAAY,SAAS,CAAC,WAAW,SAAS,QAAQ,CAAC;QACnE;QACA,MAAM,WAAW,MAAM,OAAO,MAAM,CAAC,MAAM,QAAQ;QACnD,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,MAAM,8BAA8B;QAClD;QACA,IAAI,CAAC,QAAQ,GAAG,EAAE;YACd,MAAM,IAAI,MAAM,6BAA6B,KAAK,SAAS,CAAC;QAChE;QACA,IAAI,CAAC,QAAQ,GAAG,EAAE;YACd,MAAM,IAAI,MAAM,kCAAkC,KAAK,SAAS,CAAC;QACrE;QACA,MAAM,MAAM,OAAO,QAAQ,GAAG;QAC9B,IAAI,MAAM,MACN,MAAM,IAAI,MAAM;QACpB,MAAM,MAAM,OAAO,QAAQ,GAAG;QAC9B,IAAI,MAAM,MACN,MAAM,IAAI,MAAM;QACpB,MAAM,MAAM,IAAI,OAAO,OAAO,KAAK;QACnC,IAAI,OAAO,MAAM,WAAW;YACxB,MAAM,IAAI,MAAM,wCAAwC,KAAK,SAAS,CAAC;QAC3E;QACA,MAAM,WAAW,MAAM,aAAa,gBAAgB;QACpD,MAAM,SAAS,MAAM,aAAa,gBAAgB;QAClD,IAAI,MAAM,UAAU;YAChB,MAAM,IAAI,MAAM,2BACZ,MACA,QACA,WACA,OACA,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,MAAM,QAAQ;YACd,MAAM,IAAI,MAAM,0BACZ,MACA,QACA,SACA,OACA,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,WAAW,QAAQ,OAAO,CAAC,QAAQ,GAAG,IAAI,GAAG;YAC7C,MAAM,IAAI,MAAM,sCACZ,UACA,gBACA,QAAQ,GAAG;QACnB;QACA,4CAA4C;QAC5C,IAAI,OAAO,qBAAqB,eAAe,qBAAqB,MAAM;YACtE,MAAM,MAAM,QAAQ,GAAG;YACvB,IAAI,cAAc;YAClB,kEAAkE;YAClE,WAAW;YACX,IAAI,iBAAiB,WAAW,KAAK,OAAO;gBACxC,cAAc,iBAAiB,OAAO,CAAC,OAAO,CAAC;YACnD,OACK;gBACD,cAAc,QAAQ;YAC1B;YACA,IAAI,CAAC,aAAa;gBACd,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,OAAO,IAAI,cAAc,WAAW,CAAC,UAAU;IACnD;IACA;;;;KAIC,GACD,MAAM,mCAAmC;QACrC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc;YACrD,IAAI,CAAC,oBAAoB,YAAY,EAAE;gBACnC,MAAM,IAAI,MAAM;YACpB;YACA,OAAO;QACX;QACA;IACJ;IACA;;;;KAIC,GACD,kBAAkB;QACd,MAAM,aAAa,IAAI,CAAC,WAAW,CAAC,WAAW;QAC/C,OAAO,aACD,cAAc,IAAI,OAAO,OAAO,KAAK,IAAI,CAAC,2BAA2B,GACrE;IACV;AACJ;AACA,QAAQ,YAAY,GAAG,cACvB,wCAAwC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/computeclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2013 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Compute = void 0;\nconst gaxios_1 = require(\"gaxios\");\nconst gcpMetadata = require(\"gcp-metadata\");\nconst oauth2client_1 = require(\"./oauth2client\");\nclass Compute extends oauth2client_1.OAuth2Client {\n    serviceAccountEmail;\n    scopes;\n    /**\n     * Google Compute Engine service account credentials.\n     *\n     * Retrieve access token from the metadata server.\n     * See: https://cloud.google.com/compute/docs/access/authenticate-workloads#applications\n     */\n    constructor(options = {}) {\n        super(options);\n        // Start with an expired refresh token, which will automatically be\n        // refreshed before the first API call is made.\n        this.credentials = { expiry_date: 1, refresh_token: 'compute-placeholder' };\n        this.serviceAccountEmail = options.serviceAccountEmail || 'default';\n        this.scopes = Array.isArray(options.scopes)\n            ? options.scopes\n            : options.scopes\n                ? [options.scopes]\n                : [];\n    }\n    /**\n     * Refreshes the access token.\n     * @param refreshToken Unused parameter\n     */\n    async refreshTokenNoCache() {\n        const tokenPath = `service-accounts/${this.serviceAccountEmail}/token`;\n        let data;\n        try {\n            const instanceOptions = {\n                property: tokenPath,\n            };\n            if (this.scopes.length > 0) {\n                instanceOptions.params = {\n                    scopes: this.scopes.join(','),\n                };\n            }\n            data = await gcpMetadata.instance(instanceOptions);\n        }\n        catch (e) {\n            if (e instanceof gaxios_1.GaxiosError) {\n                e.message = `Could not refresh access token: ${e.message}`;\n                this.wrapError(e);\n            }\n            throw e;\n        }\n        const tokens = data;\n        if (data && data.expires_in) {\n            tokens.expiry_date = new Date().getTime() + data.expires_in * 1000;\n            delete tokens.expires_in;\n        }\n        this.emit('tokens', tokens);\n        return { tokens, res: null };\n    }\n    /**\n     * Fetches an ID token.\n     * @param targetAudience the audience for the fetched ID token.\n     */\n    async fetchIdToken(targetAudience) {\n        const idTokenPath = `service-accounts/${this.serviceAccountEmail}/identity` +\n            `?format=full&audience=${targetAudience}`;\n        let idToken;\n        try {\n            const instanceOptions = {\n                property: idTokenPath,\n            };\n            idToken = await gcpMetadata.instance(instanceOptions);\n        }\n        catch (e) {\n            if (e instanceof Error) {\n                e.message = `Could not fetch ID token: ${e.message}`;\n            }\n            throw e;\n        }\n        return idToken;\n    }\n    wrapError(e) {\n        const res = e.response;\n        if (res && res.status) {\n            e.status = res.status;\n            if (res.status === 403) {\n                e.message =\n                    'A Forbidden error was returned while attempting to retrieve an access ' +\n                        'token for the Compute Engine built-in service account. This may be because the Compute ' +\n                        'Engine instance does not have the correct permission scopes specified: ' +\n                        e.message;\n            }\n            else if (res.status === 404) {\n                e.message =\n                    'A Not Found error was returned while attempting to retrieve an access' +\n                        'token for the Compute Engine built-in service account. This may be because the Compute ' +\n                        'Engine instance does not have any permission scopes specified: ' +\n                        e.message;\n            }\n        }\n    }\n}\nexports.Compute = Compute;\n//# sourceMappingURL=computeclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,KAAK;AACvB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,gBAAgB,eAAe,YAAY;IAC7C,oBAAoB;IACpB,OAAO;IACP;;;;;KAKC,GACD,YAAY,UAAU,CAAC,CAAC,CAAE;QACtB,KAAK,CAAC;QACN,mEAAmE;QACnE,+CAA+C;QAC/C,IAAI,CAAC,WAAW,GAAG;YAAE,aAAa;YAAG,eAAe;QAAsB;QAC1E,IAAI,CAAC,mBAAmB,GAAG,QAAQ,mBAAmB,IAAI;QAC1D,IAAI,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,MAAM,IACpC,QAAQ,MAAM,GACd,QAAQ,MAAM,GACV;YAAC,QAAQ,MAAM;SAAC,GAChB,EAAE;IAChB;IACA;;;KAGC,GACD,MAAM,sBAAsB;QACxB,MAAM,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QACtE,IAAI;QACJ,IAAI;YACA,MAAM,kBAAkB;gBACpB,UAAU;YACd;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;gBACxB,gBAAgB,MAAM,GAAG;oBACrB,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC7B;YACJ;YACA,OAAO,MAAM,YAAY,QAAQ,CAAC;QACtC,EACA,OAAO,GAAG;YACN,IAAI,aAAa,SAAS,WAAW,EAAE;gBACnC,EAAE,OAAO,GAAG,CAAC,gCAAgC,EAAE,EAAE,OAAO,EAAE;gBAC1D,IAAI,CAAC,SAAS,CAAC;YACnB;YACA,MAAM;QACV;QACA,MAAM,SAAS;QACf,IAAI,QAAQ,KAAK,UAAU,EAAE;YACzB,OAAO,WAAW,GAAG,IAAI,OAAO,OAAO,KAAK,KAAK,UAAU,GAAG;YAC9D,OAAO,OAAO,UAAU;QAC5B;QACA,IAAI,CAAC,IAAI,CAAC,UAAU;QACpB,OAAO;YAAE;YAAQ,KAAK;QAAK;IAC/B;IACA;;;KAGC,GACD,MAAM,aAAa,cAAc,EAAE;QAC/B,MAAM,cAAc,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,GACvE,CAAC,sBAAsB,EAAE,gBAAgB;QAC7C,IAAI;QACJ,IAAI;YACA,MAAM,kBAAkB;gBACpB,UAAU;YACd;YACA,UAAU,MAAM,YAAY,QAAQ,CAAC;QACzC,EACA,OAAO,GAAG;YACN,IAAI,aAAa,OAAO;gBACpB,EAAE,OAAO,GAAG,CAAC,0BAA0B,EAAE,EAAE,OAAO,EAAE;YACxD;YACA,MAAM;QACV;QACA,OAAO;IACX;IACA,UAAU,CAAC,EAAE;QACT,MAAM,MAAM,EAAE,QAAQ;QACtB,IAAI,OAAO,IAAI,MAAM,EAAE;YACnB,EAAE,MAAM,GAAG,IAAI,MAAM;YACrB,IAAI,IAAI,MAAM,KAAK,KAAK;gBACpB,EAAE,OAAO,GACL,2EACI,4FACA,4EACA,EAAE,OAAO;YACrB,OACK,IAAI,IAAI,MAAM,KAAK,KAAK;gBACzB,EAAE,OAAO,GACL,0EACI,4FACA,oEACA,EAAE,OAAO;YACrB;QACJ;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG,SAClB,yCAAyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1758, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/idtokenclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.IdTokenClient = void 0;\nconst oauth2client_1 = require(\"./oauth2client\");\nclass IdTokenClient extends oauth2client_1.OAuth2Client {\n    targetAudience;\n    idTokenProvider;\n    /**\n     * Google ID Token client\n     *\n     * Retrieve ID token from the metadata server.\n     * See: https://cloud.google.com/docs/authentication/get-id-token#metadata-server\n     */\n    constructor(options) {\n        super(options);\n        this.targetAudience = options.targetAudience;\n        this.idTokenProvider = options.idTokenProvider;\n    }\n    async getRequestMetadataAsync() {\n        if (!this.credentials.id_token ||\n            !this.credentials.expiry_date ||\n            this.isTokenExpiring()) {\n            const idToken = await this.idTokenProvider.fetchIdToken(this.targetAudience);\n            this.credentials = {\n                id_token: idToken,\n                expiry_date: this.getIdTokenExpiryDate(idToken),\n            };\n        }\n        const headers = new Headers({\n            authorization: 'Bearer ' + this.credentials.id_token,\n        });\n        return { headers };\n    }\n    getIdTokenExpiryDate(idToken) {\n        const payloadB64 = idToken.split('.')[1];\n        if (payloadB64) {\n            const payload = JSON.parse(Buffer.from(payloadB64, 'base64').toString('ascii'));\n            return payload.exp * 1000;\n        }\n    }\n}\nexports.IdTokenClient = IdTokenClient;\n//# sourceMappingURL=idtokenclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,MAAM,sBAAsB,eAAe,YAAY;IACnD,eAAe;IACf,gBAAgB;IAChB;;;;;KAKC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,cAAc,GAAG,QAAQ,cAAc;QAC5C,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe;IAClD;IACA,MAAM,0BAA0B;QAC5B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,IAC1B,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,IAC7B,IAAI,CAAC,eAAe,IAAI;YACxB,MAAM,UAAU,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc;YAC3E,IAAI,CAAC,WAAW,GAAG;gBACf,UAAU;gBACV,aAAa,IAAI,CAAC,oBAAoB,CAAC;YAC3C;QACJ;QACA,MAAM,UAAU,IAAI,QAAQ;YACxB,eAAe,YAAY,IAAI,CAAC,WAAW,CAAC,QAAQ;QACxD;QACA,OAAO;YAAE;QAAQ;IACrB;IACA,qBAAqB,OAAO,EAAE;QAC1B,MAAM,aAAa,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE;QACxC,IAAI,YAAY;YACZ,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,YAAY,UAAU,QAAQ,CAAC;YACtE,OAAO,QAAQ,GAAG,GAAG;QACzB;IACJ;AACJ;AACA,QAAQ,aAAa,GAAG,eACxB,yCAAyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/envDetect.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2018 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GCPEnv = void 0;\nexports.clear = clear;\nexports.getEnv = getEnv;\nconst gcpMetadata = require(\"gcp-metadata\");\nvar GCPEnv;\n(function (GCPEnv) {\n    GCPEnv[\"APP_ENGINE\"] = \"APP_ENGINE\";\n    GCPEnv[\"KUBERNETES_ENGINE\"] = \"KUBERNETES_ENGINE\";\n    GCPEnv[\"CLOUD_FUNCTIONS\"] = \"CLOUD_FUNCTIONS\";\n    GCPEnv[\"COMPUTE_ENGINE\"] = \"COMPUTE_ENGINE\";\n    GCPEnv[\"CLOUD_RUN\"] = \"CLOUD_RUN\";\n    GCPEnv[\"NONE\"] = \"NONE\";\n})(GCPEnv || (exports.GCPEnv = GCPEnv = {}));\nlet envPromise;\nfunction clear() {\n    envPromise = undefined;\n}\nasync function getEnv() {\n    if (envPromise) {\n        return envPromise;\n    }\n    envPromise = getEnvMemoized();\n    return envPromise;\n}\nasync function getEnvMemoized() {\n    let env = GCPEnv.NONE;\n    if (isAppEngine()) {\n        env = GCPEnv.APP_ENGINE;\n    }\n    else if (isCloudFunction()) {\n        env = GCPEnv.CLOUD_FUNCTIONS;\n    }\n    else if (await isComputeEngine()) {\n        if (await isKubernetesEngine()) {\n            env = GCPEnv.KUBERNETES_ENGINE;\n        }\n        else if (isCloudRun()) {\n            env = GCPEnv.CLOUD_RUN;\n        }\n        else {\n            env = GCPEnv.COMPUTE_ENGINE;\n        }\n    }\n    else {\n        env = GCPEnv.NONE;\n    }\n    return env;\n}\nfunction isAppEngine() {\n    return !!(process.env.GAE_SERVICE || process.env.GAE_MODULE_NAME);\n}\nfunction isCloudFunction() {\n    return !!(process.env.FUNCTION_NAME || process.env.FUNCTION_TARGET);\n}\n/**\n * This check only verifies that the environment is running knative.\n * This must be run *after* checking for Kubernetes, otherwise it will\n * return a false positive.\n */\nfunction isCloudRun() {\n    return !!process.env.K_CONFIGURATION;\n}\nasync function isKubernetesEngine() {\n    try {\n        await gcpMetadata.instance('attributes/cluster-name');\n        return true;\n    }\n    catch (e) {\n        return false;\n    }\n}\nasync function isComputeEngine() {\n    return gcpMetadata.isAvailable();\n}\n//# sourceMappingURL=envDetect.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,MAAM,GAAG,KAAK;AACtB,QAAQ,KAAK,GAAG;AAChB,QAAQ,MAAM,GAAG;AACjB,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,MAAM;IACb,MAAM,CAAC,aAAa,GAAG;IACvB,MAAM,CAAC,oBAAoB,GAAG;IAC9B,MAAM,CAAC,kBAAkB,GAAG;IAC5B,MAAM,CAAC,iBAAiB,GAAG;IAC3B,MAAM,CAAC,YAAY,GAAG;IACtB,MAAM,CAAC,OAAO,GAAG;AACrB,CAAC,EAAE,UAAU,CAAC,QAAQ,MAAM,GAAG,SAAS,CAAC,CAAC;AAC1C,IAAI;AACJ,SAAS;IACL,aAAa;AACjB;AACA,eAAe;IACX,IAAI,YAAY;QACZ,OAAO;IACX;IACA,aAAa;IACb,OAAO;AACX;AACA,eAAe;IACX,IAAI,MAAM,OAAO,IAAI;IACrB,IAAI,eAAe;QACf,MAAM,OAAO,UAAU;IAC3B,OACK,IAAI,mBAAmB;QACxB,MAAM,OAAO,eAAe;IAChC,OACK,IAAI,MAAM,mBAAmB;QAC9B,IAAI,MAAM,sBAAsB;YAC5B,MAAM,OAAO,iBAAiB;QAClC,OACK,IAAI,cAAc;YACnB,MAAM,OAAO,SAAS;QAC1B,OACK;YACD,MAAM,OAAO,cAAc;QAC/B;IACJ,OACK;QACD,MAAM,OAAO,IAAI;IACrB;IACA,OAAO;AACX;AACA,SAAS;IACL,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,WAAW,IAAI,QAAQ,GAAG,CAAC,eAAe;AACpE;AACA,SAAS;IACL,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,aAAa,IAAI,QAAQ,GAAG,CAAC,eAAe;AACtE;AACA;;;;CAIC,GACD,SAAS;IACL,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,eAAe;AACxC;AACA,eAAe;IACX,IAAI;QACA,MAAM,YAAY,QAAQ,CAAC;QAC3B,OAAO;IACX,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AACA,eAAe;IACX,OAAO,YAAY,WAAW;AAClC,EACA,qCAAqC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/jwtaccess.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2015 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.JWTAccess = void 0;\nconst jws = require(\"jws\");\nconst util_1 = require(\"../util\");\nconst DEFAULT_HEADER = {\n    alg: 'RS256',\n    typ: 'JWT',\n};\nclass JWTAccess {\n    email;\n    key;\n    keyId;\n    projectId;\n    eagerRefreshThresholdMillis;\n    cache = new util_1.LRUCache({\n        capacity: 500,\n        maxAge: 60 * 60 * 1000,\n    });\n    /**\n     * JWTAccess service account credentials.\n     *\n     * Create a new access token by using the credential to create a new JWT token\n     * that's recognized as the access token.\n     *\n     * @param email the service account email address.\n     * @param key the private key that will be used to sign the token.\n     * @param keyId the ID of the private key used to sign the token.\n     */\n    constructor(email, key, keyId, eagerRefreshThresholdMillis) {\n        this.email = email;\n        this.key = key;\n        this.keyId = keyId;\n        this.eagerRefreshThresholdMillis =\n            eagerRefreshThresholdMillis ?? 5 * 60 * 1000;\n    }\n    /**\n     * Ensures that we're caching a key appropriately, giving precedence to scopes vs. url\n     *\n     * @param url The URI being authorized.\n     * @param scopes The scope or scopes being authorized\n     * @returns A string that returns the cached key.\n     */\n    getCachedKey(url, scopes) {\n        let cacheKey = url;\n        if (scopes && Array.isArray(scopes) && scopes.length) {\n            cacheKey = url ? `${url}_${scopes.join('_')}` : `${scopes.join('_')}`;\n        }\n        else if (typeof scopes === 'string') {\n            cacheKey = url ? `${url}_${scopes}` : scopes;\n        }\n        if (!cacheKey) {\n            throw Error('Scopes or url must be provided');\n        }\n        return cacheKey;\n    }\n    /**\n     * Get a non-expired access token, after refreshing if necessary.\n     *\n     * @param url The URI being authorized.\n     * @param additionalClaims An object with a set of additional claims to\n     * include in the payload.\n     * @returns An object that includes the authorization header.\n     */\n    getRequestHeaders(url, additionalClaims, scopes) {\n        // Return cached authorization headers, unless we are within\n        // eagerRefreshThresholdMillis ms of them expiring:\n        const key = this.getCachedKey(url, scopes);\n        const cachedToken = this.cache.get(key);\n        const now = Date.now();\n        if (cachedToken &&\n            cachedToken.expiration - now > this.eagerRefreshThresholdMillis) {\n            // Copying headers into a new `Headers` object to avoid potential leakage -\n            // as this is a cache it is possible for multiple requests to reference this\n            // same value.\n            return new Headers(cachedToken.headers);\n        }\n        const iat = Math.floor(Date.now() / 1000);\n        const exp = JWTAccess.getExpirationTime(iat);\n        let defaultClaims;\n        // Turn scopes into space-separated string\n        if (Array.isArray(scopes)) {\n            scopes = scopes.join(' ');\n        }\n        // If scopes are specified, sign with scopes\n        if (scopes) {\n            defaultClaims = {\n                iss: this.email,\n                sub: this.email,\n                scope: scopes,\n                exp,\n                iat,\n            };\n        }\n        else {\n            defaultClaims = {\n                iss: this.email,\n                sub: this.email,\n                aud: url,\n                exp,\n                iat,\n            };\n        }\n        // if additionalClaims are provided, ensure they do not collide with\n        // other required claims.\n        if (additionalClaims) {\n            for (const claim in defaultClaims) {\n                if (additionalClaims[claim]) {\n                    throw new Error(`The '${claim}' property is not allowed when passing additionalClaims. This claim is included in the JWT by default.`);\n                }\n            }\n        }\n        const header = this.keyId\n            ? { ...DEFAULT_HEADER, kid: this.keyId }\n            : DEFAULT_HEADER;\n        const payload = Object.assign(defaultClaims, additionalClaims);\n        // Sign the jwt and add it to the cache\n        const signedJWT = jws.sign({ header, payload, secret: this.key });\n        const headers = new Headers({ authorization: `Bearer ${signedJWT}` });\n        this.cache.set(key, {\n            expiration: exp * 1000,\n            headers,\n        });\n        return headers;\n    }\n    /**\n     * Returns an expiration time for the JWT token.\n     *\n     * @param iat The issued at time for the JWT.\n     * @returns An expiration time for the JWT.\n     */\n    static getExpirationTime(iat) {\n        const exp = iat + 3600; // 3600 seconds = 1 hour\n        return exp;\n    }\n    /**\n     * Create a JWTAccess credentials instance using the given input options.\n     * @param json The input object.\n     */\n    fromJSON(json) {\n        if (!json) {\n            throw new Error('Must pass in a JSON object containing the service account auth settings.');\n        }\n        if (!json.client_email) {\n            throw new Error('The incoming JSON object does not contain a client_email field');\n        }\n        if (!json.private_key) {\n            throw new Error('The incoming JSON object does not contain a private_key field');\n        }\n        // Extract the relevant information from the json key file.\n        this.email = json.client_email;\n        this.key = json.private_key;\n        this.keyId = json.private_key_id;\n        this.projectId = json.project_id;\n    }\n    fromStream(inputStream, callback) {\n        if (callback) {\n            this.fromStreamAsync(inputStream).then(() => callback(), callback);\n        }\n        else {\n            return this.fromStreamAsync(inputStream);\n        }\n    }\n    fromStreamAsync(inputStream) {\n        return new Promise((resolve, reject) => {\n            if (!inputStream) {\n                reject(new Error('Must pass in a stream containing the service account auth settings.'));\n            }\n            let s = '';\n            inputStream\n                .setEncoding('utf8')\n                .on('data', chunk => (s += chunk))\n                .on('error', reject)\n                .on('end', () => {\n                try {\n                    const data = JSON.parse(s);\n                    this.fromJSON(data);\n                    resolve();\n                }\n                catch (err) {\n                    reject(err);\n                }\n            });\n        });\n    }\n}\nexports.JWTAccess = JWTAccess;\n//# sourceMappingURL=jwtaccess.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,MAAM,iBAAiB;IACnB,KAAK;IACL,KAAK;AACT;AACA,MAAM;IACF,MAAM;IACN,IAAI;IACJ,MAAM;IACN,UAAU;IACV,4BAA4B;IAC5B,QAAQ,IAAI,OAAO,QAAQ,CAAC;QACxB,UAAU;QACV,QAAQ,KAAK,KAAK;IACtB,GAAG;IACH;;;;;;;;;KASC,GACD,YAAY,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,2BAA2B,CAAE;QACxD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,2BAA2B,GAC5B,+BAA+B,IAAI,KAAK;IAChD;IACA;;;;;;KAMC,GACD,aAAa,GAAG,EAAE,MAAM,EAAE;QACtB,IAAI,WAAW;QACf,IAAI,UAAU,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,EAAE;YAClD,WAAW,MAAM,GAAG,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,IAAI,CAAC,MAAM;QACzE,OACK,IAAI,OAAO,WAAW,UAAU;YACjC,WAAW,MAAM,GAAG,IAAI,CAAC,EAAE,QAAQ,GAAG;QAC1C;QACA,IAAI,CAAC,UAAU;YACX,MAAM,MAAM;QAChB;QACA,OAAO;IACX;IACA;;;;;;;KAOC,GACD,kBAAkB,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE;QAC7C,4DAA4D;QAC5D,mDAAmD;QACnD,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK;QACnC,MAAM,cAAc,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACnC,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,eACA,YAAY,UAAU,GAAG,MAAM,IAAI,CAAC,2BAA2B,EAAE;YACjE,2EAA2E;YAC3E,4EAA4E;YAC5E,cAAc;YACd,OAAO,IAAI,QAAQ,YAAY,OAAO;QAC1C;QACA,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QACpC,MAAM,MAAM,UAAU,iBAAiB,CAAC;QACxC,IAAI;QACJ,0CAA0C;QAC1C,IAAI,MAAM,OAAO,CAAC,SAAS;YACvB,SAAS,OAAO,IAAI,CAAC;QACzB;QACA,4CAA4C;QAC5C,IAAI,QAAQ;YACR,gBAAgB;gBACZ,KAAK,IAAI,CAAC,KAAK;gBACf,KAAK,IAAI,CAAC,KAAK;gBACf,OAAO;gBACP;gBACA;YACJ;QACJ,OACK;YACD,gBAAgB;gBACZ,KAAK,IAAI,CAAC,KAAK;gBACf,KAAK,IAAI,CAAC,KAAK;gBACf,KAAK;gBACL;gBACA;YACJ;QACJ;QACA,oEAAoE;QACpE,yBAAyB;QACzB,IAAI,kBAAkB;YAClB,IAAK,MAAM,SAAS,cAAe;gBAC/B,IAAI,gBAAgB,CAAC,MAAM,EAAE;oBACzB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,MAAM,sGAAsG,CAAC;gBACzI;YACJ;QACJ;QACA,MAAM,SAAS,IAAI,CAAC,KAAK,GACnB;YAAE,GAAG,cAAc;YAAE,KAAK,IAAI,CAAC,KAAK;QAAC,IACrC;QACN,MAAM,UAAU,OAAO,MAAM,CAAC,eAAe;QAC7C,uCAAuC;QACvC,MAAM,YAAY,IAAI,IAAI,CAAC;YAAE;YAAQ;YAAS,QAAQ,IAAI,CAAC,GAAG;QAAC;QAC/D,MAAM,UAAU,IAAI,QAAQ;YAAE,eAAe,CAAC,OAAO,EAAE,WAAW;QAAC;QACnE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAChB,YAAY,MAAM;YAClB;QACJ;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,OAAO,kBAAkB,GAAG,EAAE;QAC1B,MAAM,MAAM,MAAM,MAAM,wBAAwB;QAChD,OAAO;IACX;IACA;;;KAGC,GACD,SAAS,IAAI,EAAE;QACX,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,YAAY,EAAE;YACpB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,WAAW,EAAE;YACnB,MAAM,IAAI,MAAM;QACpB;QACA,2DAA2D;QAC3D,IAAI,CAAC,KAAK,GAAG,KAAK,YAAY;QAC9B,IAAI,CAAC,GAAG,GAAG,KAAK,WAAW;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,cAAc;QAChC,IAAI,CAAC,SAAS,GAAG,KAAK,UAAU;IACpC;IACA,WAAW,WAAW,EAAE,QAAQ,EAAE;QAC9B,IAAI,UAAU;YACV,IAAI,CAAC,eAAe,CAAC,aAAa,IAAI,CAAC,IAAM,YAAY;QAC7D,OACK;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC;IACJ;IACA,gBAAgB,WAAW,EAAE;QACzB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,aAAa;gBACd,OAAO,IAAI,MAAM;YACrB;YACA,IAAI,IAAI;YACR,YACK,WAAW,CAAC,QACZ,EAAE,CAAC,QAAQ,CAAA,QAAU,KAAK,OAC1B,EAAE,CAAC,SAAS,QACZ,EAAE,CAAC,OAAO;gBACX,IAAI;oBACA,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,IAAI,CAAC,QAAQ,CAAC;oBACd;gBACJ,EACA,OAAO,KAAK;oBACR,OAAO;gBACX;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,SAAS,GAAG,WACpB,qCAAqC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/jwtclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2013 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.JWT = void 0;\nconst gtoken_1 = require(\"gtoken\");\nconst jwtaccess_1 = require(\"./jwtaccess\");\nconst oauth2client_1 = require(\"./oauth2client\");\nconst authclient_1 = require(\"./authclient\");\nclass JWT extends oauth2client_1.OAuth2Client {\n    email;\n    keyFile;\n    key;\n    keyId;\n    defaultScopes;\n    scopes;\n    scope;\n    subject;\n    gtoken;\n    additionalClaims;\n    useJWTAccessWithScope;\n    defaultServicePath;\n    access;\n    /**\n     * JWT service account credentials.\n     *\n     * Retrieve access token using gtoken.\n     *\n     * @param options the\n     */\n    constructor(options = {}) {\n        super(options);\n        this.email = options.email;\n        this.keyFile = options.keyFile;\n        this.key = options.key;\n        this.keyId = options.keyId;\n        this.scopes = options.scopes;\n        this.subject = options.subject;\n        this.additionalClaims = options.additionalClaims;\n        // Start with an expired refresh token, which will automatically be\n        // refreshed before the first API call is made.\n        this.credentials = { refresh_token: 'jwt-placeholder', expiry_date: 1 };\n    }\n    /**\n     * Creates a copy of the credential with the specified scopes.\n     * @param scopes List of requested scopes or a single scope.\n     * @return The cloned instance.\n     */\n    createScoped(scopes) {\n        const jwt = new JWT(this);\n        jwt.scopes = scopes;\n        return jwt;\n    }\n    /**\n     * Obtains the metadata to be sent with the request.\n     *\n     * @param url the URI being authorized.\n     */\n    async getRequestMetadataAsync(url) {\n        url = this.defaultServicePath ? `https://${this.defaultServicePath}/` : url;\n        const useSelfSignedJWT = (!this.hasUserScopes() && url) ||\n            (this.useJWTAccessWithScope && this.hasAnyScopes()) ||\n            this.universeDomain !== authclient_1.DEFAULT_UNIVERSE;\n        if (this.subject && this.universeDomain !== authclient_1.DEFAULT_UNIVERSE) {\n            throw new RangeError(`Service Account user is configured for the credential. Domain-wide delegation is not supported in universes other than ${authclient_1.DEFAULT_UNIVERSE}`);\n        }\n        if (!this.apiKey && useSelfSignedJWT) {\n            if (this.additionalClaims &&\n                this.additionalClaims.target_audience) {\n                const { tokens } = await this.refreshToken();\n                return {\n                    headers: this.addSharedMetadataHeaders(new Headers({\n                        authorization: `Bearer ${tokens.id_token}`,\n                    })),\n                };\n            }\n            else {\n                // no scopes have been set, but a uri has been provided. Use JWTAccess\n                // credentials.\n                if (!this.access) {\n                    this.access = new jwtaccess_1.JWTAccess(this.email, this.key, this.keyId, this.eagerRefreshThresholdMillis);\n                }\n                let scopes;\n                if (this.hasUserScopes()) {\n                    scopes = this.scopes;\n                }\n                else if (!url) {\n                    scopes = this.defaultScopes;\n                }\n                const useScopes = this.useJWTAccessWithScope ||\n                    this.universeDomain !== authclient_1.DEFAULT_UNIVERSE;\n                const headers = await this.access.getRequestHeaders(url ?? undefined, this.additionalClaims, \n                // Scopes take precedent over audience for signing,\n                // so we only provide them if `useJWTAccessWithScope` is on or\n                // if we are in a non-default universe\n                useScopes ? scopes : undefined);\n                return { headers: this.addSharedMetadataHeaders(headers) };\n            }\n        }\n        else if (this.hasAnyScopes() || this.apiKey) {\n            return super.getRequestMetadataAsync(url);\n        }\n        else {\n            // If no audience, apiKey, or scopes are provided, we should not attempt\n            // to populate any headers:\n            return { headers: new Headers() };\n        }\n    }\n    /**\n     * Fetches an ID token.\n     * @param targetAudience the audience for the fetched ID token.\n     */\n    async fetchIdToken(targetAudience) {\n        // Create a new gToken for fetching an ID token\n        const gtoken = new gtoken_1.GoogleToken({\n            iss: this.email,\n            sub: this.subject,\n            scope: this.scopes || this.defaultScopes,\n            keyFile: this.keyFile,\n            key: this.key,\n            additionalClaims: { target_audience: targetAudience },\n            transporter: this.transporter,\n        });\n        await gtoken.getToken({\n            forceRefresh: true,\n        });\n        if (!gtoken.idToken) {\n            throw new Error('Unknown error: Failed to fetch ID token');\n        }\n        return gtoken.idToken;\n    }\n    /**\n     * Determine if there are currently scopes available.\n     */\n    hasUserScopes() {\n        if (!this.scopes) {\n            return false;\n        }\n        return this.scopes.length > 0;\n    }\n    /**\n     * Are there any default or user scopes defined.\n     */\n    hasAnyScopes() {\n        if (this.scopes && this.scopes.length > 0)\n            return true;\n        if (this.defaultScopes && this.defaultScopes.length > 0)\n            return true;\n        return false;\n    }\n    authorize(callback) {\n        if (callback) {\n            this.authorizeAsync().then(r => callback(null, r), callback);\n        }\n        else {\n            return this.authorizeAsync();\n        }\n    }\n    async authorizeAsync() {\n        const result = await this.refreshToken();\n        if (!result) {\n            throw new Error('No result returned');\n        }\n        this.credentials = result.tokens;\n        this.credentials.refresh_token = 'jwt-placeholder';\n        this.key = this.gtoken.key;\n        this.email = this.gtoken.iss;\n        return result.tokens;\n    }\n    /**\n     * Refreshes the access token.\n     * @param refreshToken ignored\n     * @private\n     */\n    async refreshTokenNoCache() {\n        const gtoken = this.createGToken();\n        const token = await gtoken.getToken({\n            forceRefresh: this.isTokenExpiring(),\n        });\n        const tokens = {\n            access_token: token.access_token,\n            token_type: 'Bearer',\n            expiry_date: gtoken.expiresAt,\n            id_token: gtoken.idToken,\n        };\n        this.emit('tokens', tokens);\n        return { res: null, tokens };\n    }\n    /**\n     * Create a gToken if it doesn't already exist.\n     */\n    createGToken() {\n        if (!this.gtoken) {\n            this.gtoken = new gtoken_1.GoogleToken({\n                iss: this.email,\n                sub: this.subject,\n                scope: this.scopes || this.defaultScopes,\n                keyFile: this.keyFile,\n                key: this.key,\n                additionalClaims: this.additionalClaims,\n                transporter: this.transporter,\n            });\n        }\n        return this.gtoken;\n    }\n    /**\n     * Create a JWT credentials instance using the given input options.\n     * @param json The input object.\n     *\n     * @remarks\n     *\n     * **Important**: If you accept a credential configuration (credential JSON/File/Stream) from an external source for authentication to Google Cloud, you must validate it before providing it to any Google API or library. Providing an unvalidated credential configuration to Google APIs can compromise the security of your systems and data. For more information, refer to {@link https://cloud.google.com/docs/authentication/external/externally-sourced-credentials Validate credential configurations from external sources}.\n     */\n    fromJSON(json) {\n        if (!json) {\n            throw new Error('Must pass in a JSON object containing the service account auth settings.');\n        }\n        if (!json.client_email) {\n            throw new Error('The incoming JSON object does not contain a client_email field');\n        }\n        if (!json.private_key) {\n            throw new Error('The incoming JSON object does not contain a private_key field');\n        }\n        // Extract the relevant information from the json key file.\n        this.email = json.client_email;\n        this.key = json.private_key;\n        this.keyId = json.private_key_id;\n        this.projectId = json.project_id;\n        this.quotaProjectId = json.quota_project_id;\n        this.universeDomain = json.universe_domain || this.universeDomain;\n    }\n    fromStream(inputStream, callback) {\n        if (callback) {\n            this.fromStreamAsync(inputStream).then(() => callback(), callback);\n        }\n        else {\n            return this.fromStreamAsync(inputStream);\n        }\n    }\n    fromStreamAsync(inputStream) {\n        return new Promise((resolve, reject) => {\n            if (!inputStream) {\n                throw new Error('Must pass in a stream containing the service account auth settings.');\n            }\n            let s = '';\n            inputStream\n                .setEncoding('utf8')\n                .on('error', reject)\n                .on('data', chunk => (s += chunk))\n                .on('end', () => {\n                try {\n                    const data = JSON.parse(s);\n                    this.fromJSON(data);\n                    resolve();\n                }\n                catch (e) {\n                    reject(e);\n                }\n            });\n        });\n    }\n    /**\n     * Creates a JWT credentials instance using an API Key for authentication.\n     * @param apiKey The API Key in string form.\n     */\n    fromAPIKey(apiKey) {\n        if (typeof apiKey !== 'string') {\n            throw new Error('Must provide an API Key string.');\n        }\n        this.apiKey = apiKey;\n    }\n    /**\n     * Using the key or keyFile on the JWT client, obtain an object that contains\n     * the key and the client email.\n     */\n    async getCredentials() {\n        if (this.key) {\n            return { private_key: this.key, client_email: this.email };\n        }\n        else if (this.keyFile) {\n            const gtoken = this.createGToken();\n            const creds = await gtoken.getCredentials(this.keyFile);\n            return { private_key: creds.privateKey, client_email: creds.clientEmail };\n        }\n        throw new Error('A key or a keyFile must be provided to getCredentials.');\n    }\n}\nexports.JWT = JWT;\n//# sourceMappingURL=jwtclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,GAAG,GAAG,KAAK;AACnB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,YAAY,eAAe,YAAY;IACzC,MAAM;IACN,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,cAAc;IACd,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,iBAAiB;IACjB,sBAAsB;IACtB,mBAAmB;IACnB,OAAO;IACP;;;;;;KAMC,GACD,YAAY,UAAU,CAAC,CAAC,CAAE;QACtB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK;QAC1B,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO;QAC9B,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG;QACtB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK;QAC1B,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;QAC5B,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO;QAC9B,IAAI,CAAC,gBAAgB,GAAG,QAAQ,gBAAgB;QAChD,mEAAmE;QACnE,+CAA+C;QAC/C,IAAI,CAAC,WAAW,GAAG;YAAE,eAAe;YAAmB,aAAa;QAAE;IAC1E;IACA;;;;KAIC,GACD,aAAa,MAAM,EAAE;QACjB,MAAM,MAAM,IAAI,IAAI,IAAI;QACxB,IAAI,MAAM,GAAG;QACb,OAAO;IACX;IACA;;;;KAIC,GACD,MAAM,wBAAwB,GAAG,EAAE;QAC/B,MAAM,IAAI,CAAC,kBAAkB,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG;QACxE,MAAM,mBAAmB,AAAC,CAAC,IAAI,CAAC,aAAa,MAAM,OAC9C,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,YAAY,MAChD,IAAI,CAAC,cAAc,KAAK,aAAa,gBAAgB;QACzD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,gBAAgB,EAAE;YACvE,MAAM,IAAI,WAAW,CAAC,uHAAuH,EAAE,aAAa,gBAAgB,EAAE;QAClL;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,kBAAkB;YAClC,IAAI,IAAI,CAAC,gBAAgB,IACrB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE;gBACvC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,YAAY;gBAC1C,OAAO;oBACH,SAAS,IAAI,CAAC,wBAAwB,CAAC,IAAI,QAAQ;wBAC/C,eAAe,CAAC,OAAO,EAAE,OAAO,QAAQ,EAAE;oBAC9C;gBACJ;YACJ,OACK;gBACD,sEAAsE;gBACtE,eAAe;gBACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACd,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,2BAA2B;gBAC9G;gBACA,IAAI;gBACJ,IAAI,IAAI,CAAC,aAAa,IAAI;oBACtB,SAAS,IAAI,CAAC,MAAM;gBACxB,OACK,IAAI,CAAC,KAAK;oBACX,SAAS,IAAI,CAAC,aAAa;gBAC/B;gBACA,MAAM,YAAY,IAAI,CAAC,qBAAqB,IACxC,IAAI,CAAC,cAAc,KAAK,aAAa,gBAAgB;gBACzD,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,WAAW,IAAI,CAAC,gBAAgB,EAC3F,mDAAmD;gBACnD,8DAA8D;gBAC9D,sCAAsC;gBACtC,YAAY,SAAS;gBACrB,OAAO;oBAAE,SAAS,IAAI,CAAC,wBAAwB,CAAC;gBAAS;YAC7D;QACJ,OACK,IAAI,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,MAAM,EAAE;YACzC,OAAO,KAAK,CAAC,wBAAwB;QACzC,OACK;YACD,wEAAwE;YACxE,2BAA2B;YAC3B,OAAO;gBAAE,SAAS,IAAI;YAAU;QACpC;IACJ;IACA;;;KAGC,GACD,MAAM,aAAa,cAAc,EAAE;QAC/B,+CAA+C;QAC/C,MAAM,SAAS,IAAI,SAAS,WAAW,CAAC;YACpC,KAAK,IAAI,CAAC,KAAK;YACf,KAAK,IAAI,CAAC,OAAO;YACjB,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;YACxC,SAAS,IAAI,CAAC,OAAO;YACrB,KAAK,IAAI,CAAC,GAAG;YACb,kBAAkB;gBAAE,iBAAiB;YAAe;YACpD,aAAa,IAAI,CAAC,WAAW;QACjC;QACA,MAAM,OAAO,QAAQ,CAAC;YAClB,cAAc;QAClB;QACA,IAAI,CAAC,OAAO,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,OAAO,OAAO;IACzB;IACA;;KAEC,GACD,gBAAgB;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO;QACX;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;IAChC;IACA;;KAEC,GACD,eAAe;QACX,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GACpC,OAAO;QACX,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,GAClD,OAAO;QACX,OAAO;IACX;IACA,UAAU,QAAQ,EAAE;QAChB,IAAI,UAAU;YACV,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI;QACvD,OACK;YACD,OAAO,IAAI,CAAC,cAAc;QAC9B;IACJ;IACA,MAAM,iBAAiB;QACnB,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY;QACtC,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,WAAW,GAAG,OAAO,MAAM;QAChC,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG;QACjC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;QAC5B,OAAO,OAAO,MAAM;IACxB;IACA;;;;KAIC,GACD,MAAM,sBAAsB;QACxB,MAAM,SAAS,IAAI,CAAC,YAAY;QAChC,MAAM,QAAQ,MAAM,OAAO,QAAQ,CAAC;YAChC,cAAc,IAAI,CAAC,eAAe;QACtC;QACA,MAAM,SAAS;YACX,cAAc,MAAM,YAAY;YAChC,YAAY;YACZ,aAAa,OAAO,SAAS;YAC7B,UAAU,OAAO,OAAO;QAC5B;QACA,IAAI,CAAC,IAAI,CAAC,UAAU;QACpB,OAAO;YAAE,KAAK;YAAM;QAAO;IAC/B;IACA;;KAEC,GACD,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,WAAW,CAAC;gBACnC,KAAK,IAAI,CAAC,KAAK;gBACf,KAAK,IAAI,CAAC,OAAO;gBACjB,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;gBACxC,SAAS,IAAI,CAAC,OAAO;gBACrB,KAAK,IAAI,CAAC,GAAG;gBACb,kBAAkB,IAAI,CAAC,gBAAgB;gBACvC,aAAa,IAAI,CAAC,WAAW;YACjC;QACJ;QACA,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;;;;;;KAOC,GACD,SAAS,IAAI,EAAE;QACX,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,YAAY,EAAE;YACpB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,WAAW,EAAE;YACnB,MAAM,IAAI,MAAM;QACpB;QACA,2DAA2D;QAC3D,IAAI,CAAC,KAAK,GAAG,KAAK,YAAY;QAC9B,IAAI,CAAC,GAAG,GAAG,KAAK,WAAW;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,cAAc;QAChC,IAAI,CAAC,SAAS,GAAG,KAAK,UAAU;QAChC,IAAI,CAAC,cAAc,GAAG,KAAK,gBAAgB;QAC3C,IAAI,CAAC,cAAc,GAAG,KAAK,eAAe,IAAI,IAAI,CAAC,cAAc;IACrE;IACA,WAAW,WAAW,EAAE,QAAQ,EAAE;QAC9B,IAAI,UAAU;YACV,IAAI,CAAC,eAAe,CAAC,aAAa,IAAI,CAAC,IAAM,YAAY;QAC7D,OACK;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC;IACJ;IACA,gBAAgB,WAAW,EAAE;QACzB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,aAAa;gBACd,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,IAAI;YACR,YACK,WAAW,CAAC,QACZ,EAAE,CAAC,SAAS,QACZ,EAAE,CAAC,QAAQ,CAAA,QAAU,KAAK,OAC1B,EAAE,CAAC,OAAO;gBACX,IAAI;oBACA,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,IAAI,CAAC,QAAQ,CAAC;oBACd;gBACJ,EACA,OAAO,GAAG;oBACN,OAAO;gBACX;YACJ;QACJ;IACJ;IACA;;;KAGC,GACD,WAAW,MAAM,EAAE;QACf,IAAI,OAAO,WAAW,UAAU;YAC5B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;KAGC,GACD,MAAM,iBAAiB;QACnB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,OAAO;gBAAE,aAAa,IAAI,CAAC,GAAG;gBAAE,cAAc,IAAI,CAAC,KAAK;YAAC;QAC7D,OACK,IAAI,IAAI,CAAC,OAAO,EAAE;YACnB,MAAM,SAAS,IAAI,CAAC,YAAY;YAChC,MAAM,QAAQ,MAAM,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO;YACtD,OAAO;gBAAE,aAAa,MAAM,UAAU;gBAAE,cAAc,MAAM,WAAW;YAAC;QAC5E;QACA,MAAM,IAAI,MAAM;IACpB;AACJ;AACA,QAAQ,GAAG,GAAG,KACd,qCAAqC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2401, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/refreshclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2015 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UserRefreshClient = exports.USER_REFRESH_ACCOUNT_TYPE = void 0;\nconst oauth2client_1 = require(\"./oauth2client\");\nconst authclient_1 = require(\"./authclient\");\nexports.USER_REFRESH_ACCOUNT_TYPE = 'authorized_user';\nclass UserRefreshClient extends oauth2client_1.OAuth2Client {\n    // TODO: refactor tests to make this private\n    // In a future gts release, the _propertyName rule will be lifted.\n    // This is also a hard one because `this.refreshToken` is a function.\n    _refreshToken;\n    /**\n     * The User Refresh Token client.\n     *\n     * @param optionsOrClientId The User Refresh Token client options. Passing an `clientId` directly is **@DEPRECATED**.\n     * @param clientSecret **@DEPRECATED**. Provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead.\n     * @param refreshToken **@DEPRECATED**. Provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead.\n     * @param eagerRefreshThresholdMillis **@DEPRECATED**. Provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead.\n     * @param forceRefreshOnFailure **@DEPRECATED**. Provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead.\n     */\n    constructor(optionsOrClientId, \n    /**\n     * @deprecated - provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead\n     */\n    clientSecret, \n    /**\n     * @deprecated - provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead\n     */\n    refreshToken, \n    /**\n     * @deprecated - provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead\n     */\n    eagerRefreshThresholdMillis, \n    /**\n     * @deprecated - provide a {@link UserRefreshClientOptions `UserRefreshClientOptions`} object in the first parameter instead\n     */\n    forceRefreshOnFailure) {\n        const opts = optionsOrClientId && typeof optionsOrClientId === 'object'\n            ? optionsOrClientId\n            : {\n                clientId: optionsOrClientId,\n                clientSecret,\n                refreshToken,\n                eagerRefreshThresholdMillis,\n                forceRefreshOnFailure,\n            };\n        super(opts);\n        this._refreshToken = opts.refreshToken;\n        this.credentials.refresh_token = opts.refreshToken;\n    }\n    /**\n     * Refreshes the access token.\n     * @param refreshToken An ignored refreshToken..\n     * @param callback Optional callback.\n     */\n    async refreshTokenNoCache() {\n        return super.refreshTokenNoCache(this._refreshToken);\n    }\n    async fetchIdToken(targetAudience) {\n        const opts = {\n            ...UserRefreshClient.RETRY_CONFIG,\n            url: this.endpoints.oauth2TokenUrl,\n            method: 'POST',\n            data: new URLSearchParams({\n                client_id: this._clientId,\n                client_secret: this._clientSecret,\n                grant_type: 'refresh_token',\n                refresh_token: this._refreshToken,\n                target_audience: targetAudience,\n            }),\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'fetchIdToken');\n        const res = await this.transporter.request(opts);\n        return res.data.id_token;\n    }\n    /**\n     * Create a UserRefreshClient credentials instance using the given input\n     * options.\n     * @param json The input object.\n     */\n    fromJSON(json) {\n        if (!json) {\n            throw new Error('Must pass in a JSON object containing the user refresh token');\n        }\n        if (json.type !== 'authorized_user') {\n            throw new Error('The incoming JSON object does not have the \"authorized_user\" type');\n        }\n        if (!json.client_id) {\n            throw new Error('The incoming JSON object does not contain a client_id field');\n        }\n        if (!json.client_secret) {\n            throw new Error('The incoming JSON object does not contain a client_secret field');\n        }\n        if (!json.refresh_token) {\n            throw new Error('The incoming JSON object does not contain a refresh_token field');\n        }\n        this._clientId = json.client_id;\n        this._clientSecret = json.client_secret;\n        this._refreshToken = json.refresh_token;\n        this.credentials.refresh_token = json.refresh_token;\n        this.quotaProjectId = json.quota_project_id;\n        this.universeDomain = json.universe_domain || this.universeDomain;\n    }\n    fromStream(inputStream, callback) {\n        if (callback) {\n            this.fromStreamAsync(inputStream).then(() => callback(), callback);\n        }\n        else {\n            return this.fromStreamAsync(inputStream);\n        }\n    }\n    async fromStreamAsync(inputStream) {\n        return new Promise((resolve, reject) => {\n            if (!inputStream) {\n                return reject(new Error('Must pass in a stream containing the user refresh token.'));\n            }\n            let s = '';\n            inputStream\n                .setEncoding('utf8')\n                .on('error', reject)\n                .on('data', chunk => (s += chunk))\n                .on('end', () => {\n                try {\n                    const data = JSON.parse(s);\n                    this.fromJSON(data);\n                    return resolve();\n                }\n                catch (err) {\n                    return reject(err);\n                }\n            });\n        });\n    }\n    /**\n     * Create a UserRefreshClient credentials instance using the given input\n     * options.\n     * @param json The input object.\n     */\n    static fromJSON(json) {\n        const client = new UserRefreshClient();\n        client.fromJSON(json);\n        return client;\n    }\n}\nexports.UserRefreshClient = UserRefreshClient;\n//# sourceMappingURL=refreshclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,yBAAyB,GAAG,KAAK;AACrE,MAAM;AACN,MAAM;AACN,QAAQ,yBAAyB,GAAG;AACpC,MAAM,0BAA0B,eAAe,YAAY;IACvD,4CAA4C;IAC5C,kEAAkE;IAClE,qEAAqE;IACrE,cAAc;IACd;;;;;;;;KAQC,GACD,YAAY,iBAAiB,EAC7B;;KAEC,GACD,YAAY,EACZ;;KAEC,GACD,YAAY,EACZ;;KAEC,GACD,2BAA2B,EAC3B;;KAEC,GACD,qBAAqB,CAAE;QACnB,MAAM,OAAO,qBAAqB,OAAO,sBAAsB,WACzD,oBACA;YACE,UAAU;YACV;YACA;YACA;YACA;QACJ;QACJ,KAAK,CAAC;QACN,IAAI,CAAC,aAAa,GAAG,KAAK,YAAY;QACtC,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,KAAK,YAAY;IACtD;IACA;;;;KAIC,GACD,MAAM,sBAAsB;QACxB,OAAO,KAAK,CAAC,oBAAoB,IAAI,CAAC,aAAa;IACvD;IACA,MAAM,aAAa,cAAc,EAAE;QAC/B,MAAM,OAAO;YACT,GAAG,kBAAkB,YAAY;YACjC,KAAK,IAAI,CAAC,SAAS,CAAC,cAAc;YAClC,QAAQ;YACR,MAAM,IAAI,gBAAgB;gBACtB,WAAW,IAAI,CAAC,SAAS;gBACzB,eAAe,IAAI,CAAC,aAAa;gBACjC,YAAY;gBACZ,eAAe,IAAI,CAAC,aAAa;gBACjC,iBAAiB;YACrB;QACJ;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,MAAM,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC3C,OAAO,IAAI,IAAI,CAAC,QAAQ;IAC5B;IACA;;;;KAIC,GACD,SAAS,IAAI,EAAE;QACX,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACjC,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,aAAa,EAAE;YACrB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,aAAa,EAAE;YACrB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa;QACvC,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa;QACvC,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,KAAK,aAAa;QACnD,IAAI,CAAC,cAAc,GAAG,KAAK,gBAAgB;QAC3C,IAAI,CAAC,cAAc,GAAG,KAAK,eAAe,IAAI,IAAI,CAAC,cAAc;IACrE;IACA,WAAW,WAAW,EAAE,QAAQ,EAAE;QAC9B,IAAI,UAAU;YACV,IAAI,CAAC,eAAe,CAAC,aAAa,IAAI,CAAC,IAAM,YAAY;QAC7D,OACK;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC;IACJ;IACA,MAAM,gBAAgB,WAAW,EAAE;QAC/B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,aAAa;gBACd,OAAO,OAAO,IAAI,MAAM;YAC5B;YACA,IAAI,IAAI;YACR,YACK,WAAW,CAAC,QACZ,EAAE,CAAC,SAAS,QACZ,EAAE,CAAC,QAAQ,CAAA,QAAU,KAAK,OAC1B,EAAE,CAAC,OAAO;gBACX,IAAI;oBACA,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,IAAI,CAAC,QAAQ,CAAC;oBACd,OAAO;gBACX,EACA,OAAO,KAAK;oBACR,OAAO,OAAO;gBAClB;YACJ;QACJ;IACJ;IACA;;;;KAIC,GACD,OAAO,SAAS,IAAI,EAAE;QAClB,MAAM,SAAS,IAAI;QACnB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACX;AACJ;AACA,QAAQ,iBAAiB,GAAG,mBAC5B,yCAAyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/impersonated.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Impersonated = exports.IMPERSONATED_ACCOUNT_TYPE = void 0;\nconst oauth2client_1 = require(\"./oauth2client\");\nconst gaxios_1 = require(\"gaxios\");\nconst util_1 = require(\"../util\");\nexports.IMPERSONATED_ACCOUNT_TYPE = 'impersonated_service_account';\nclass Impersonated extends oauth2client_1.OAuth2Client {\n    sourceClient;\n    targetPrincipal;\n    targetScopes;\n    delegates;\n    lifetime;\n    endpoint;\n    /**\n     * Impersonated service account credentials.\n     *\n     * Create a new access token by impersonating another service account.\n     *\n     * Impersonated Credentials allowing credentials issued to a user or\n     * service account to impersonate another. The source project using\n     * Impersonated Credentials must enable the \"IAMCredentials\" API.\n     * Also, the target service account must grant the orginating principal\n     * the \"Service Account Token Creator\" IAM role.\n     *\n     * @param {object} options - The configuration object.\n     * @param {object} [options.sourceClient] the source credential used as to\n     * acquire the impersonated credentials.\n     * @param {string} [options.targetPrincipal] the service account to\n     * impersonate.\n     * @param {string[]} [options.delegates] the chained list of delegates\n     * required to grant the final access_token. If set, the sequence of\n     * identities must have \"Service Account Token Creator\" capability granted to\n     * the preceding identity. For example, if set to [serviceAccountB,\n     * serviceAccountC], the sourceCredential must have the Token Creator role on\n     * serviceAccountB. serviceAccountB must have the Token Creator on\n     * serviceAccountC. Finally, C must have Token Creator on target_principal.\n     * If left unset, sourceCredential must have that role on targetPrincipal.\n     * @param {string[]} [options.targetScopes] scopes to request during the\n     * authorization grant.\n     * @param {number} [options.lifetime] number of seconds the delegated\n     * credential should be valid for up to 3600 seconds by default, or 43,200\n     * seconds by extending the token's lifetime, see:\n     * https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials#sa-credentials-oauth\n     * @param {string} [options.endpoint] api endpoint override.\n     */\n    constructor(options = {}) {\n        super(options);\n        // Start with an expired refresh token, which will automatically be\n        // refreshed before the first API call is made.\n        this.credentials = {\n            expiry_date: 1,\n            refresh_token: 'impersonated-placeholder',\n        };\n        this.sourceClient = options.sourceClient ?? new oauth2client_1.OAuth2Client();\n        this.targetPrincipal = options.targetPrincipal ?? '';\n        this.delegates = options.delegates ?? [];\n        this.targetScopes = options.targetScopes ?? [];\n        this.lifetime = options.lifetime ?? 3600;\n        const usingExplicitUniverseDomain = !!(0, util_1.originalOrCamelOptions)(options).get('universe_domain');\n        if (!usingExplicitUniverseDomain) {\n            // override the default universe with the source's universe\n            this.universeDomain = this.sourceClient.universeDomain;\n        }\n        else if (this.sourceClient.universeDomain !== this.universeDomain) {\n            // non-default universe and is not matching the source - this could be a credential leak\n            throw new RangeError(`Universe domain ${this.sourceClient.universeDomain} in source credentials does not match ${this.universeDomain} universe domain set for impersonated credentials.`);\n        }\n        this.endpoint =\n            options.endpoint ?? `https://iamcredentials.${this.universeDomain}`;\n    }\n    /**\n     * Signs some bytes.\n     *\n     * {@link https://cloud.google.com/iam/docs/reference/credentials/rest/v1/projects.serviceAccounts/signBlob Reference Documentation}\n     * @param blobToSign String to sign.\n     *\n     * @returns A {@link SignBlobResponse} denoting the keyID and signedBlob in base64 string\n     */\n    async sign(blobToSign) {\n        await this.sourceClient.getAccessToken();\n        const name = `projects/-/serviceAccounts/${this.targetPrincipal}`;\n        const u = `${this.endpoint}/v1/${name}:signBlob`;\n        const body = {\n            delegates: this.delegates,\n            payload: Buffer.from(blobToSign).toString('base64'),\n        };\n        const res = await this.sourceClient.request({\n            ...Impersonated.RETRY_CONFIG,\n            url: u,\n            data: body,\n            method: 'POST',\n        });\n        return res.data;\n    }\n    /** The service account email to be impersonated. */\n    getTargetPrincipal() {\n        return this.targetPrincipal;\n    }\n    /**\n     * Refreshes the access token.\n     */\n    async refreshToken() {\n        try {\n            await this.sourceClient.getAccessToken();\n            const name = 'projects/-/serviceAccounts/' + this.targetPrincipal;\n            const u = `${this.endpoint}/v1/${name}:generateAccessToken`;\n            const body = {\n                delegates: this.delegates,\n                scope: this.targetScopes,\n                lifetime: this.lifetime + 's',\n            };\n            const res = await this.sourceClient.request({\n                ...Impersonated.RETRY_CONFIG,\n                url: u,\n                data: body,\n                method: 'POST',\n            });\n            const tokenResponse = res.data;\n            this.credentials.access_token = tokenResponse.accessToken;\n            this.credentials.expiry_date = Date.parse(tokenResponse.expireTime);\n            return {\n                tokens: this.credentials,\n                res,\n            };\n        }\n        catch (error) {\n            if (!(error instanceof Error))\n                throw error;\n            let status = 0;\n            let message = '';\n            if (error instanceof gaxios_1.GaxiosError) {\n                status = error?.response?.data?.error?.status;\n                message = error?.response?.data?.error?.message;\n            }\n            if (status && message) {\n                error.message = `${status}: unable to impersonate: ${message}`;\n                throw error;\n            }\n            else {\n                error.message = `unable to impersonate: ${error}`;\n                throw error;\n            }\n        }\n    }\n    /**\n     * Generates an OpenID Connect ID token for a service account.\n     *\n     * {@link https://cloud.google.com/iam/docs/reference/credentials/rest/v1/projects.serviceAccounts/generateIdToken Reference Documentation}\n     *\n     * @param targetAudience the audience for the fetched ID token.\n     * @param options the for the request\n     * @return an OpenID Connect ID token\n     */\n    async fetchIdToken(targetAudience, options) {\n        await this.sourceClient.getAccessToken();\n        const name = `projects/-/serviceAccounts/${this.targetPrincipal}`;\n        const u = `${this.endpoint}/v1/${name}:generateIdToken`;\n        const body = {\n            delegates: this.delegates,\n            audience: targetAudience,\n            includeEmail: options?.includeEmail ?? true,\n            useEmailAzp: options?.includeEmail ?? true,\n        };\n        const res = await this.sourceClient.request({\n            ...Impersonated.RETRY_CONFIG,\n            url: u,\n            data: body,\n            method: 'POST',\n        });\n        return res.data.token;\n    }\n}\nexports.Impersonated = Impersonated;\n//# sourceMappingURL=impersonated.js.map"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;CAcC,GACD,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,QAAQ,yBAAyB,GAAG,KAAK;AAChE,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,yBAAyB,GAAG;AACpC,MAAM,qBAAqB,eAAe,YAAY;IAClD,aAAa;IACb,gBAAgB;IAChB,aAAa;IACb,UAAU;IACV,SAAS;IACT,SAAS;IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BC,GACD,YAAY,UAAU,CAAC,CAAC,CAAE;QACtB,KAAK,CAAC;QACN,mEAAmE;QACnE,+CAA+C;QAC/C,IAAI,CAAC,WAAW,GAAG;YACf,aAAa;YACb,eAAe;QACnB;QACA,IAAI,CAAC,YAAY,GAAG,QAAQ,YAAY,IAAI,IAAI,eAAe,YAAY;QAC3E,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI;QAClD,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,IAAI,EAAE;QACxC,IAAI,CAAC,YAAY,GAAG,QAAQ,YAAY,IAAI,EAAE;QAC9C,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ,IAAI;QACpC,MAAM,8BAA8B,CAAC,CAAC,CAAC,GAAG,OAAO,sBAAsB,EAAE,SAAS,GAAG,CAAC;QACtF,IAAI,CAAC,6BAA6B;YAC9B,2DAA2D;YAC3D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc;QAC1D,OACK,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,IAAI,CAAC,cAAc,EAAE;YAC/D,wFAAwF;YACxF,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,sCAAsC,EAAE,IAAI,CAAC,cAAc,CAAC,kDAAkD,CAAC;QAC5L;QACA,IAAI,CAAC,QAAQ,GACT,QAAQ,QAAQ,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,cAAc,EAAE;IAC3E;IACA;;;;;;;KAOC,GACD,MAAM,KAAK,UAAU,EAAE;QACnB,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc;QACtC,MAAM,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,eAAe,EAAE;QACjE,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC;QAChD,MAAM,OAAO;YACT,WAAW,IAAI,CAAC,SAAS;YACzB,SAAS,OAAO,IAAI,CAAC,YAAY,QAAQ,CAAC;QAC9C;QACA,MAAM,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YACxC,GAAG,aAAa,YAAY;YAC5B,KAAK;YACL,MAAM;YACN,QAAQ;QACZ;QACA,OAAO,IAAI,IAAI;IACnB;IACA,kDAAkD,GAClD,qBAAqB;QACjB,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA;;KAEC,GACD,MAAM,eAAe;QACjB,IAAI;YACA,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc;YACtC,MAAM,OAAO,gCAAgC,IAAI,CAAC,eAAe;YACjE,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,oBAAoB,CAAC;YAC3D,MAAM,OAAO;gBACT,WAAW,IAAI,CAAC,SAAS;gBACzB,OAAO,IAAI,CAAC,YAAY;gBACxB,UAAU,IAAI,CAAC,QAAQ,GAAG;YAC9B;YACA,MAAM,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;gBACxC,GAAG,aAAa,YAAY;gBAC5B,KAAK;gBACL,MAAM;gBACN,QAAQ;YACZ;YACA,MAAM,gBAAgB,IAAI,IAAI;YAC9B,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,cAAc,WAAW;YACzD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC,cAAc,UAAU;YAClE,OAAO;gBACH,QAAQ,IAAI,CAAC,WAAW;gBACxB;YACJ;QACJ,EACA,OAAO,OAAO;YACV,IAAI,CAAC,CAAC,iBAAiB,KAAK,GACxB,MAAM;YACV,IAAI,SAAS;YACb,IAAI,UAAU;YACd,IAAI,iBAAiB,SAAS,WAAW,EAAE;gBACvC,SAAS,OAAO,UAAU,MAAM,OAAO;gBACvC,UAAU,OAAO,UAAU,MAAM,OAAO;YAC5C;YACA,IAAI,UAAU,SAAS;gBACnB,MAAM,OAAO,GAAG,GAAG,OAAO,yBAAyB,EAAE,SAAS;gBAC9D,MAAM;YACV,OACK;gBACD,MAAM,OAAO,GAAG,CAAC,uBAAuB,EAAE,OAAO;gBACjD,MAAM;YACV;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,MAAM,aAAa,cAAc,EAAE,OAAO,EAAE;QACxC,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc;QACtC,MAAM,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,eAAe,EAAE;QACjE,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,gBAAgB,CAAC;QACvD,MAAM,OAAO;YACT,WAAW,IAAI,CAAC,SAAS;YACzB,UAAU;YACV,cAAc,SAAS,gBAAgB;YACvC,aAAa,SAAS,gBAAgB;QAC1C;QACA,MAAM,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YACxC,GAAG,aAAa,YAAY;YAC5B,KAAK;YACL,MAAM;YACN,QAAQ;QACZ;QACA,OAAO,IAAI,IAAI,CAAC,KAAK;IACzB;AACJ;AACA,QAAQ,YAAY,GAAG,cACvB,wCAAwC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2731, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/oauth2common.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.OAuthClientAuthHandler = void 0;\nexports.getErrorFromOAuthErrorResponse = getErrorFromOAuthErrorResponse;\nconst gaxios_1 = require(\"gaxios\");\nconst crypto_1 = require(\"../crypto/crypto\");\n/** List of HTTP methods that accept request bodies. */\nconst METHODS_SUPPORTING_REQUEST_BODY = ['PUT', 'POST', 'PATCH'];\n/**\n * Abstract class for handling client authentication in OAuth-based\n * operations.\n * When request-body client authentication is used, only application/json and\n * application/x-www-form-urlencoded content types for HTTP methods that support\n * request bodies are supported.\n */\nclass OAuthClientAuthHandler {\n    #crypto = (0, crypto_1.createCrypto)();\n    #clientAuthentication;\n    transporter;\n    /**\n     * Instantiates an OAuth client authentication handler.\n     * @param options The OAuth Client Auth Handler instance options. Passing an `ClientAuthentication` directly is **@DEPRECATED**.\n     */\n    constructor(options) {\n        if (options && 'clientId' in options) {\n            this.#clientAuthentication = options;\n            this.transporter = new gaxios_1.Gaxios();\n        }\n        else {\n            this.#clientAuthentication = options?.clientAuthentication;\n            this.transporter = options?.transporter || new gaxios_1.Gaxios();\n        }\n    }\n    /**\n     * Applies client authentication on the OAuth request's headers or POST\n     * body but does not process the request.\n     * @param opts The GaxiosOptions whose headers or data are to be modified\n     *   depending on the client authentication mechanism to be used.\n     * @param bearerToken The optional bearer token to use for authentication.\n     *   When this is used, no client authentication credentials are needed.\n     */\n    applyClientAuthenticationOptions(opts, bearerToken) {\n        opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers);\n        // Inject authenticated header.\n        this.injectAuthenticatedHeaders(opts, bearerToken);\n        // Inject authenticated request body.\n        if (!bearerToken) {\n            this.injectAuthenticatedRequestBody(opts);\n        }\n    }\n    /**\n     * Applies client authentication on the request's header if either\n     * basic authentication or bearer token authentication is selected.\n     *\n     * @param opts The GaxiosOptions whose headers or data are to be modified\n     *   depending on the client authentication mechanism to be used.\n     * @param bearerToken The optional bearer token to use for authentication.\n     *   When this is used, no client authentication credentials are needed.\n     */\n    injectAuthenticatedHeaders(opts, bearerToken) {\n        // Bearer token prioritized higher than basic Auth.\n        if (bearerToken) {\n            opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers, {\n                authorization: `Bearer ${bearerToken}`,\n            });\n        }\n        else if (this.#clientAuthentication?.confidentialClientType === 'basic') {\n            opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers);\n            const clientId = this.#clientAuthentication.clientId;\n            const clientSecret = this.#clientAuthentication.clientSecret || '';\n            const base64EncodedCreds = this.#crypto.encodeBase64StringUtf8(`${clientId}:${clientSecret}`);\n            gaxios_1.Gaxios.mergeHeaders(opts.headers, {\n                authorization: `Basic ${base64EncodedCreds}`,\n            });\n        }\n    }\n    /**\n     * Applies client authentication on the request's body if request-body\n     * client authentication is selected.\n     *\n     * @param opts The GaxiosOptions whose headers or data are to be modified\n     *   depending on the client authentication mechanism to be used.\n     */\n    injectAuthenticatedRequestBody(opts) {\n        if (this.#clientAuthentication?.confidentialClientType === 'request-body') {\n            const method = (opts.method || 'GET').toUpperCase();\n            if (!METHODS_SUPPORTING_REQUEST_BODY.includes(method)) {\n                throw new Error(`${method} HTTP method does not support ` +\n                    `${this.#clientAuthentication.confidentialClientType} ` +\n                    'client authentication');\n            }\n            // Get content-type\n            const headers = new Headers(opts.headers);\n            const contentType = headers.get('content-type');\n            // Inject authenticated request body\n            if (contentType?.startsWith('application/x-www-form-urlencoded') ||\n                opts.data instanceof URLSearchParams) {\n                const data = new URLSearchParams(opts.data ?? '');\n                data.append('client_id', this.#clientAuthentication.clientId);\n                data.append('client_secret', this.#clientAuthentication.clientSecret || '');\n                opts.data = data;\n            }\n            else if (contentType?.startsWith('application/json')) {\n                opts.data = opts.data || {};\n                Object.assign(opts.data, {\n                    client_id: this.#clientAuthentication.clientId,\n                    client_secret: this.#clientAuthentication.clientSecret || '',\n                });\n            }\n            else {\n                throw new Error(`${contentType} content-types are not supported with ` +\n                    `${this.#clientAuthentication.confidentialClientType} ` +\n                    'client authentication');\n            }\n        }\n    }\n    /**\n     * Retry config for Auth-related requests.\n     *\n     * @remarks\n     *\n     * This is not a part of the default {@link AuthClient.transporter transporter/gaxios}\n     * config as some downstream APIs would prefer if customers explicitly enable retries,\n     * such as GCS.\n     */\n    static get RETRY_CONFIG() {\n        return {\n            retry: true,\n            retryConfig: {\n                httpMethodsToRetry: ['GET', 'PUT', 'POST', 'HEAD', 'OPTIONS', 'DELETE'],\n            },\n        };\n    }\n}\nexports.OAuthClientAuthHandler = OAuthClientAuthHandler;\n/**\n * Converts an OAuth error response to a native JavaScript Error.\n * @param resp The OAuth error response to convert to a native Error object.\n * @param err The optional original error. If provided, the error properties\n *   will be copied to the new error.\n * @return The converted native Error object.\n */\nfunction getErrorFromOAuthErrorResponse(resp, err) {\n    // Error response.\n    const errorCode = resp.error;\n    const errorDescription = resp.error_description;\n    const errorUri = resp.error_uri;\n    let message = `Error code ${errorCode}`;\n    if (typeof errorDescription !== 'undefined') {\n        message += `: ${errorDescription}`;\n    }\n    if (typeof errorUri !== 'undefined') {\n        message += ` - ${errorUri}`;\n    }\n    const newError = new Error(message);\n    // Copy properties from original error to newly generated error.\n    if (err) {\n        const keys = Object.keys(err);\n        if (err.stack) {\n            // Copy error.stack if available.\n            keys.push('stack');\n        }\n        keys.forEach(key => {\n            // Do not overwrite the message field.\n            if (key !== 'message') {\n                Object.defineProperty(newError, key, {\n                    value: err[key],\n                    writable: false,\n                    enumerable: true,\n                });\n            }\n        });\n    }\n    return newError;\n}\n//# sourceMappingURL=oauth2common.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG,KAAK;AACtC,QAAQ,8BAA8B,GAAG;AACzC,MAAM;AACN,MAAM;AACN,qDAAqD,GACrD,MAAM,kCAAkC;IAAC;IAAO;IAAQ;CAAQ;AAChE;;;;;;CAMC,GACD,MAAM;IACF,CAAA,MAAO,GAAG,CAAC,GAAG,SAAS,YAAY,IAAI;IACvC,CAAA,oBAAqB,CAAC;IACtB,YAAY;IACZ;;;KAGC,GACD,YAAY,OAAO,CAAE;QACjB,IAAI,WAAW,cAAc,SAAS;YAClC,IAAI,CAAC,CAAA,oBAAqB,GAAG;YAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,SAAS,MAAM;QAC1C,OACK;YACD,IAAI,CAAC,CAAA,oBAAqB,GAAG,SAAS;YACtC,IAAI,CAAC,WAAW,GAAG,SAAS,eAAe,IAAI,SAAS,MAAM;QAClE;IACJ;IACA;;;;;;;KAOC,GACD,iCAAiC,IAAI,EAAE,WAAW,EAAE;QAChD,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO;QACxD,+BAA+B;QAC/B,IAAI,CAAC,0BAA0B,CAAC,MAAM;QACtC,qCAAqC;QACrC,IAAI,CAAC,aAAa;YACd,IAAI,CAAC,8BAA8B,CAAC;QACxC;IACJ;IACA;;;;;;;;KAQC,GACD,2BAA2B,IAAI,EAAE,WAAW,EAAE;QAC1C,mDAAmD;QACnD,IAAI,aAAa;YACb,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO,EAAE;gBACtD,eAAe,CAAC,OAAO,EAAE,aAAa;YAC1C;QACJ,OACK,IAAI,IAAI,CAAC,CAAA,oBAAqB,EAAE,2BAA2B,SAAS;YACrE,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO;YACxD,MAAM,WAAW,IAAI,CAAC,CAAA,oBAAqB,CAAC,QAAQ;YACpD,MAAM,eAAe,IAAI,CAAC,CAAA,oBAAqB,CAAC,YAAY,IAAI;YAChE,MAAM,qBAAqB,IAAI,CAAC,CAAA,MAAO,CAAC,sBAAsB,CAAC,GAAG,SAAS,CAAC,EAAE,cAAc;YAC5F,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO,EAAE;gBACvC,eAAe,CAAC,MAAM,EAAE,oBAAoB;YAChD;QACJ;IACJ;IACA;;;;;;KAMC,GACD,+BAA+B,IAAI,EAAE;QACjC,IAAI,IAAI,CAAC,CAAA,oBAAqB,EAAE,2BAA2B,gBAAgB;YACvE,MAAM,SAAS,CAAC,KAAK,MAAM,IAAI,KAAK,EAAE,WAAW;YACjD,IAAI,CAAC,gCAAgC,QAAQ,CAAC,SAAS;gBACnD,MAAM,IAAI,MAAM,GAAG,OAAO,8BAA8B,CAAC,GACrD,GAAG,IAAI,CAAC,CAAA,oBAAqB,CAAC,sBAAsB,CAAC,CAAC,CAAC,GACvD;YACR;YACA,mBAAmB;YACnB,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;YACxC,MAAM,cAAc,QAAQ,GAAG,CAAC;YAChC,oCAAoC;YACpC,IAAI,aAAa,WAAW,wCACxB,KAAK,IAAI,YAAY,iBAAiB;gBACtC,MAAM,OAAO,IAAI,gBAAgB,KAAK,IAAI,IAAI;gBAC9C,KAAK,MAAM,CAAC,aAAa,IAAI,CAAC,CAAA,oBAAqB,CAAC,QAAQ;gBAC5D,KAAK,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAA,oBAAqB,CAAC,YAAY,IAAI;gBACxE,KAAK,IAAI,GAAG;YAChB,OACK,IAAI,aAAa,WAAW,qBAAqB;gBAClD,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC;gBAC1B,OAAO,MAAM,CAAC,KAAK,IAAI,EAAE;oBACrB,WAAW,IAAI,CAAC,CAAA,oBAAqB,CAAC,QAAQ;oBAC9C,eAAe,IAAI,CAAC,CAAA,oBAAqB,CAAC,YAAY,IAAI;gBAC9D;YACJ,OACK;gBACD,MAAM,IAAI,MAAM,GAAG,YAAY,sCAAsC,CAAC,GAClE,GAAG,IAAI,CAAC,CAAA,oBAAqB,CAAC,sBAAsB,CAAC,CAAC,CAAC,GACvD;YACR;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,WAAW,eAAe;QACtB,OAAO;YACH,OAAO;YACP,aAAa;gBACT,oBAAoB;oBAAC;oBAAO;oBAAO;oBAAQ;oBAAQ;oBAAW;iBAAS;YAC3E;QACJ;IACJ;AACJ;AACA,QAAQ,sBAAsB,GAAG;AACjC;;;;;;CAMC,GACD,SAAS,+BAA+B,IAAI,EAAE,GAAG;IAC7C,kBAAkB;IAClB,MAAM,YAAY,KAAK,KAAK;IAC5B,MAAM,mBAAmB,KAAK,iBAAiB;IAC/C,MAAM,WAAW,KAAK,SAAS;IAC/B,IAAI,UAAU,CAAC,WAAW,EAAE,WAAW;IACvC,IAAI,OAAO,qBAAqB,aAAa;QACzC,WAAW,CAAC,EAAE,EAAE,kBAAkB;IACtC;IACA,IAAI,OAAO,aAAa,aAAa;QACjC,WAAW,CAAC,GAAG,EAAE,UAAU;IAC/B;IACA,MAAM,WAAW,IAAI,MAAM;IAC3B,gEAAgE;IAChE,IAAI,KAAK;QACL,MAAM,OAAO,OAAO,IAAI,CAAC;QACzB,IAAI,IAAI,KAAK,EAAE;YACX,iCAAiC;YACjC,KAAK,IAAI,CAAC;QACd;QACA,KAAK,OAAO,CAAC,CAAA;YACT,sCAAsC;YACtC,IAAI,QAAQ,WAAW;gBACnB,OAAO,cAAc,CAAC,UAAU,KAAK;oBACjC,OAAO,GAAG,CAAC,IAAI;oBACf,UAAU;oBACV,YAAY;gBAChB;YACJ;QACJ;IACJ;IACA,OAAO;AACX,EACA,wCAAwC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2920, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/stscredentials.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.StsCredentials = void 0;\nconst gaxios_1 = require(\"gaxios\");\nconst authclient_1 = require(\"./authclient\");\nconst oauth2common_1 = require(\"./oauth2common\");\nconst util_1 = require(\"../util\");\n/**\n * Implements the OAuth 2.0 token exchange based on\n * https://tools.ietf.org/html/rfc8693\n */\nclass StsCredentials extends oauth2common_1.OAuthClientAuthHandler {\n    #tokenExchangeEndpoint;\n    /**\n     * Initializes an STS credentials instance.\n     *\n     * @param options The STS credentials instance options. Passing an `tokenExchangeEndpoint` directly is **@DEPRECATED**.\n     * @param clientAuthentication **@DEPRECATED**. Provide a {@link StsCredentialsConstructionOptions `StsCredentialsConstructionOptions`} object in the first parameter instead.\n     */\n    constructor(options = {\n        tokenExchangeEndpoint: '',\n    }, \n    /**\n     * @deprecated - provide a {@link StsCredentialsConstructionOptions `StsCredentialsConstructionOptions`} object in the first parameter instead\n     */\n    clientAuthentication) {\n        if (typeof options !== 'object' || options instanceof URL) {\n            options = {\n                tokenExchangeEndpoint: options,\n                clientAuthentication,\n            };\n        }\n        super(options);\n        this.#tokenExchangeEndpoint = options.tokenExchangeEndpoint;\n    }\n    /**\n     * Exchanges the provided token for another type of token based on the\n     * rfc8693 spec.\n     * @param stsCredentialsOptions The token exchange options used to populate\n     *   the token exchange request.\n     * @param additionalHeaders Optional additional headers to pass along the\n     *   request.\n     * @param options Optional additional GCP-specific non-spec defined options\n     *   to send with the request.\n     *   Example: `&options=${encodeUriComponent(JSON.stringified(options))}`\n     * @return A promise that resolves with the token exchange response containing\n     *   the requested token and its expiration time.\n     */\n    async exchangeToken(stsCredentialsOptions, headers, options) {\n        const values = {\n            grant_type: stsCredentialsOptions.grantType,\n            resource: stsCredentialsOptions.resource,\n            audience: stsCredentialsOptions.audience,\n            scope: stsCredentialsOptions.scope?.join(' '),\n            requested_token_type: stsCredentialsOptions.requestedTokenType,\n            subject_token: stsCredentialsOptions.subjectToken,\n            subject_token_type: stsCredentialsOptions.subjectTokenType,\n            actor_token: stsCredentialsOptions.actingParty?.actorToken,\n            actor_token_type: stsCredentialsOptions.actingParty?.actorTokenType,\n            // Non-standard GCP-specific options.\n            options: options && JSON.stringify(options),\n        };\n        const opts = {\n            ...StsCredentials.RETRY_CONFIG,\n            url: this.#tokenExchangeEndpoint.toString(),\n            method: 'POST',\n            headers,\n            data: new URLSearchParams((0, util_1.removeUndefinedValuesInObject)(values)),\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'exchangeToken');\n        // Apply OAuth client authentication.\n        this.applyClientAuthenticationOptions(opts);\n        try {\n            const response = await this.transporter.request(opts);\n            // Successful response.\n            const stsSuccessfulResponse = response.data;\n            stsSuccessfulResponse.res = response;\n            return stsSuccessfulResponse;\n        }\n        catch (error) {\n            // Translate error to OAuthError.\n            if (error instanceof gaxios_1.GaxiosError && error.response) {\n                throw (0, oauth2common_1.getErrorFromOAuthErrorResponse)(error.response.data, \n                // Preserve other fields from the original error.\n                error);\n            }\n            // Request could fail before the server responds.\n            throw error;\n        }\n    }\n}\nexports.StsCredentials = StsCredentials;\n//# sourceMappingURL=stscredentials.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;CAGC,GACD,MAAM,uBAAuB,eAAe,sBAAsB;IAC9D,CAAA,qBAAsB,CAAC;IACvB;;;;;KAKC,GACD,YAAY,UAAU;QAClB,uBAAuB;IAC3B,CAAC,EACD;;KAEC,GACD,oBAAoB,CAAE;QAClB,IAAI,OAAO,YAAY,YAAY,mBAAmB,KAAK;YACvD,UAAU;gBACN,uBAAuB;gBACvB;YACJ;QACJ;QACA,KAAK,CAAC;QACN,IAAI,CAAC,CAAA,qBAAsB,GAAG,QAAQ,qBAAqB;IAC/D;IACA;;;;;;;;;;;;KAYC,GACD,MAAM,cAAc,qBAAqB,EAAE,OAAO,EAAE,OAAO,EAAE;QACzD,MAAM,SAAS;YACX,YAAY,sBAAsB,SAAS;YAC3C,UAAU,sBAAsB,QAAQ;YACxC,UAAU,sBAAsB,QAAQ;YACxC,OAAO,sBAAsB,KAAK,EAAE,KAAK;YACzC,sBAAsB,sBAAsB,kBAAkB;YAC9D,eAAe,sBAAsB,YAAY;YACjD,oBAAoB,sBAAsB,gBAAgB;YAC1D,aAAa,sBAAsB,WAAW,EAAE;YAChD,kBAAkB,sBAAsB,WAAW,EAAE;YACrD,qCAAqC;YACrC,SAAS,WAAW,KAAK,SAAS,CAAC;QACvC;QACA,MAAM,OAAO;YACT,GAAG,eAAe,YAAY;YAC9B,KAAK,IAAI,CAAC,CAAA,qBAAsB,CAAC,QAAQ;YACzC,QAAQ;YACR;YACA,MAAM,IAAI,gBAAgB,CAAC,GAAG,OAAO,6BAA6B,EAAE;QACxE;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,qCAAqC;QACrC,IAAI,CAAC,gCAAgC,CAAC;QACtC,IAAI;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAChD,uBAAuB;YACvB,MAAM,wBAAwB,SAAS,IAAI;YAC3C,sBAAsB,GAAG,GAAG;YAC5B,OAAO;QACX,EACA,OAAO,OAAO;YACV,iCAAiC;YACjC,IAAI,iBAAiB,SAAS,WAAW,IAAI,MAAM,QAAQ,EAAE;gBACzD,MAAM,CAAC,GAAG,eAAe,8BAA8B,EAAE,MAAM,QAAQ,CAAC,IAAI,EAC5E,iDAAiD;gBACjD;YACJ;YACA,iDAAiD;YACjD,MAAM;QACV;IACJ;AACJ;AACA,QAAQ,cAAc,GAAG,gBACzB,0CAA0C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3025, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/baseexternalclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BaseExternalAccountClient = exports.CLOUD_RESOURCE_MANAGER = exports.EXTERNAL_ACCOUNT_TYPE = exports.EXPIRATION_TIME_OFFSET = void 0;\nconst gaxios_1 = require(\"gaxios\");\nconst stream = require(\"stream\");\nconst authclient_1 = require(\"./authclient\");\nconst sts = require(\"./stscredentials\");\nconst util_1 = require(\"../util\");\nconst shared_cjs_1 = require(\"../shared.cjs\");\n/**\n * The required token exchange grant_type: rfc8693#section-2.1\n */\nconst STS_GRANT_TYPE = 'urn:ietf:params:oauth:grant-type:token-exchange';\n/**\n * The requested token exchange requested_token_type: rfc8693#section-2.1\n */\nconst STS_REQUEST_TOKEN_TYPE = 'urn:ietf:params:oauth:token-type:access_token';\n/** The default OAuth scope to request when none is provided. */\nconst DEFAULT_OAUTH_SCOPE = 'https://www.googleapis.com/auth/cloud-platform';\n/** Default impersonated token lifespan in seconds.*/\nconst DEFAULT_TOKEN_LIFESPAN = 3600;\n/**\n * Offset to take into account network delays and server clock skews.\n */\nexports.EXPIRATION_TIME_OFFSET = 5 * 60 * 1000;\n/**\n * The credentials JSON file type for external account clients.\n * There are 3 types of JSON configs:\n * 1. authorized_user => Google end user credential\n * 2. service_account => Google service account credential\n * 3. external_Account => non-GCP service (eg. AWS, Azure, K8s)\n */\nexports.EXTERNAL_ACCOUNT_TYPE = 'external_account';\n/**\n * Cloud resource manager URL used to retrieve project information.\n *\n * @deprecated use {@link BaseExternalAccountClient.cloudResourceManagerURL} instead\n **/\nexports.CLOUD_RESOURCE_MANAGER = 'https://cloudresourcemanager.googleapis.com/v1/projects/';\n/** The workforce audience pattern. */\nconst WORKFORCE_AUDIENCE_PATTERN = '//iam\\\\.googleapis\\\\.com/locations/[^/]+/workforcePools/[^/]+/providers/.+';\nconst DEFAULT_TOKEN_URL = 'https://sts.{universeDomain}/v1/token';\n/**\n * Base external account client. This is used to instantiate AuthClients for\n * exchanging external account credentials for GCP access token and authorizing\n * requests to GCP APIs.\n * The base class implements common logic for exchanging various type of\n * external credentials for GCP access token. The logic of determining and\n * retrieving the external credential based on the environment and\n * credential_source will be left for the subclasses.\n */\nclass BaseExternalAccountClient extends authclient_1.AuthClient {\n    /**\n     * OAuth scopes for the GCP access token to use. When not provided,\n     * the default https://www.googleapis.com/auth/cloud-platform is\n     * used.\n     */\n    scopes;\n    projectNumber;\n    audience;\n    subjectTokenType;\n    stsCredential;\n    clientAuth;\n    credentialSourceType;\n    cachedAccessToken;\n    serviceAccountImpersonationUrl;\n    serviceAccountImpersonationLifetime;\n    workforcePoolUserProject;\n    configLifetimeRequested;\n    tokenUrl;\n    /**\n     * @example\n     * ```ts\n     * new URL('https://cloudresourcemanager.googleapis.com/v1/projects/');\n     * ```\n     */\n    cloudResourceManagerURL;\n    supplierContext;\n    /**\n     * A pending access token request. Used for concurrent calls.\n     */\n    #pendingAccessToken = null;\n    /**\n     * Instantiate a BaseExternalAccountClient instance using the provided JSON\n     * object loaded from an external account credentials file.\n     * @param options The external account options object typically loaded\n     *   from the external account JSON credential file. The camelCased options\n     *   are aliases for the snake_cased options.\n     */\n    constructor(options) {\n        super(options);\n        const opts = (0, util_1.originalOrCamelOptions)(options);\n        const type = opts.get('type');\n        if (type && type !== exports.EXTERNAL_ACCOUNT_TYPE) {\n            throw new Error(`Expected \"${exports.EXTERNAL_ACCOUNT_TYPE}\" type but ` +\n                `received \"${options.type}\"`);\n        }\n        const clientId = opts.get('client_id');\n        const clientSecret = opts.get('client_secret');\n        this.tokenUrl =\n            opts.get('token_url') ??\n                DEFAULT_TOKEN_URL.replace('{universeDomain}', this.universeDomain);\n        const subjectTokenType = opts.get('subject_token_type');\n        const workforcePoolUserProject = opts.get('workforce_pool_user_project');\n        const serviceAccountImpersonationUrl = opts.get('service_account_impersonation_url');\n        const serviceAccountImpersonation = opts.get('service_account_impersonation');\n        const serviceAccountImpersonationLifetime = (0, util_1.originalOrCamelOptions)(serviceAccountImpersonation).get('token_lifetime_seconds');\n        this.cloudResourceManagerURL = new URL(opts.get('cloud_resource_manager_url') ||\n            `https://cloudresourcemanager.${this.universeDomain}/v1/projects/`);\n        if (clientId) {\n            this.clientAuth = {\n                confidentialClientType: 'basic',\n                clientId,\n                clientSecret,\n            };\n        }\n        this.stsCredential = new sts.StsCredentials({\n            tokenExchangeEndpoint: this.tokenUrl,\n            clientAuthentication: this.clientAuth,\n        });\n        this.scopes = opts.get('scopes') || [DEFAULT_OAUTH_SCOPE];\n        this.cachedAccessToken = null;\n        this.audience = opts.get('audience');\n        this.subjectTokenType = subjectTokenType;\n        this.workforcePoolUserProject = workforcePoolUserProject;\n        const workforceAudiencePattern = new RegExp(WORKFORCE_AUDIENCE_PATTERN);\n        if (this.workforcePoolUserProject &&\n            !this.audience.match(workforceAudiencePattern)) {\n            throw new Error('workforcePoolUserProject should not be set for non-workforce pool ' +\n                'credentials.');\n        }\n        this.serviceAccountImpersonationUrl = serviceAccountImpersonationUrl;\n        this.serviceAccountImpersonationLifetime =\n            serviceAccountImpersonationLifetime;\n        if (this.serviceAccountImpersonationLifetime) {\n            this.configLifetimeRequested = true;\n        }\n        else {\n            this.configLifetimeRequested = false;\n            this.serviceAccountImpersonationLifetime = DEFAULT_TOKEN_LIFESPAN;\n        }\n        this.projectNumber = this.getProjectNumber(this.audience);\n        this.supplierContext = {\n            audience: this.audience,\n            subjectTokenType: this.subjectTokenType,\n            transporter: this.transporter,\n        };\n    }\n    /** The service account email to be impersonated, if available. */\n    getServiceAccountEmail() {\n        if (this.serviceAccountImpersonationUrl) {\n            if (this.serviceAccountImpersonationUrl.length > 256) {\n                /**\n                 * Prevents DOS attacks.\n                 * @see {@link https://github.com/googleapis/google-auth-library-nodejs/security/code-scanning/84}\n                 **/\n                throw new RangeError(`URL is too long: ${this.serviceAccountImpersonationUrl}`);\n            }\n            // Parse email from URL. The formal looks as follows:\n            // https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken\n            const re = /serviceAccounts\\/(?<email>[^:]+):generateAccessToken$/;\n            const result = re.exec(this.serviceAccountImpersonationUrl);\n            return result?.groups?.email || null;\n        }\n        return null;\n    }\n    /**\n     * Provides a mechanism to inject GCP access tokens directly.\n     * When the provided credential expires, a new credential, using the\n     * external account options, is retrieved.\n     * @param credentials The Credentials object to set on the current client.\n     */\n    setCredentials(credentials) {\n        super.setCredentials(credentials);\n        this.cachedAccessToken = credentials;\n    }\n    /**\n     * @return A promise that resolves with the current GCP access token\n     *   response. If the current credential is expired, a new one is retrieved.\n     */\n    async getAccessToken() {\n        // If cached access token is unavailable or expired, force refresh.\n        if (!this.cachedAccessToken || this.isExpired(this.cachedAccessToken)) {\n            await this.refreshAccessTokenAsync();\n        }\n        // Return GCP access token in GetAccessTokenResponse format.\n        return {\n            token: this.cachedAccessToken.access_token,\n            res: this.cachedAccessToken.res,\n        };\n    }\n    /**\n     * The main authentication interface. It takes an optional url which when\n     * present is the endpoint being accessed, and returns a Promise which\n     * resolves with authorization header fields.\n     *\n     * The result has the form:\n     * { authorization: 'Bearer <access_token_value>' }\n     */\n    async getRequestHeaders() {\n        const accessTokenResponse = await this.getAccessToken();\n        const headers = new Headers({\n            authorization: `Bearer ${accessTokenResponse.token}`,\n        });\n        return this.addSharedMetadataHeaders(headers);\n    }\n    request(opts, callback) {\n        if (callback) {\n            this.requestAsync(opts).then(r => callback(null, r), e => {\n                return callback(e, e.response);\n            });\n        }\n        else {\n            return this.requestAsync(opts);\n        }\n    }\n    /**\n     * @return A promise that resolves with the project ID corresponding to the\n     *   current workload identity pool or current workforce pool if\n     *   determinable. For workforce pool credential, it returns the project ID\n     *   corresponding to the workforcePoolUserProject.\n     *   This is introduced to match the current pattern of using the Auth\n     *   library:\n     *   const projectId = await auth.getProjectId();\n     *   const url = `https://dns.googleapis.com/dns/v1/projects/${projectId}`;\n     *   const res = await client.request({ url });\n     *   The resource may not have permission\n     *   (resourcemanager.projects.get) to call this API or the required\n     *   scopes may not be selected:\n     *   https://cloud.google.com/resource-manager/reference/rest/v1/projects/get#authorization-scopes\n     */\n    async getProjectId() {\n        const projectNumber = this.projectNumber || this.workforcePoolUserProject;\n        if (this.projectId) {\n            // Return previously determined project ID.\n            return this.projectId;\n        }\n        else if (projectNumber) {\n            // Preferable not to use request() to avoid retrial policies.\n            const headers = await this.getRequestHeaders();\n            const opts = {\n                ...BaseExternalAccountClient.RETRY_CONFIG,\n                headers,\n                url: `${this.cloudResourceManagerURL.toString()}${projectNumber}`,\n            };\n            authclient_1.AuthClient.setMethodName(opts, 'getProjectId');\n            const response = await this.transporter.request(opts);\n            this.projectId = response.data.projectId;\n            return this.projectId;\n        }\n        return null;\n    }\n    /**\n     * Authenticates the provided HTTP request, processes it and resolves with the\n     * returned response.\n     * @param opts The HTTP request options.\n     * @param reAuthRetried Whether the current attempt is a retry after a failed attempt due to an auth failure.\n     * @return A promise that resolves with the successful response.\n     */\n    async requestAsync(opts, reAuthRetried = false) {\n        let response;\n        try {\n            const requestHeaders = await this.getRequestHeaders();\n            opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers);\n            this.addUserProjectAndAuthHeaders(opts.headers, requestHeaders);\n            response = await this.transporter.request(opts);\n        }\n        catch (e) {\n            const res = e.response;\n            if (res) {\n                const statusCode = res.status;\n                // Retry the request for metadata if the following criteria are true:\n                // - We haven't already retried.  It only makes sense to retry once.\n                // - The response was a 401 or a 403\n                // - The request didn't send a readableStream\n                // - forceRefreshOnFailure is true\n                const isReadableStream = res.config.data instanceof stream.Readable;\n                const isAuthErr = statusCode === 401 || statusCode === 403;\n                if (!reAuthRetried &&\n                    isAuthErr &&\n                    !isReadableStream &&\n                    this.forceRefreshOnFailure) {\n                    await this.refreshAccessTokenAsync();\n                    return await this.requestAsync(opts, true);\n                }\n            }\n            throw e;\n        }\n        return response;\n    }\n    /**\n     * Forces token refresh, even if unexpired tokens are currently cached.\n     * External credentials are exchanged for GCP access tokens via the token\n     * exchange endpoint and other settings provided in the client options\n     * object.\n     * If the service_account_impersonation_url is provided, an additional\n     * step to exchange the external account GCP access token for a service\n     * account impersonated token is performed.\n     * @return A promise that resolves with the fresh GCP access tokens.\n     */\n    async refreshAccessTokenAsync() {\n        // Use an existing access token request, or cache a new one\n        this.#pendingAccessToken =\n            this.#pendingAccessToken || this.#internalRefreshAccessTokenAsync();\n        try {\n            return await this.#pendingAccessToken;\n        }\n        finally {\n            // clear pending access token for future requests\n            this.#pendingAccessToken = null;\n        }\n    }\n    async #internalRefreshAccessTokenAsync() {\n        // Retrieve the external credential.\n        const subjectToken = await this.retrieveSubjectToken();\n        // Construct the STS credentials options.\n        const stsCredentialsOptions = {\n            grantType: STS_GRANT_TYPE,\n            audience: this.audience,\n            requestedTokenType: STS_REQUEST_TOKEN_TYPE,\n            subjectToken,\n            subjectTokenType: this.subjectTokenType,\n            // generateAccessToken requires the provided access token to have\n            // scopes:\n            // https://www.googleapis.com/auth/iam or\n            // https://www.googleapis.com/auth/cloud-platform\n            // The new service account access token scopes will match the user\n            // provided ones.\n            scope: this.serviceAccountImpersonationUrl\n                ? [DEFAULT_OAUTH_SCOPE]\n                : this.getScopesArray(),\n        };\n        // Exchange the external credentials for a GCP access token.\n        // Client auth is prioritized over passing the workforcePoolUserProject\n        // parameter for STS token exchange.\n        const additionalOptions = !this.clientAuth && this.workforcePoolUserProject\n            ? { userProject: this.workforcePoolUserProject }\n            : undefined;\n        const additionalHeaders = new Headers({\n            'x-goog-api-client': this.getMetricsHeaderValue(),\n        });\n        const stsResponse = await this.stsCredential.exchangeToken(stsCredentialsOptions, additionalHeaders, additionalOptions);\n        if (this.serviceAccountImpersonationUrl) {\n            this.cachedAccessToken = await this.getImpersonatedAccessToken(stsResponse.access_token);\n        }\n        else if (stsResponse.expires_in) {\n            // Save response in cached access token.\n            this.cachedAccessToken = {\n                access_token: stsResponse.access_token,\n                expiry_date: new Date().getTime() + stsResponse.expires_in * 1000,\n                res: stsResponse.res,\n            };\n        }\n        else {\n            // Save response in cached access token.\n            this.cachedAccessToken = {\n                access_token: stsResponse.access_token,\n                res: stsResponse.res,\n            };\n        }\n        // Save credentials.\n        this.credentials = {};\n        Object.assign(this.credentials, this.cachedAccessToken);\n        delete this.credentials.res;\n        // Trigger tokens event to notify external listeners.\n        this.emit('tokens', {\n            refresh_token: null,\n            expiry_date: this.cachedAccessToken.expiry_date,\n            access_token: this.cachedAccessToken.access_token,\n            token_type: 'Bearer',\n            id_token: null,\n        });\n        // Return the cached access token.\n        return this.cachedAccessToken;\n    }\n    /**\n     * Returns the workload identity pool project number if it is determinable\n     * from the audience resource name.\n     * @param audience The STS audience used to determine the project number.\n     * @return The project number associated with the workload identity pool, if\n     *   this can be determined from the STS audience field. Otherwise, null is\n     *   returned.\n     */\n    getProjectNumber(audience) {\n        // STS audience pattern:\n        // //iam.googleapis.com/projects/$PROJECT_NUMBER/locations/...\n        const match = audience.match(/\\/projects\\/([^/]+)/);\n        if (!match) {\n            return null;\n        }\n        return match[1];\n    }\n    /**\n     * Exchanges an external account GCP access token for a service\n     * account impersonated access token using iamcredentials\n     * GenerateAccessToken API.\n     * @param token The access token to exchange for a service account access\n     *   token.\n     * @return A promise that resolves with the service account impersonated\n     *   credentials response.\n     */\n    async getImpersonatedAccessToken(token) {\n        const opts = {\n            ...BaseExternalAccountClient.RETRY_CONFIG,\n            url: this.serviceAccountImpersonationUrl,\n            method: 'POST',\n            headers: {\n                'content-type': 'application/json',\n                authorization: `Bearer ${token}`,\n            },\n            data: {\n                scope: this.getScopesArray(),\n                lifetime: this.serviceAccountImpersonationLifetime + 's',\n            },\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'getImpersonatedAccessToken');\n        const response = await this.transporter.request(opts);\n        const successResponse = response.data;\n        return {\n            access_token: successResponse.accessToken,\n            // Convert from ISO format to timestamp.\n            expiry_date: new Date(successResponse.expireTime).getTime(),\n            res: response,\n        };\n    }\n    /**\n     * Returns whether the provided credentials are expired or not.\n     * If there is no expiry time, assumes the token is not expired or expiring.\n     * @param accessToken The credentials to check for expiration.\n     * @return Whether the credentials are expired or not.\n     */\n    isExpired(accessToken) {\n        const now = new Date().getTime();\n        return accessToken.expiry_date\n            ? now >= accessToken.expiry_date - this.eagerRefreshThresholdMillis\n            : false;\n    }\n    /**\n     * @return The list of scopes for the requested GCP access token.\n     */\n    getScopesArray() {\n        // Since scopes can be provided as string or array, the type should\n        // be normalized.\n        if (typeof this.scopes === 'string') {\n            return [this.scopes];\n        }\n        return this.scopes || [DEFAULT_OAUTH_SCOPE];\n    }\n    getMetricsHeaderValue() {\n        const nodeVersion = process.version.replace(/^v/, '');\n        const saImpersonation = this.serviceAccountImpersonationUrl !== undefined;\n        const credentialSourceType = this.credentialSourceType\n            ? this.credentialSourceType\n            : 'unknown';\n        return `gl-node/${nodeVersion} auth/${shared_cjs_1.pkg.version} google-byoid-sdk source/${credentialSourceType} sa-impersonation/${saImpersonation} config-lifetime/${this.configLifetimeRequested}`;\n    }\n    getTokenUrl() {\n        return this.tokenUrl;\n    }\n}\nexports.BaseExternalAccountClient = BaseExternalAccountClient;\n//# sourceMappingURL=baseexternalclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,sBAAsB,GAAG,KAAK;AAC3I,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;CAEC,GACD,MAAM,iBAAiB;AACvB;;CAEC,GACD,MAAM,yBAAyB;AAC/B,8DAA8D,GAC9D,MAAM,sBAAsB;AAC5B,mDAAmD,GACnD,MAAM,yBAAyB;AAC/B;;CAEC,GACD,QAAQ,sBAAsB,GAAG,IAAI,KAAK;AAC1C;;;;;;CAMC,GACD,QAAQ,qBAAqB,GAAG;AAChC;;;;EAIE,GACF,QAAQ,sBAAsB,GAAG;AACjC,oCAAoC,GACpC,MAAM,6BAA6B;AACnC,MAAM,oBAAoB;AAC1B;;;;;;;;CAQC,GACD,MAAM,kCAAkC,aAAa,UAAU;IAC3D;;;;KAIC,GACD,OAAO;IACP,cAAc;IACd,SAAS;IACT,iBAAiB;IACjB,cAAc;IACd,WAAW;IACX,qBAAqB;IACrB,kBAAkB;IAClB,+BAA+B;IAC/B,oCAAoC;IACpC,yBAAyB;IACzB,wBAAwB;IACxB,SAAS;IACT;;;;;KAKC,GACD,wBAAwB;IACxB,gBAAgB;IAChB;;KAEC,GACD,CAAA,kBAAmB,GAAG,KAAK;IAC3B;;;;;;KAMC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,MAAM,OAAO,CAAC,GAAG,OAAO,sBAAsB,EAAE;QAChD,MAAM,OAAO,KAAK,GAAG,CAAC;QACtB,IAAI,QAAQ,SAAS,QAAQ,qBAAqB,EAAE;YAChD,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,QAAQ,qBAAqB,CAAC,WAAW,CAAC,GACnE,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,CAAC,CAAC;QACpC;QACA,MAAM,WAAW,KAAK,GAAG,CAAC;QAC1B,MAAM,eAAe,KAAK,GAAG,CAAC;QAC9B,IAAI,CAAC,QAAQ,GACT,KAAK,GAAG,CAAC,gBACL,kBAAkB,OAAO,CAAC,oBAAoB,IAAI,CAAC,cAAc;QACzE,MAAM,mBAAmB,KAAK,GAAG,CAAC;QAClC,MAAM,2BAA2B,KAAK,GAAG,CAAC;QAC1C,MAAM,iCAAiC,KAAK,GAAG,CAAC;QAChD,MAAM,8BAA8B,KAAK,GAAG,CAAC;QAC7C,MAAM,sCAAsC,CAAC,GAAG,OAAO,sBAAsB,EAAE,6BAA6B,GAAG,CAAC;QAChH,IAAI,CAAC,uBAAuB,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,iCAC5C,CAAC,6BAA6B,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;QACtE,IAAI,UAAU;YACV,IAAI,CAAC,UAAU,GAAG;gBACd,wBAAwB;gBACxB;gBACA;YACJ;QACJ;QACA,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,cAAc,CAAC;YACxC,uBAAuB,IAAI,CAAC,QAAQ;YACpC,sBAAsB,IAAI,CAAC,UAAU;QACzC;QACA,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,aAAa;YAAC;SAAoB;QACzD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,wBAAwB,GAAG;QAChC,MAAM,2BAA2B,IAAI,OAAO;QAC5C,IAAI,IAAI,CAAC,wBAAwB,IAC7B,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B;YAChD,MAAM,IAAI,MAAM,uEACZ;QACR;QACA,IAAI,CAAC,8BAA8B,GAAG;QACtC,IAAI,CAAC,mCAAmC,GACpC;QACJ,IAAI,IAAI,CAAC,mCAAmC,EAAE;YAC1C,IAAI,CAAC,uBAAuB,GAAG;QACnC,OACK;YACD,IAAI,CAAC,uBAAuB,GAAG;YAC/B,IAAI,CAAC,mCAAmC,GAAG;QAC/C;QACA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ;QACxD,IAAI,CAAC,eAAe,GAAG;YACnB,UAAU,IAAI,CAAC,QAAQ;YACvB,kBAAkB,IAAI,CAAC,gBAAgB;YACvC,aAAa,IAAI,CAAC,WAAW;QACjC;IACJ;IACA,gEAAgE,GAChE,yBAAyB;QACrB,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACrC,IAAI,IAAI,CAAC,8BAA8B,CAAC,MAAM,GAAG,KAAK;gBAClD;;;kBAGE,GACF,MAAM,IAAI,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,8BAA8B,EAAE;YAClF;YACA,qDAAqD;YACrD,kIAAkI;YAClI,MAAM,KAAK;YACX,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,8BAA8B;YAC1D,OAAO,QAAQ,QAAQ,SAAS;QACpC;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,eAAe,WAAW,EAAE;QACxB,KAAK,CAAC,eAAe;QACrB,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA;;;KAGC,GACD,MAAM,iBAAiB;QACnB,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,GAAG;YACnE,MAAM,IAAI,CAAC,uBAAuB;QACtC;QACA,4DAA4D;QAC5D,OAAO;YACH,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY;YAC1C,KAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG;QACnC;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,oBAAoB;QACtB,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc;QACrD,MAAM,UAAU,IAAI,QAAQ;YACxB,eAAe,CAAC,OAAO,EAAE,oBAAoB,KAAK,EAAE;QACxD;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,QAAQ,IAAI,EAAE,QAAQ,EAAE;QACpB,IAAI,UAAU;YACV,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI,CAAA;gBACjD,OAAO,SAAS,GAAG,EAAE,QAAQ;YACjC;QACJ,OACK;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B;IACJ;IACA;;;;;;;;;;;;;;KAcC,GACD,MAAM,eAAe;QACjB,MAAM,gBAAgB,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,wBAAwB;QACzE,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,2CAA2C;YAC3C,OAAO,IAAI,CAAC,SAAS;QACzB,OACK,IAAI,eAAe;YACpB,6DAA6D;YAC7D,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB;YAC5C,MAAM,OAAO;gBACT,GAAG,0BAA0B,YAAY;gBACzC;gBACA,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,KAAK,eAAe;YACrE;YACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;YAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAChD,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS;YACxC,OAAO,IAAI,CAAC,SAAS;QACzB;QACA,OAAO;IACX;IACA;;;;;;KAMC,GACD,MAAM,aAAa,IAAI,EAAE,gBAAgB,KAAK,EAAE;QAC5C,IAAI;QACJ,IAAI;YACA,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB;YACnD,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO;YACxD,IAAI,CAAC,4BAA4B,CAAC,KAAK,OAAO,EAAE;YAChD,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC9C,EACA,OAAO,GAAG;YACN,MAAM,MAAM,EAAE,QAAQ;YACtB,IAAI,KAAK;gBACL,MAAM,aAAa,IAAI,MAAM;gBAC7B,qEAAqE;gBACrE,oEAAoE;gBACpE,oCAAoC;gBACpC,6CAA6C;gBAC7C,kCAAkC;gBAClC,MAAM,mBAAmB,IAAI,MAAM,CAAC,IAAI,YAAY,OAAO,QAAQ;gBACnE,MAAM,YAAY,eAAe,OAAO,eAAe;gBACvD,IAAI,CAAC,iBACD,aACA,CAAC,oBACD,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,MAAM,IAAI,CAAC,uBAAuB;oBAClC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM;gBACzC;YACJ;YACA,MAAM;QACV;QACA,OAAO;IACX;IACA;;;;;;;;;KASC,GACD,MAAM,0BAA0B;QAC5B,2DAA2D;QAC3D,IAAI,CAAC,CAAA,kBAAmB,GACpB,IAAI,CAAC,CAAA,kBAAmB,IAAI,IAAI,CAAC,CAAA,+BAAgC;QACrE,IAAI;YACA,OAAO,MAAM,IAAI,CAAC,CAAA,kBAAmB;QACzC,SACQ;YACJ,iDAAiD;YACjD,IAAI,CAAC,CAAA,kBAAmB,GAAG;QAC/B;IACJ;IACA,MAAM,CAAA,+BAAgC;QAClC,oCAAoC;QACpC,MAAM,eAAe,MAAM,IAAI,CAAC,oBAAoB;QACpD,yCAAyC;QACzC,MAAM,wBAAwB;YAC1B,WAAW;YACX,UAAU,IAAI,CAAC,QAAQ;YACvB,oBAAoB;YACpB;YACA,kBAAkB,IAAI,CAAC,gBAAgB;YACvC,iEAAiE;YACjE,UAAU;YACV,yCAAyC;YACzC,iDAAiD;YACjD,kEAAkE;YAClE,iBAAiB;YACjB,OAAO,IAAI,CAAC,8BAA8B,GACpC;gBAAC;aAAoB,GACrB,IAAI,CAAC,cAAc;QAC7B;QACA,4DAA4D;QAC5D,uEAAuE;QACvE,oCAAoC;QACpC,MAAM,oBAAoB,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,wBAAwB,GACrE;YAAE,aAAa,IAAI,CAAC,wBAAwB;QAAC,IAC7C;QACN,MAAM,oBAAoB,IAAI,QAAQ;YAClC,qBAAqB,IAAI,CAAC,qBAAqB;QACnD;QACA,MAAM,cAAc,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,uBAAuB,mBAAmB;QACrG,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACrC,IAAI,CAAC,iBAAiB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,YAAY,YAAY;QAC3F,OACK,IAAI,YAAY,UAAU,EAAE;YAC7B,wCAAwC;YACxC,IAAI,CAAC,iBAAiB,GAAG;gBACrB,cAAc,YAAY,YAAY;gBACtC,aAAa,IAAI,OAAO,OAAO,KAAK,YAAY,UAAU,GAAG;gBAC7D,KAAK,YAAY,GAAG;YACxB;QACJ,OACK;YACD,wCAAwC;YACxC,IAAI,CAAC,iBAAiB,GAAG;gBACrB,cAAc,YAAY,YAAY;gBACtC,KAAK,YAAY,GAAG;YACxB;QACJ;QACA,oBAAoB;QACpB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB;QACtD,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3B,qDAAqD;QACrD,IAAI,CAAC,IAAI,CAAC,UAAU;YAChB,eAAe;YACf,aAAa,IAAI,CAAC,iBAAiB,CAAC,WAAW;YAC/C,cAAc,IAAI,CAAC,iBAAiB,CAAC,YAAY;YACjD,YAAY;YACZ,UAAU;QACd;QACA,kCAAkC;QAClC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA;;;;;;;KAOC,GACD,iBAAiB,QAAQ,EAAE;QACvB,wBAAwB;QACxB,8DAA8D;QAC9D,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,OAAO,KAAK,CAAC,EAAE;IACnB;IACA;;;;;;;;KAQC,GACD,MAAM,2BAA2B,KAAK,EAAE;QACpC,MAAM,OAAO;YACT,GAAG,0BAA0B,YAAY;YACzC,KAAK,IAAI,CAAC,8BAA8B;YACxC,QAAQ;YACR,SAAS;gBACL,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM;gBACF,OAAO,IAAI,CAAC,cAAc;gBAC1B,UAAU,IAAI,CAAC,mCAAmC,GAAG;YACzD;QACJ;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAChD,MAAM,kBAAkB,SAAS,IAAI;QACrC,OAAO;YACH,cAAc,gBAAgB,WAAW;YACzC,wCAAwC;YACxC,aAAa,IAAI,KAAK,gBAAgB,UAAU,EAAE,OAAO;YACzD,KAAK;QACT;IACJ;IACA;;;;;KAKC,GACD,UAAU,WAAW,EAAE;QACnB,MAAM,MAAM,IAAI,OAAO,OAAO;QAC9B,OAAO,YAAY,WAAW,GACxB,OAAO,YAAY,WAAW,GAAG,IAAI,CAAC,2BAA2B,GACjE;IACV;IACA;;KAEC,GACD,iBAAiB;QACb,mEAAmE;QACnE,iBAAiB;QACjB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;YACjC,OAAO;gBAAC,IAAI,CAAC,MAAM;aAAC;QACxB;QACA,OAAO,IAAI,CAAC,MAAM,IAAI;YAAC;SAAoB;IAC/C;IACA,wBAAwB;QACpB,MAAM,cAAc,QAAQ,OAAO,CAAC,OAAO,CAAC,MAAM;QAClD,MAAM,kBAAkB,IAAI,CAAC,8BAA8B,KAAK;QAChE,MAAM,uBAAuB,IAAI,CAAC,oBAAoB,GAChD,IAAI,CAAC,oBAAoB,GACzB;QACN,OAAO,CAAC,QAAQ,EAAE,YAAY,MAAM,EAAE,aAAa,GAAG,CAAC,OAAO,CAAC,yBAAyB,EAAE,qBAAqB,kBAAkB,EAAE,gBAAgB,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,EAAE;IACxM;IACA,cAAc;QACV,OAAO,IAAI,CAAC,QAAQ;IACxB;AACJ;AACA,QAAQ,yBAAyB,GAAG,2BACpC,8CAA8C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3465, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/filesubjecttokensupplier.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FileSubjectTokenSupplier = void 0;\nconst util_1 = require(\"util\");\nconst fs = require(\"fs\");\n// fs.readfile is undefined in browser karma tests causing\n// `npm run browser-test` to fail as test.oauth2.ts imports this file via\n// src/index.ts.\n// Fallback to void function to avoid promisify throwing a TypeError.\nconst readFile = (0, util_1.promisify)(fs.readFile ?? (() => { }));\nconst realpath = (0, util_1.promisify)(fs.realpath ?? (() => { }));\nconst lstat = (0, util_1.promisify)(fs.lstat ?? (() => { }));\n/**\n * Internal subject token supplier implementation used when a file location\n * is configured in the credential configuration used to build an {@link IdentityPoolClient}\n */\nclass FileSubjectTokenSupplier {\n    filePath;\n    formatType;\n    subjectTokenFieldName;\n    /**\n     * Instantiates a new file based subject token supplier.\n     * @param opts The file subject token supplier options to build the supplier\n     *   with.\n     */\n    constructor(opts) {\n        this.filePath = opts.filePath;\n        this.formatType = opts.formatType;\n        this.subjectTokenFieldName = opts.subjectTokenFieldName;\n    }\n    /**\n     * Returns the subject token stored at the file specified in the constructor.\n     * @param context {@link ExternalAccountSupplierContext} from the calling\n     *   {@link IdentityPoolClient}, contains the requested audience and subject\n     *   token type for the external account identity. Not used.\n     */\n    async getSubjectToken() {\n        // Make sure there is a file at the path. lstatSync will throw if there is\n        // nothing there.\n        let parsedFilePath = this.filePath;\n        try {\n            // Resolve path to actual file in case of symlink. Expect a thrown error\n            // if not resolvable.\n            parsedFilePath = await realpath(parsedFilePath);\n            if (!(await lstat(parsedFilePath)).isFile()) {\n                throw new Error();\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                err.message = `The file at ${parsedFilePath} does not exist, or it is not a file. ${err.message}`;\n            }\n            throw err;\n        }\n        let subjectToken;\n        const rawText = await readFile(parsedFilePath, { encoding: 'utf8' });\n        if (this.formatType === 'text') {\n            subjectToken = rawText;\n        }\n        else if (this.formatType === 'json' && this.subjectTokenFieldName) {\n            const json = JSON.parse(rawText);\n            subjectToken = json[this.subjectTokenFieldName];\n        }\n        if (!subjectToken) {\n            throw new Error('Unable to parse the subject_token from the credential_source file');\n        }\n        return subjectToken;\n    }\n}\nexports.FileSubjectTokenSupplier = FileSubjectTokenSupplier;\n//# sourceMappingURL=filesubjecttokensupplier.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,wBAAwB,GAAG,KAAK;AACxC,MAAM;AACN,MAAM;AACN,0DAA0D;AAC1D,yEAAyE;AACzE,gBAAgB;AAChB,qEAAqE;AACrE,MAAM,WAAW,CAAC,GAAG,OAAO,SAAS,EAAE,GAAG,QAAQ,IAAI,CAAC,KAAQ,CAAC;AAChE,MAAM,WAAW,CAAC,GAAG,OAAO,SAAS,EAAE,GAAG,QAAQ,IAAI,CAAC,KAAQ,CAAC;AAChE,MAAM,QAAQ,CAAC,GAAG,OAAO,SAAS,EAAE,GAAG,KAAK,IAAI,CAAC,KAAQ,CAAC;AAC1D;;;CAGC,GACD,MAAM;IACF,SAAS;IACT,WAAW;IACX,sBAAsB;IACtB;;;;KAIC,GACD,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;QAC7B,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;QACjC,IAAI,CAAC,qBAAqB,GAAG,KAAK,qBAAqB;IAC3D;IACA;;;;;KAKC,GACD,MAAM,kBAAkB;QACpB,0EAA0E;QAC1E,iBAAiB;QACjB,IAAI,iBAAiB,IAAI,CAAC,QAAQ;QAClC,IAAI;YACA,wEAAwE;YACxE,qBAAqB;YACrB,iBAAiB,MAAM,SAAS;YAChC,IAAI,CAAC,CAAC,MAAM,MAAM,eAAe,EAAE,MAAM,IAAI;gBACzC,MAAM,IAAI;YACd;QACJ,EACA,OAAO,KAAK;YACR,IAAI,eAAe,OAAO;gBACtB,IAAI,OAAO,GAAG,CAAC,YAAY,EAAE,eAAe,sCAAsC,EAAE,IAAI,OAAO,EAAE;YACrG;YACA,MAAM;QACV;QACA,IAAI;QACJ,MAAM,UAAU,MAAM,SAAS,gBAAgB;YAAE,UAAU;QAAO;QAClE,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC5B,eAAe;QACnB,OACK,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,CAAC,qBAAqB,EAAE;YAC/D,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,eAAe,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACnD;QACA,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;IACX;AACJ;AACA,QAAQ,wBAAwB,GAAG,0BACnC,oDAAoD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/urlsubjecttokensupplier.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UrlSubjectTokenSupplier = void 0;\nconst authclient_1 = require(\"./authclient\");\n/**\n * Internal subject token supplier implementation used when a URL\n * is configured in the credential configuration used to build an {@link IdentityPoolClient}\n */\nclass UrlSubjectTokenSupplier {\n    url;\n    headers;\n    formatType;\n    subjectTokenFieldName;\n    additionalGaxiosOptions;\n    /**\n     * Instantiates a URL subject token supplier.\n     * @param opts The URL subject token supplier options to build the supplier with.\n     */\n    constructor(opts) {\n        this.url = opts.url;\n        this.formatType = opts.formatType;\n        this.subjectTokenFieldName = opts.subjectTokenFieldName;\n        this.headers = opts.headers;\n        this.additionalGaxiosOptions = opts.additionalGaxiosOptions;\n    }\n    /**\n     * Sends a GET request to the URL provided in the constructor and resolves\n     * with the returned external subject token.\n     * @param context {@link ExternalAccountSupplierContext} from the calling\n     *   {@link IdentityPoolClient}, contains the requested audience and subject\n     *   token type for the external account identity. Not used.\n     */\n    async getSubjectToken(context) {\n        const opts = {\n            ...this.additionalGaxiosOptions,\n            url: this.url,\n            method: 'GET',\n            headers: this.headers,\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'getSubjectToken');\n        let subjectToken;\n        if (this.formatType === 'text') {\n            const response = await context.transporter.request(opts);\n            subjectToken = response.data;\n        }\n        else if (this.formatType === 'json' && this.subjectTokenFieldName) {\n            const response = await context.transporter.request(opts);\n            subjectToken = response.data[this.subjectTokenFieldName];\n        }\n        if (!subjectToken) {\n            throw new Error('Unable to parse the subject_token from the credential_source URL');\n        }\n        return subjectToken;\n    }\n}\nexports.UrlSubjectTokenSupplier = UrlSubjectTokenSupplier;\n//# sourceMappingURL=urlsubjecttokensupplier.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,uBAAuB,GAAG,KAAK;AACvC,MAAM;AACN;;;CAGC,GACD,MAAM;IACF,IAAI;IACJ,QAAQ;IACR,WAAW;IACX,sBAAsB;IACtB,wBAAwB;IACxB;;;KAGC,GACD,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;QACjC,IAAI,CAAC,qBAAqB,GAAG,KAAK,qBAAqB;QACvD,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,uBAAuB,GAAG,KAAK,uBAAuB;IAC/D;IACA;;;;;;KAMC,GACD,MAAM,gBAAgB,OAAO,EAAE;QAC3B,MAAM,OAAO;YACT,GAAG,IAAI,CAAC,uBAAuB;YAC/B,KAAK,IAAI,CAAC,GAAG;YACb,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;QACzB;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,IAAI;QACJ,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC5B,MAAM,WAAW,MAAM,QAAQ,WAAW,CAAC,OAAO,CAAC;YACnD,eAAe,SAAS,IAAI;QAChC,OACK,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,CAAC,qBAAqB,EAAE;YAC/D,MAAM,WAAW,MAAM,QAAQ,WAAW,CAAC,OAAO,CAAC;YACnD,eAAe,SAAS,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;QAC5D;QACA,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;IACX;AACJ;AACA,QAAQ,uBAAuB,GAAG,yBAClC,mDAAmD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3624, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/certificatesubjecttokensupplier.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2025 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CertificateSubjectTokenSupplier = exports.InvalidConfigurationError = exports.CertificateSourceUnavailableError = exports.CERTIFICATE_CONFIGURATION_ENV_VARIABLE = void 0;\nconst util_1 = require(\"../util\");\nconst fs = require(\"fs\");\nconst crypto_1 = require(\"crypto\");\nconst https = require(\"https\");\nexports.CERTIFICATE_CONFIGURATION_ENV_VARIABLE = 'GOOGLE_API_CERTIFICATE_CONFIG';\n/**\n * Thrown when the certificate source cannot be located or accessed.\n */\nclass CertificateSourceUnavailableError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = 'CertificateSourceUnavailableError';\n    }\n}\nexports.CertificateSourceUnavailableError = CertificateSourceUnavailableError;\n/**\n * Thrown for invalid configuration that is not related to file availability.\n */\nclass InvalidConfigurationError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = 'InvalidConfigurationError';\n    }\n}\nexports.InvalidConfigurationError = InvalidConfigurationError;\n/**\n * A subject token supplier that uses a client certificate for authentication.\n * It provides the certificate chain as the subject token for identity federation.\n */\nclass CertificateSubjectTokenSupplier {\n    certificateConfigPath;\n    trustChainPath;\n    cert;\n    key;\n    /**\n     * Initializes a new instance of the CertificateSubjectTokenSupplier.\n     * @param opts The configuration options for the supplier.\n     */\n    constructor(opts) {\n        if (!opts.useDefaultCertificateConfig && !opts.certificateConfigLocation) {\n            throw new InvalidConfigurationError('Either `useDefaultCertificateConfig` must be true or a `certificateConfigLocation` must be provided.');\n        }\n        if (opts.useDefaultCertificateConfig && opts.certificateConfigLocation) {\n            throw new InvalidConfigurationError('Both `useDefaultCertificateConfig` and `certificateConfigLocation` cannot be provided.');\n        }\n        this.trustChainPath = opts.trustChainPath;\n        this.certificateConfigPath = opts.certificateConfigLocation ?? '';\n    }\n    /**\n     * Creates an HTTPS agent configured with the client certificate and private key for mTLS.\n     * @returns An mTLS-configured https.Agent.\n     */\n    async createMtlsHttpsAgent() {\n        if (!this.key || !this.cert) {\n            throw new InvalidConfigurationError('Cannot create mTLS Agent with missing certificate or key');\n        }\n        return new https.Agent({ key: this.key, cert: this.cert });\n    }\n    /**\n     * Constructs the subject token, which is the base64-encoded certificate chain.\n     * @returns A promise that resolves with the subject token.\n     */\n    async getSubjectToken() {\n        // The \"subject token\" in this context is the processed certificate chain.\n        this.certificateConfigPath = await this.#resolveCertificateConfigFilePath();\n        const { certPath, keyPath } = await this.#getCertAndKeyPaths();\n        ({ cert: this.cert, key: this.key } = await this.#getKeyAndCert(certPath, keyPath));\n        return await this.#processChainFromPaths(this.cert);\n    }\n    /**\n     * Resolves the absolute path to the certificate configuration file\n     * by checking the \"certificate_config_location\" provided in the ADC file,\n     * or the \"GOOGLE_API_CERTIFICATE_CONFIG\" environment variable\n     * or in the default gcloud path.\n     * @param overridePath An optional path to check first.\n     * @returns The resolved file path.\n     */\n    async #resolveCertificateConfigFilePath() {\n        // 1. Check for the override path from constructor options.\n        const overridePath = this.certificateConfigPath;\n        if (overridePath) {\n            if (await (0, util_1.isValidFile)(overridePath)) {\n                return overridePath;\n            }\n            throw new CertificateSourceUnavailableError(`Provided certificate config path is invalid: ${overridePath}`);\n        }\n        // 2. Check the standard environment variable.\n        const envPath = process.env[exports.CERTIFICATE_CONFIGURATION_ENV_VARIABLE];\n        if (envPath) {\n            if (await (0, util_1.isValidFile)(envPath)) {\n                return envPath;\n            }\n            throw new CertificateSourceUnavailableError(`Path from environment variable \"${exports.CERTIFICATE_CONFIGURATION_ENV_VARIABLE}\" is invalid: ${envPath}`);\n        }\n        // 3. Check the well-known gcloud config location.\n        const wellKnownPath = (0, util_1.getWellKnownCertificateConfigFileLocation)();\n        if (await (0, util_1.isValidFile)(wellKnownPath)) {\n            return wellKnownPath;\n        }\n        // 4. If none are found, throw an error.\n        throw new CertificateSourceUnavailableError('Could not find certificate configuration file. Searched override path, ' +\n            `the \"${exports.CERTIFICATE_CONFIGURATION_ENV_VARIABLE}\" env var, and the gcloud path (${wellKnownPath}).`);\n    }\n    /**\n     * Reads and parses the certificate config JSON file to extract the certificate and key paths.\n     * @returns An object containing the certificate and key paths.\n     */\n    async #getCertAndKeyPaths() {\n        const configPath = this.certificateConfigPath;\n        let fileContents;\n        try {\n            fileContents = await fs.promises.readFile(configPath, 'utf8');\n        }\n        catch (err) {\n            throw new CertificateSourceUnavailableError(`Failed to read certificate config file at: ${configPath}`);\n        }\n        try {\n            const config = JSON.parse(fileContents);\n            const certPath = config?.cert_configs?.workload?.cert_path;\n            const keyPath = config?.cert_configs?.workload?.key_path;\n            if (!certPath || !keyPath) {\n                throw new InvalidConfigurationError(`Certificate config file (${configPath}) is missing required \"cert_path\" or \"key_path\" in the workload config.`);\n            }\n            return { certPath, keyPath };\n        }\n        catch (e) {\n            if (e instanceof InvalidConfigurationError)\n                throw e;\n            throw new InvalidConfigurationError(`Failed to parse certificate config from ${configPath}: ${e.message}`);\n        }\n    }\n    /**\n     * Reads and parses the cert and key files get their content and check valid format.\n     * @returns An object containing the cert content and key content in buffer format.\n     */\n    async #getKeyAndCert(certPath, keyPath) {\n        let cert, key;\n        try {\n            cert = await fs.promises.readFile(certPath);\n            new crypto_1.X509Certificate(cert);\n        }\n        catch (err) {\n            const message = err instanceof Error ? err.message : String(err);\n            throw new CertificateSourceUnavailableError(`Failed to read certificate file at ${certPath}: ${message}`);\n        }\n        try {\n            key = await fs.promises.readFile(keyPath);\n            (0, crypto_1.createPrivateKey)(key);\n        }\n        catch (err) {\n            const message = err instanceof Error ? err.message : String(err);\n            throw new CertificateSourceUnavailableError(`Failed to read private key file at ${keyPath}: ${message}`);\n        }\n        return { cert, key };\n    }\n    /**\n     * Reads the leaf certificate and trust chain, combines them,\n     * and returns a JSON array of base64-encoded certificates.\n     * @returns A stringified JSON array of the certificate chain.\n     */\n    async #processChainFromPaths(leafCertBuffer) {\n        const leafCert = new crypto_1.X509Certificate(leafCertBuffer);\n        // If no trust chain is provided, just use the successfully parsed leaf certificate.\n        if (!this.trustChainPath) {\n            return JSON.stringify([leafCert.raw.toString('base64')]);\n        }\n        // Handle the trust chain logic.\n        try {\n            const chainPems = await fs.promises.readFile(this.trustChainPath, 'utf8');\n            const pemBlocks = chainPems.match(/-----BEGIN CERTIFICATE-----[^-]+-----END CERTIFICATE-----/g) ?? [];\n            const chainCerts = pemBlocks.map((pem, index) => {\n                try {\n                    return new crypto_1.X509Certificate(pem);\n                }\n                catch (err) {\n                    const message = err instanceof Error ? err.message : String(err);\n                    // Throw a more precise error if a single certificate in the chain is invalid.\n                    throw new InvalidConfigurationError(`Failed to parse certificate at index ${index} in trust chain file ${this.trustChainPath}: ${message}`);\n                }\n            });\n            const leafIndex = chainCerts.findIndex(chainCert => leafCert.raw.equals(chainCert.raw));\n            let finalChain;\n            if (leafIndex === -1) {\n                // Leaf not found, so prepend it to the chain.\n                finalChain = [leafCert, ...chainCerts];\n            }\n            else if (leafIndex === 0) {\n                // Leaf is already the first element, so the chain is correctly ordered.\n                finalChain = chainCerts;\n            }\n            else {\n                // Leaf is in the chain but not at the top, which is invalid.\n                throw new InvalidConfigurationError(`Leaf certificate exists in the trust chain but is not the first entry (found at index ${leafIndex}).`);\n            }\n            return JSON.stringify(finalChain.map(cert => cert.raw.toString('base64')));\n        }\n        catch (err) {\n            // Re-throw our specific configuration errors.\n            if (err instanceof InvalidConfigurationError)\n                throw err;\n            const message = err instanceof Error ? err.message : String(err);\n            throw new CertificateSourceUnavailableError(`Failed to process certificate chain from ${this.trustChainPath}: ${message}`);\n        }\n    }\n}\nexports.CertificateSubjectTokenSupplier = CertificateSubjectTokenSupplier;\n//# sourceMappingURL=certificatesubjecttokensupplier.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,+BAA+B,GAAG,QAAQ,yBAAyB,GAAG,QAAQ,iCAAiC,GAAG,QAAQ,sCAAsC,GAAG,KAAK;AAChL,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,sCAAsC,GAAG;AACjD;;CAEC,GACD,MAAM,0CAA0C;IAC5C,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,QAAQ,iCAAiC,GAAG;AAC5C;;CAEC,GACD,MAAM,kCAAkC;IACpC,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,QAAQ,yBAAyB,GAAG;AACpC;;;CAGC,GACD,MAAM;IACF,sBAAsB;IACtB,eAAe;IACf,KAAK;IACL,IAAI;IACJ;;;KAGC,GACD,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,KAAK,2BAA2B,IAAI,CAAC,KAAK,yBAAyB,EAAE;YACtE,MAAM,IAAI,0BAA0B;QACxC;QACA,IAAI,KAAK,2BAA2B,IAAI,KAAK,yBAAyB,EAAE;YACpE,MAAM,IAAI,0BAA0B;QACxC;QACA,IAAI,CAAC,cAAc,GAAG,KAAK,cAAc;QACzC,IAAI,CAAC,qBAAqB,GAAG,KAAK,yBAAyB,IAAI;IACnE;IACA;;;KAGC,GACD,MAAM,uBAAuB;QACzB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACzB,MAAM,IAAI,0BAA0B;QACxC;QACA,OAAO,IAAI,MAAM,KAAK,CAAC;YAAE,KAAK,IAAI,CAAC,GAAG;YAAE,MAAM,IAAI,CAAC,IAAI;QAAC;IAC5D;IACA;;;KAGC,GACD,MAAM,kBAAkB;QACpB,0EAA0E;QAC1E,IAAI,CAAC,qBAAqB,GAAG,MAAM,IAAI,CAAC,CAAA,gCAAiC;QACzE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,CAAA,kBAAmB;QAC5D,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,IAAI,CAAC,CAAA,aAAc,CAAC,UAAU,QAAQ;QAClF,OAAO,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC,IAAI,CAAC,IAAI;IACtD;IACA;;;;;;;KAOC,GACD,MAAM,CAAA,gCAAiC;QACnC,2DAA2D;QAC3D,MAAM,eAAe,IAAI,CAAC,qBAAqB;QAC/C,IAAI,cAAc;YACd,IAAI,MAAM,CAAC,GAAG,OAAO,WAAW,EAAE,eAAe;gBAC7C,OAAO;YACX;YACA,MAAM,IAAI,kCAAkC,CAAC,6CAA6C,EAAE,cAAc;QAC9G;QACA,8CAA8C;QAC9C,MAAM,UAAU,QAAQ,GAAG,CAAC,QAAQ,sCAAsC,CAAC;QAC3E,IAAI,SAAS;YACT,IAAI,MAAM,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU;gBACxC,OAAO;YACX;YACA,MAAM,IAAI,kCAAkC,CAAC,gCAAgC,EAAE,QAAQ,sCAAsC,CAAC,cAAc,EAAE,SAAS;QAC3J;QACA,kDAAkD;QAClD,MAAM,gBAAgB,CAAC,GAAG,OAAO,yCAAyC;QAC1E,IAAI,MAAM,CAAC,GAAG,OAAO,WAAW,EAAE,gBAAgB;YAC9C,OAAO;QACX;QACA,wCAAwC;QACxC,MAAM,IAAI,kCAAkC,4EACxC,CAAC,KAAK,EAAE,QAAQ,sCAAsC,CAAC,gCAAgC,EAAE,cAAc,EAAE,CAAC;IAClH;IACA;;;KAGC,GACD,MAAM,CAAA,kBAAmB;QACrB,MAAM,aAAa,IAAI,CAAC,qBAAqB;QAC7C,IAAI;QACJ,IAAI;YACA,eAAe,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,YAAY;QAC1D,EACA,OAAO,KAAK;YACR,MAAM,IAAI,kCAAkC,CAAC,2CAA2C,EAAE,YAAY;QAC1G;QACA,IAAI;YACA,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,MAAM,WAAW,QAAQ,cAAc,UAAU;YACjD,MAAM,UAAU,QAAQ,cAAc,UAAU;YAChD,IAAI,CAAC,YAAY,CAAC,SAAS;gBACvB,MAAM,IAAI,0BAA0B,CAAC,yBAAyB,EAAE,WAAW,uEAAuE,CAAC;YACvJ;YACA,OAAO;gBAAE;gBAAU;YAAQ;QAC/B,EACA,OAAO,GAAG;YACN,IAAI,aAAa,2BACb,MAAM;YACV,MAAM,IAAI,0BAA0B,CAAC,wCAAwC,EAAE,WAAW,EAAE,EAAE,EAAE,OAAO,EAAE;QAC7G;IACJ;IACA;;;KAGC,GACD,MAAM,CAAA,aAAc,CAAC,QAAQ,EAAE,OAAO;QAClC,IAAI,MAAM;QACV,IAAI;YACA,OAAO,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAClC,IAAI,SAAS,eAAe,CAAC;QACjC,EACA,OAAO,KAAK;YACR,MAAM,UAAU,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YAC5D,MAAM,IAAI,kCAAkC,CAAC,mCAAmC,EAAE,SAAS,EAAE,EAAE,SAAS;QAC5G;QACA,IAAI;YACA,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC;YACjC,CAAC,GAAG,SAAS,gBAAgB,EAAE;QACnC,EACA,OAAO,KAAK;YACR,MAAM,UAAU,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YAC5D,MAAM,IAAI,kCAAkC,CAAC,mCAAmC,EAAE,QAAQ,EAAE,EAAE,SAAS;QAC3G;QACA,OAAO;YAAE;YAAM;QAAI;IACvB;IACA;;;;KAIC,GACD,MAAM,CAAA,qBAAsB,CAAC,cAAc;QACvC,MAAM,WAAW,IAAI,SAAS,eAAe,CAAC;QAC9C,oFAAoF;QACpF,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,KAAK,SAAS,CAAC;gBAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;aAAU;QAC3D;QACA,gCAAgC;QAChC,IAAI;YACA,MAAM,YAAY,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE;YAClE,MAAM,YAAY,UAAU,KAAK,CAAC,iEAAiE,EAAE;YACrG,MAAM,aAAa,UAAU,GAAG,CAAC,CAAC,KAAK;gBACnC,IAAI;oBACA,OAAO,IAAI,SAAS,eAAe,CAAC;gBACxC,EACA,OAAO,KAAK;oBACR,MAAM,UAAU,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;oBAC5D,8EAA8E;oBAC9E,MAAM,IAAI,0BAA0B,CAAC,qCAAqC,EAAE,MAAM,qBAAqB,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS;gBAC9I;YACJ;YACA,MAAM,YAAY,WAAW,SAAS,CAAC,CAAA,YAAa,SAAS,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG;YACrF,IAAI;YACJ,IAAI,cAAc,CAAC,GAAG;gBAClB,8CAA8C;gBAC9C,aAAa;oBAAC;uBAAa;iBAAW;YAC1C,OACK,IAAI,cAAc,GAAG;gBACtB,wEAAwE;gBACxE,aAAa;YACjB,OACK;gBACD,6DAA6D;gBAC7D,MAAM,IAAI,0BAA0B,CAAC,sFAAsF,EAAE,UAAU,EAAE,CAAC;YAC9I;YACA,OAAO,KAAK,SAAS,CAAC,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC;QACnE,EACA,OAAO,KAAK;YACR,8CAA8C;YAC9C,IAAI,eAAe,2BACf,MAAM;YACV,MAAM,UAAU,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YAC5D,MAAM,IAAI,kCAAkC,CAAC,yCAAyC,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS;QAC7H;IACJ;AACJ;AACA,QAAQ,+BAA+B,GAAG,iCAC1C,2DAA2D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3846, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/identitypoolclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.IdentityPoolClient = void 0;\nconst baseexternalclient_1 = require(\"./baseexternalclient\");\nconst util_1 = require(\"../util\");\nconst filesubjecttokensupplier_1 = require(\"./filesubjecttokensupplier\");\nconst urlsubjecttokensupplier_1 = require(\"./urlsubjecttokensupplier\");\nconst certificatesubjecttokensupplier_1 = require(\"./certificatesubjecttokensupplier\");\nconst stscredentials_1 = require(\"./stscredentials\");\nconst gaxios_1 = require(\"gaxios\");\n/**\n * Defines the Url-sourced and file-sourced external account clients mainly\n * used for K8s and Azure workloads.\n */\nclass IdentityPoolClient extends baseexternalclient_1.BaseExternalAccountClient {\n    subjectTokenSupplier;\n    /**\n     * Instantiate an IdentityPoolClient instance using the provided JSON\n     * object loaded from an external account credentials file.\n     * An error is thrown if the credential is not a valid file-sourced or\n     * url-sourced credential or a workforce pool user project is provided\n     * with a non workforce audience.\n     * @param options The external account options object typically loaded\n     *   from the external account JSON credential file. The camelCased options\n     *   are aliases for the snake_cased options.\n     */\n    constructor(options) {\n        super(options);\n        const opts = (0, util_1.originalOrCamelOptions)(options);\n        const credentialSource = opts.get('credential_source');\n        const subjectTokenSupplier = opts.get('subject_token_supplier');\n        // Validate credential sourcing configuration.\n        if (!credentialSource && !subjectTokenSupplier) {\n            throw new Error('A credential source or subject token supplier must be specified.');\n        }\n        if (credentialSource && subjectTokenSupplier) {\n            throw new Error('Only one of credential source or subject token supplier can be specified.');\n        }\n        if (subjectTokenSupplier) {\n            this.subjectTokenSupplier = subjectTokenSupplier;\n            this.credentialSourceType = 'programmatic';\n        }\n        else {\n            const credentialSourceOpts = (0, util_1.originalOrCamelOptions)(credentialSource);\n            const formatOpts = (0, util_1.originalOrCamelOptions)(credentialSourceOpts.get('format'));\n            // Text is the default format type.\n            const formatType = formatOpts.get('type') || 'text';\n            const formatSubjectTokenFieldName = formatOpts.get('subject_token_field_name');\n            if (formatType !== 'json' && formatType !== 'text') {\n                throw new Error(`Invalid credential_source format \"${formatType}\"`);\n            }\n            if (formatType === 'json' && !formatSubjectTokenFieldName) {\n                throw new Error('Missing subject_token_field_name for JSON credential_source format');\n            }\n            const file = credentialSourceOpts.get('file');\n            const url = credentialSourceOpts.get('url');\n            const certificate = credentialSourceOpts.get('certificate');\n            const headers = credentialSourceOpts.get('headers');\n            if ((file && url) || (url && certificate) || (file && certificate)) {\n                throw new Error('No valid Identity Pool \"credential_source\" provided, must be either file, url, or certificate.');\n            }\n            else if (file) {\n                this.credentialSourceType = 'file';\n                this.subjectTokenSupplier = new filesubjecttokensupplier_1.FileSubjectTokenSupplier({\n                    filePath: file,\n                    formatType: formatType,\n                    subjectTokenFieldName: formatSubjectTokenFieldName,\n                });\n            }\n            else if (url) {\n                this.credentialSourceType = 'url';\n                this.subjectTokenSupplier = new urlsubjecttokensupplier_1.UrlSubjectTokenSupplier({\n                    url: url,\n                    formatType: formatType,\n                    subjectTokenFieldName: formatSubjectTokenFieldName,\n                    headers: headers,\n                    additionalGaxiosOptions: IdentityPoolClient.RETRY_CONFIG,\n                });\n            }\n            else if (certificate) {\n                this.credentialSourceType = 'certificate';\n                const certificateSubjecttokensupplier = new certificatesubjecttokensupplier_1.CertificateSubjectTokenSupplier({\n                    useDefaultCertificateConfig: certificate.use_default_certificate_config,\n                    certificateConfigLocation: certificate.certificate_config_location,\n                    trustChainPath: certificate.trust_chain_path,\n                });\n                this.subjectTokenSupplier = certificateSubjecttokensupplier;\n            }\n            else {\n                throw new Error('No valid Identity Pool \"credential_source\" provided, must be either file, url, or certificate.');\n            }\n        }\n    }\n    /**\n     * Triggered when a external subject token is needed to be exchanged for a GCP\n     * access token via GCP STS endpoint. Gets a subject token by calling\n     * the configured {@link SubjectTokenSupplier}\n     * @return A promise that resolves with the external subject token.\n     */\n    async retrieveSubjectToken() {\n        const subjectToken = await this.subjectTokenSupplier.getSubjectToken(this.supplierContext);\n        if (this.subjectTokenSupplier instanceof certificatesubjecttokensupplier_1.CertificateSubjectTokenSupplier) {\n            const mtlsAgent = await this.subjectTokenSupplier.createMtlsHttpsAgent();\n            this.stsCredential = new stscredentials_1.StsCredentials({\n                tokenExchangeEndpoint: this.getTokenUrl(),\n                clientAuthentication: this.clientAuth,\n                transporter: new gaxios_1.Gaxios({ agent: mtlsAgent }),\n            });\n            this.transporter = new gaxios_1.Gaxios({\n                ...(this.transporter.defaults || {}),\n                agent: mtlsAgent,\n            });\n        }\n        return subjectToken;\n    }\n}\nexports.IdentityPoolClient = IdentityPoolClient;\n//# sourceMappingURL=identitypoolclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG,KAAK;AAClC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;CAGC,GACD,MAAM,2BAA2B,qBAAqB,yBAAyB;IAC3E,qBAAqB;IACrB;;;;;;;;;KASC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,MAAM,OAAO,CAAC,GAAG,OAAO,sBAAsB,EAAE;QAChD,MAAM,mBAAmB,KAAK,GAAG,CAAC;QAClC,MAAM,uBAAuB,KAAK,GAAG,CAAC;QACtC,8CAA8C;QAC9C,IAAI,CAAC,oBAAoB,CAAC,sBAAsB;YAC5C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,oBAAoB,sBAAsB;YAC1C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,sBAAsB;YACtB,IAAI,CAAC,oBAAoB,GAAG;YAC5B,IAAI,CAAC,oBAAoB,GAAG;QAChC,OACK;YACD,MAAM,uBAAuB,CAAC,GAAG,OAAO,sBAAsB,EAAE;YAChE,MAAM,aAAa,CAAC,GAAG,OAAO,sBAAsB,EAAE,qBAAqB,GAAG,CAAC;YAC/E,mCAAmC;YACnC,MAAM,aAAa,WAAW,GAAG,CAAC,WAAW;YAC7C,MAAM,8BAA8B,WAAW,GAAG,CAAC;YACnD,IAAI,eAAe,UAAU,eAAe,QAAQ;gBAChD,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC;YACtE;YACA,IAAI,eAAe,UAAU,CAAC,6BAA6B;gBACvD,MAAM,IAAI,MAAM;YACpB;YACA,MAAM,OAAO,qBAAqB,GAAG,CAAC;YACtC,MAAM,MAAM,qBAAqB,GAAG,CAAC;YACrC,MAAM,cAAc,qBAAqB,GAAG,CAAC;YAC7C,MAAM,UAAU,qBAAqB,GAAG,CAAC;YACzC,IAAI,AAAC,QAAQ,OAAS,OAAO,eAAiB,QAAQ,aAAc;gBAChE,MAAM,IAAI,MAAM;YACpB,OACK,IAAI,MAAM;gBACX,IAAI,CAAC,oBAAoB,GAAG;gBAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,2BAA2B,wBAAwB,CAAC;oBAChF,UAAU;oBACV,YAAY;oBACZ,uBAAuB;gBAC3B;YACJ,OACK,IAAI,KAAK;gBACV,IAAI,CAAC,oBAAoB,GAAG;gBAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,0BAA0B,uBAAuB,CAAC;oBAC9E,KAAK;oBACL,YAAY;oBACZ,uBAAuB;oBACvB,SAAS;oBACT,yBAAyB,mBAAmB,YAAY;gBAC5D;YACJ,OACK,IAAI,aAAa;gBAClB,IAAI,CAAC,oBAAoB,GAAG;gBAC5B,MAAM,kCAAkC,IAAI,kCAAkC,+BAA+B,CAAC;oBAC1G,6BAA6B,YAAY,8BAA8B;oBACvE,2BAA2B,YAAY,2BAA2B;oBAClE,gBAAgB,YAAY,gBAAgB;gBAChD;gBACA,IAAI,CAAC,oBAAoB,GAAG;YAChC,OACK;gBACD,MAAM,IAAI,MAAM;YACpB;QACJ;IACJ;IACA;;;;;KAKC,GACD,MAAM,uBAAuB;QACzB,MAAM,eAAe,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe;QACzF,IAAI,IAAI,CAAC,oBAAoB,YAAY,kCAAkC,+BAA+B,EAAE;YACxG,MAAM,YAAY,MAAM,IAAI,CAAC,oBAAoB,CAAC,oBAAoB;YACtE,IAAI,CAAC,aAAa,GAAG,IAAI,iBAAiB,cAAc,CAAC;gBACrD,uBAAuB,IAAI,CAAC,WAAW;gBACvC,sBAAsB,IAAI,CAAC,UAAU;gBACrC,aAAa,IAAI,SAAS,MAAM,CAAC;oBAAE,OAAO;gBAAU;YACxD;YACA,IAAI,CAAC,WAAW,GAAG,IAAI,SAAS,MAAM,CAAC;gBACnC,GAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,CAAC;gBACnC,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ;AACA,QAAQ,kBAAkB,GAAG,oBAC7B,8CAA8C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3977, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/awsrequestsigner.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AwsRequestSigner = void 0;\nconst gaxios_1 = require(\"gaxios\");\nconst crypto_1 = require(\"../crypto/crypto\");\n/** AWS Signature Version 4 signing algorithm identifier.  */\nconst AWS_ALGORITHM = 'AWS4-HMAC-SHA256';\n/**\n * The termination string for the AWS credential scope value as defined in\n * https://docs.aws.amazon.com/general/latest/gr/sigv4-create-string-to-sign.html\n */\nconst AWS_REQUEST_TYPE = 'aws4_request';\n/**\n * Implements an AWS API request signer based on the AWS Signature Version 4\n * signing process.\n * https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\n */\nclass AwsRequestSigner {\n    getCredentials;\n    region;\n    crypto;\n    /**\n     * Instantiates an AWS API request signer used to send authenticated signed\n     * requests to AWS APIs based on the AWS Signature Version 4 signing process.\n     * This also provides a mechanism to generate the signed request without\n     * sending it.\n     * @param getCredentials A mechanism to retrieve AWS security credentials\n     *   when needed.\n     * @param region The AWS region to use.\n     */\n    constructor(getCredentials, region) {\n        this.getCredentials = getCredentials;\n        this.region = region;\n        this.crypto = (0, crypto_1.createCrypto)();\n    }\n    /**\n     * Generates the signed request for the provided HTTP request for calling\n     * an AWS API. This follows the steps described at:\n     * https://docs.aws.amazon.com/general/latest/gr/sigv4_signing.html\n     * @param amzOptions The AWS request options that need to be signed.\n     * @return A promise that resolves with the GaxiosOptions containing the\n     *   signed HTTP request parameters.\n     */\n    async getRequestOptions(amzOptions) {\n        if (!amzOptions.url) {\n            throw new RangeError('\"url\" is required in \"amzOptions\"');\n        }\n        // Stringify JSON requests. This will be set in the request body of the\n        // generated signed request.\n        const requestPayloadData = typeof amzOptions.data === 'object'\n            ? JSON.stringify(amzOptions.data)\n            : amzOptions.data;\n        const url = amzOptions.url;\n        const method = amzOptions.method || 'GET';\n        const requestPayload = amzOptions.body || requestPayloadData;\n        const additionalAmzHeaders = amzOptions.headers;\n        const awsSecurityCredentials = await this.getCredentials();\n        const uri = new URL(url);\n        if (typeof requestPayload !== 'string' && requestPayload !== undefined) {\n            throw new TypeError(`'requestPayload' is expected to be a string if provided. Got: ${requestPayload}`);\n        }\n        const headerMap = await generateAuthenticationHeaderMap({\n            crypto: this.crypto,\n            host: uri.host,\n            canonicalUri: uri.pathname,\n            canonicalQuerystring: uri.search.slice(1),\n            method,\n            region: this.region,\n            securityCredentials: awsSecurityCredentials,\n            requestPayload,\n            additionalAmzHeaders,\n        });\n        // Append additional optional headers, eg. X-Amz-Target, Content-Type, etc.\n        const headers = gaxios_1.Gaxios.mergeHeaders(\n        // Add x-amz-date if available.\n        headerMap.amzDate ? { 'x-amz-date': headerMap.amzDate } : {}, {\n            authorization: headerMap.authorizationHeader,\n            host: uri.host,\n        }, additionalAmzHeaders || {});\n        if (awsSecurityCredentials.token) {\n            gaxios_1.Gaxios.mergeHeaders(headers, {\n                'x-amz-security-token': awsSecurityCredentials.token,\n            });\n        }\n        const awsSignedReq = {\n            url,\n            method: method,\n            headers,\n        };\n        if (requestPayload !== undefined) {\n            awsSignedReq.body = requestPayload;\n        }\n        return awsSignedReq;\n    }\n}\nexports.AwsRequestSigner = AwsRequestSigner;\n/**\n * Creates the HMAC-SHA256 hash of the provided message using the\n * provided key.\n *\n * @param crypto The crypto instance used to facilitate cryptographic\n *   operations.\n * @param key The HMAC-SHA256 key to use.\n * @param msg The message to hash.\n * @return The computed hash bytes.\n */\nasync function sign(crypto, key, msg) {\n    return await crypto.signWithHmacSha256(key, msg);\n}\n/**\n * Calculates the signing key used to calculate the signature for\n * AWS Signature Version 4 based on:\n * https://docs.aws.amazon.com/general/latest/gr/sigv4-calculate-signature.html\n *\n * @param crypto The crypto instance used to facilitate cryptographic\n *   operations.\n * @param key The AWS secret access key.\n * @param dateStamp The '%Y%m%d' date format.\n * @param region The AWS region.\n * @param serviceName The AWS service name, eg. sts.\n * @return The signing key bytes.\n */\nasync function getSigningKey(crypto, key, dateStamp, region, serviceName) {\n    const kDate = await sign(crypto, `AWS4${key}`, dateStamp);\n    const kRegion = await sign(crypto, kDate, region);\n    const kService = await sign(crypto, kRegion, serviceName);\n    const kSigning = await sign(crypto, kService, 'aws4_request');\n    return kSigning;\n}\n/**\n * Generates the authentication header map needed for generating the AWS\n * Signature Version 4 signed request.\n *\n * @param option The options needed to compute the authentication header map.\n * @return The AWS authentication header map which constitutes of the following\n *   components: amz-date, authorization header and canonical query string.\n */\nasync function generateAuthenticationHeaderMap(options) {\n    const additionalAmzHeaders = gaxios_1.Gaxios.mergeHeaders(options.additionalAmzHeaders);\n    const requestPayload = options.requestPayload || '';\n    // iam.amazonaws.com host => iam service.\n    // sts.us-east-2.amazonaws.com => sts service.\n    const serviceName = options.host.split('.')[0];\n    const now = new Date();\n    // Format: '%Y%m%dT%H%M%SZ'.\n    const amzDate = now\n        .toISOString()\n        .replace(/[-:]/g, '')\n        .replace(/\\.[0-9]+/, '');\n    // Format: '%Y%m%d'.\n    const dateStamp = now.toISOString().replace(/[-]/g, '').replace(/T.*/, '');\n    // Add AWS token if available.\n    if (options.securityCredentials.token) {\n        additionalAmzHeaders.set('x-amz-security-token', options.securityCredentials.token);\n    }\n    // Header keys need to be sorted alphabetically.\n    const amzHeaders = gaxios_1.Gaxios.mergeHeaders({\n        host: options.host,\n    }, \n    // Previously the date was not fixed with x-amz- and could be provided manually.\n    // https://github.com/boto/botocore/blob/879f8440a4e9ace5d3cf145ce8b3d5e5ffb892ef/tests/unit/auth/aws4_testsuite/get-header-value-trim.req\n    additionalAmzHeaders.has('date') ? {} : { 'x-amz-date': amzDate }, additionalAmzHeaders);\n    let canonicalHeaders = '';\n    // TypeScript is missing `Headers#keys` at the time of writing\n    const signedHeadersList = [\n        ...amzHeaders.keys(),\n    ].sort();\n    signedHeadersList.forEach(key => {\n        canonicalHeaders += `${key}:${amzHeaders.get(key)}\\n`;\n    });\n    const signedHeaders = signedHeadersList.join(';');\n    const payloadHash = await options.crypto.sha256DigestHex(requestPayload);\n    // https://docs.aws.amazon.com/general/latest/gr/sigv4-create-canonical-request.html\n    const canonicalRequest = `${options.method.toUpperCase()}\\n` +\n        `${options.canonicalUri}\\n` +\n        `${options.canonicalQuerystring}\\n` +\n        `${canonicalHeaders}\\n` +\n        `${signedHeaders}\\n` +\n        `${payloadHash}`;\n    const credentialScope = `${dateStamp}/${options.region}/${serviceName}/${AWS_REQUEST_TYPE}`;\n    // https://docs.aws.amazon.com/general/latest/gr/sigv4-create-string-to-sign.html\n    const stringToSign = `${AWS_ALGORITHM}\\n` +\n        `${amzDate}\\n` +\n        `${credentialScope}\\n` +\n        (await options.crypto.sha256DigestHex(canonicalRequest));\n    // https://docs.aws.amazon.com/general/latest/gr/sigv4-calculate-signature.html\n    const signingKey = await getSigningKey(options.crypto, options.securityCredentials.secretAccessKey, dateStamp, options.region, serviceName);\n    const signature = await sign(options.crypto, signingKey, stringToSign);\n    // https://docs.aws.amazon.com/general/latest/gr/sigv4-add-signature-to-request.html\n    const authorizationHeader = `${AWS_ALGORITHM} Credential=${options.securityCredentials.accessKeyId}/` +\n        `${credentialScope}, SignedHeaders=${signedHeaders}, ` +\n        `Signature=${(0, crypto_1.fromArrayBufferToHex)(signature)}`;\n    return {\n        // Do not return x-amz-date if date is available.\n        amzDate: additionalAmzHeaders.has('date') ? undefined : amzDate,\n        authorizationHeader,\n        canonicalQuerystring: options.canonicalQuerystring,\n    };\n}\n//# sourceMappingURL=awsrequestsigner.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM;AACN,2DAA2D,GAC3D,MAAM,gBAAgB;AACtB;;;CAGC,GACD,MAAM,mBAAmB;AACzB;;;;CAIC,GACD,MAAM;IACF,eAAe;IACf,OAAO;IACP,OAAO;IACP;;;;;;;;KAQC,GACD,YAAY,cAAc,EAAE,MAAM,CAAE;QAChC,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,YAAY;IAC3C;IACA;;;;;;;KAOC,GACD,MAAM,kBAAkB,UAAU,EAAE;QAChC,IAAI,CAAC,WAAW,GAAG,EAAE;YACjB,MAAM,IAAI,WAAW;QACzB;QACA,uEAAuE;QACvE,4BAA4B;QAC5B,MAAM,qBAAqB,OAAO,WAAW,IAAI,KAAK,WAChD,KAAK,SAAS,CAAC,WAAW,IAAI,IAC9B,WAAW,IAAI;QACrB,MAAM,MAAM,WAAW,GAAG;QAC1B,MAAM,SAAS,WAAW,MAAM,IAAI;QACpC,MAAM,iBAAiB,WAAW,IAAI,IAAI;QAC1C,MAAM,uBAAuB,WAAW,OAAO;QAC/C,MAAM,yBAAyB,MAAM,IAAI,CAAC,cAAc;QACxD,MAAM,MAAM,IAAI,IAAI;QACpB,IAAI,OAAO,mBAAmB,YAAY,mBAAmB,WAAW;YACpE,MAAM,IAAI,UAAU,CAAC,8DAA8D,EAAE,gBAAgB;QACzG;QACA,MAAM,YAAY,MAAM,gCAAgC;YACpD,QAAQ,IAAI,CAAC,MAAM;YACnB,MAAM,IAAI,IAAI;YACd,cAAc,IAAI,QAAQ;YAC1B,sBAAsB,IAAI,MAAM,CAAC,KAAK,CAAC;YACvC;YACA,QAAQ,IAAI,CAAC,MAAM;YACnB,qBAAqB;YACrB;YACA;QACJ;QACA,2EAA2E;QAC3E,MAAM,UAAU,SAAS,MAAM,CAAC,YAAY,CAC5C,+BAA+B;QAC/B,UAAU,OAAO,GAAG;YAAE,cAAc,UAAU,OAAO;QAAC,IAAI,CAAC,GAAG;YAC1D,eAAe,UAAU,mBAAmB;YAC5C,MAAM,IAAI,IAAI;QAClB,GAAG,wBAAwB,CAAC;QAC5B,IAAI,uBAAuB,KAAK,EAAE;YAC9B,SAAS,MAAM,CAAC,YAAY,CAAC,SAAS;gBAClC,wBAAwB,uBAAuB,KAAK;YACxD;QACJ;QACA,MAAM,eAAe;YACjB;YACA,QAAQ;YACR;QACJ;QACA,IAAI,mBAAmB,WAAW;YAC9B,aAAa,IAAI,GAAG;QACxB;QACA,OAAO;IACX;AACJ;AACA,QAAQ,gBAAgB,GAAG;AAC3B;;;;;;;;;CASC,GACD,eAAe,KAAK,MAAM,EAAE,GAAG,EAAE,GAAG;IAChC,OAAO,MAAM,OAAO,kBAAkB,CAAC,KAAK;AAChD;AACA;;;;;;;;;;;;CAYC,GACD,eAAe,cAAc,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW;IACpE,MAAM,QAAQ,MAAM,KAAK,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;IAC/C,MAAM,UAAU,MAAM,KAAK,QAAQ,OAAO;IAC1C,MAAM,WAAW,MAAM,KAAK,QAAQ,SAAS;IAC7C,MAAM,WAAW,MAAM,KAAK,QAAQ,UAAU;IAC9C,OAAO;AACX;AACA;;;;;;;CAOC,GACD,eAAe,gCAAgC,OAAO;IAClD,MAAM,uBAAuB,SAAS,MAAM,CAAC,YAAY,CAAC,QAAQ,oBAAoB;IACtF,MAAM,iBAAiB,QAAQ,cAAc,IAAI;IACjD,yCAAyC;IACzC,8CAA8C;IAC9C,MAAM,cAAc,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IAC9C,MAAM,MAAM,IAAI;IAChB,4BAA4B;IAC5B,MAAM,UAAU,IACX,WAAW,GACX,OAAO,CAAC,SAAS,IACjB,OAAO,CAAC,YAAY;IACzB,oBAAoB;IACpB,MAAM,YAAY,IAAI,WAAW,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,OAAO;IACvE,8BAA8B;IAC9B,IAAI,QAAQ,mBAAmB,CAAC,KAAK,EAAE;QACnC,qBAAqB,GAAG,CAAC,wBAAwB,QAAQ,mBAAmB,CAAC,KAAK;IACtF;IACA,gDAAgD;IAChD,MAAM,aAAa,SAAS,MAAM,CAAC,YAAY,CAAC;QAC5C,MAAM,QAAQ,IAAI;IACtB,GACA,gFAAgF;IAChF,0IAA0I;IAC1I,qBAAqB,GAAG,CAAC,UAAU,CAAC,IAAI;QAAE,cAAc;IAAQ,GAAG;IACnE,IAAI,mBAAmB;IACvB,8DAA8D;IAC9D,MAAM,oBAAoB;WACnB,WAAW,IAAI;KACrB,CAAC,IAAI;IACN,kBAAkB,OAAO,CAAC,CAAA;QACtB,oBAAoB,GAAG,IAAI,CAAC,EAAE,WAAW,GAAG,CAAC,KAAK,EAAE,CAAC;IACzD;IACA,MAAM,gBAAgB,kBAAkB,IAAI,CAAC;IAC7C,MAAM,cAAc,MAAM,QAAQ,MAAM,CAAC,eAAe,CAAC;IACzD,oFAAoF;IACpF,MAAM,mBAAmB,GAAG,QAAQ,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,GACxD,GAAG,QAAQ,YAAY,CAAC,EAAE,CAAC,GAC3B,GAAG,QAAQ,oBAAoB,CAAC,EAAE,CAAC,GACnC,GAAG,iBAAiB,EAAE,CAAC,GACvB,GAAG,cAAc,EAAE,CAAC,GACpB,GAAG,aAAa;IACpB,MAAM,kBAAkB,GAAG,UAAU,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,kBAAkB;IAC3F,iFAAiF;IACjF,MAAM,eAAe,GAAG,cAAc,EAAE,CAAC,GACrC,GAAG,QAAQ,EAAE,CAAC,GACd,GAAG,gBAAgB,EAAE,CAAC,GACrB,MAAM,QAAQ,MAAM,CAAC,eAAe,CAAC;IAC1C,+EAA+E;IAC/E,MAAM,aAAa,MAAM,cAAc,QAAQ,MAAM,EAAE,QAAQ,mBAAmB,CAAC,eAAe,EAAE,WAAW,QAAQ,MAAM,EAAE;IAC/H,MAAM,YAAY,MAAM,KAAK,QAAQ,MAAM,EAAE,YAAY;IACzD,oFAAoF;IACpF,MAAM,sBAAsB,GAAG,cAAc,YAAY,EAAE,QAAQ,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,GACjG,GAAG,gBAAgB,gBAAgB,EAAE,cAAc,EAAE,CAAC,GACtD,CAAC,UAAU,EAAE,CAAC,GAAG,SAAS,oBAAoB,EAAE,YAAY;IAChE,OAAO;QACH,iDAAiD;QACjD,SAAS,qBAAqB,GAAG,CAAC,UAAU,YAAY;QACxD;QACA,sBAAsB,QAAQ,oBAAoB;IACtD;AACJ,EACA,4CAA4C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/defaultawssecuritycredentialssupplier.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DefaultAwsSecurityCredentialsSupplier = void 0;\nconst authclient_1 = require(\"./authclient\");\n/**\n * Internal AWS security credentials supplier implementation used by {@link AwsClient}\n * when a credential source is provided instead of a user defined supplier.\n * The logic is summarized as:\n * 1. If imdsv2_session_token_url is provided in the credential source, then\n *    fetch the aws session token and include it in the headers of the\n *    metadata requests. This is a requirement for IDMSv2 but optional\n *    for IDMSv1.\n * 2. Retrieve AWS region from availability-zone.\n * 3a. Check AWS credentials in environment variables. If not found, get\n *     from security-credentials endpoint.\n * 3b. Get AWS credentials from security-credentials endpoint. In order\n *     to retrieve this, the AWS role needs to be determined by calling\n *     security-credentials endpoint without any argument. Then the\n *     credentials can be retrieved via: security-credentials/role_name\n * 4. Generate the signed request to AWS STS GetCallerIdentity action.\n * 5. Inject x-goog-cloud-target-resource into header and serialize the\n *    signed request. This will be the subject-token to pass to GCP STS.\n */\nclass DefaultAwsSecurityCredentialsSupplier {\n    regionUrl;\n    securityCredentialsUrl;\n    imdsV2SessionTokenUrl;\n    additionalGaxiosOptions;\n    /**\n     * Instantiates a new DefaultAwsSecurityCredentialsSupplier using information\n     * from the credential_source stored in the ADC file.\n     * @param opts The default aws security credentials supplier options object to\n     *   build the supplier with.\n     */\n    constructor(opts) {\n        this.regionUrl = opts.regionUrl;\n        this.securityCredentialsUrl = opts.securityCredentialsUrl;\n        this.imdsV2SessionTokenUrl = opts.imdsV2SessionTokenUrl;\n        this.additionalGaxiosOptions = opts.additionalGaxiosOptions;\n    }\n    /**\n     * Returns the active AWS region. This first checks to see if the region\n     * is available as an environment variable. If it is not, then the supplier\n     * will call the region URL.\n     * @param context {@link ExternalAccountSupplierContext} from the calling\n     *   {@link AwsClient}, contains the requested audience and subject token type\n     *   for the external account identity.\n     * @return A promise that resolves with the AWS region string.\n     */\n    async getAwsRegion(context) {\n        // Priority order for region determination:\n        // AWS_REGION > AWS_DEFAULT_REGION > metadata server.\n        if (this.#regionFromEnv) {\n            return this.#regionFromEnv;\n        }\n        const metadataHeaders = new Headers();\n        if (!this.#regionFromEnv && this.imdsV2SessionTokenUrl) {\n            metadataHeaders.set('x-aws-ec2-metadata-token', await this.#getImdsV2SessionToken(context.transporter));\n        }\n        if (!this.regionUrl) {\n            throw new RangeError('Unable to determine AWS region due to missing ' +\n                '\"options.credential_source.region_url\"');\n        }\n        const opts = {\n            ...this.additionalGaxiosOptions,\n            url: this.regionUrl,\n            method: 'GET',\n            headers: metadataHeaders,\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'getAwsRegion');\n        const response = await context.transporter.request(opts);\n        // Remove last character. For example, if us-east-2b is returned,\n        // the region would be us-east-2.\n        return response.data.substr(0, response.data.length - 1);\n    }\n    /**\n     * Returns AWS security credentials. This first checks to see if the credentials\n     * is available as environment variables. If it is not, then the supplier\n     * will call the security credentials URL.\n     * @param context {@link ExternalAccountSupplierContext} from the calling\n     *   {@link AwsClient}, contains the requested audience and subject token type\n     *   for the external account identity.\n     * @return A promise that resolves with the AWS security credentials.\n     */\n    async getAwsSecurityCredentials(context) {\n        // Check environment variables for permanent credentials first.\n        // https://docs.aws.amazon.com/general/latest/gr/aws-sec-cred-types.html\n        if (this.#securityCredentialsFromEnv) {\n            return this.#securityCredentialsFromEnv;\n        }\n        const metadataHeaders = new Headers();\n        if (this.imdsV2SessionTokenUrl) {\n            metadataHeaders.set('x-aws-ec2-metadata-token', await this.#getImdsV2SessionToken(context.transporter));\n        }\n        // Since the role on a VM can change, we don't need to cache it.\n        const roleName = await this.#getAwsRoleName(metadataHeaders, context.transporter);\n        // Temporary credentials typically last for several hours.\n        // Expiration is returned in response.\n        // Consider future optimization of this logic to cache AWS tokens\n        // until their natural expiration.\n        const awsCreds = await this.#retrieveAwsSecurityCredentials(roleName, metadataHeaders, context.transporter);\n        return {\n            accessKeyId: awsCreds.AccessKeyId,\n            secretAccessKey: awsCreds.SecretAccessKey,\n            token: awsCreds.Token,\n        };\n    }\n    /**\n     * @param transporter The transporter to use for requests.\n     * @return A promise that resolves with the IMDSv2 Session Token.\n     */\n    async #getImdsV2SessionToken(transporter) {\n        const opts = {\n            ...this.additionalGaxiosOptions,\n            url: this.imdsV2SessionTokenUrl,\n            method: 'PUT',\n            headers: { 'x-aws-ec2-metadata-token-ttl-seconds': '300' },\n        };\n        authclient_1.AuthClient.setMethodName(opts, '#getImdsV2SessionToken');\n        const response = await transporter.request(opts);\n        return response.data;\n    }\n    /**\n     * @param headers The headers to be used in the metadata request.\n     * @param transporter The transporter to use for requests.\n     * @return A promise that resolves with the assigned role to the current\n     *   AWS VM. This is needed for calling the security-credentials endpoint.\n     */\n    async #getAwsRoleName(headers, transporter) {\n        if (!this.securityCredentialsUrl) {\n            throw new Error('Unable to determine AWS role name due to missing ' +\n                '\"options.credential_source.url\"');\n        }\n        const opts = {\n            ...this.additionalGaxiosOptions,\n            url: this.securityCredentialsUrl,\n            method: 'GET',\n            headers: headers,\n        };\n        authclient_1.AuthClient.setMethodName(opts, '#getAwsRoleName');\n        const response = await transporter.request(opts);\n        return response.data;\n    }\n    /**\n     * Retrieves the temporary AWS credentials by calling the security-credentials\n     * endpoint as specified in the `credential_source` object.\n     * @param roleName The role attached to the current VM.\n     * @param headers The headers to be used in the metadata request.\n     * @param transporter The transporter to use for requests.\n     * @return A promise that resolves with the temporary AWS credentials\n     *   needed for creating the GetCallerIdentity signed request.\n     */\n    async #retrieveAwsSecurityCredentials(roleName, headers, transporter) {\n        const opts = {\n            ...this.additionalGaxiosOptions,\n            url: `${this.securityCredentialsUrl}/${roleName}`,\n            headers: headers,\n        };\n        authclient_1.AuthClient.setMethodName(opts, '#retrieveAwsSecurityCredentials');\n        const response = await transporter.request(opts);\n        return response.data;\n    }\n    get #regionFromEnv() {\n        // The AWS region can be provided through AWS_REGION or AWS_DEFAULT_REGION.\n        // Only one is required.\n        return (process.env['AWS_REGION'] || process.env['AWS_DEFAULT_REGION'] || null);\n    }\n    get #securityCredentialsFromEnv() {\n        // Both AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are required.\n        if (process.env['AWS_ACCESS_KEY_ID'] &&\n            process.env['AWS_SECRET_ACCESS_KEY']) {\n            return {\n                accessKeyId: process.env['AWS_ACCESS_KEY_ID'],\n                secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'],\n                token: process.env['AWS_SESSION_TOKEN'],\n            };\n        }\n        return null;\n    }\n}\nexports.DefaultAwsSecurityCredentialsSupplier = DefaultAwsSecurityCredentialsSupplier;\n//# sourceMappingURL=defaultawssecuritycredentialssupplier.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qCAAqC,GAAG,KAAK;AACrD,MAAM;AACN;;;;;;;;;;;;;;;;;;CAkBC,GACD,MAAM;IACF,UAAU;IACV,uBAAuB;IACvB,sBAAsB;IACtB,wBAAwB;IACxB;;;;;KAKC,GACD,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;QAC/B,IAAI,CAAC,sBAAsB,GAAG,KAAK,sBAAsB;QACzD,IAAI,CAAC,qBAAqB,GAAG,KAAK,qBAAqB;QACvD,IAAI,CAAC,uBAAuB,GAAG,KAAK,uBAAuB;IAC/D;IACA;;;;;;;;KAQC,GACD,MAAM,aAAa,OAAO,EAAE;QACxB,2CAA2C;QAC3C,qDAAqD;QACrD,IAAI,IAAI,CAAC,CAAA,aAAc,EAAE;YACrB,OAAO,IAAI,CAAC,CAAA,aAAc;QAC9B;QACA,MAAM,kBAAkB,IAAI;QAC5B,IAAI,CAAC,IAAI,CAAC,CAAA,aAAc,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACpD,gBAAgB,GAAG,CAAC,4BAA4B,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC,QAAQ,WAAW;QACzG;QACA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,MAAM,IAAI,WAAW,mDACjB;QACR;QACA,MAAM,OAAO;YACT,GAAG,IAAI,CAAC,uBAAuB;YAC/B,KAAK,IAAI,CAAC,SAAS;YACnB,QAAQ;YACR,SAAS;QACb;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,MAAM,WAAW,MAAM,QAAQ,WAAW,CAAC,OAAO,CAAC;QACnD,iEAAiE;QACjE,iCAAiC;QACjC,OAAO,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,IAAI,CAAC,MAAM,GAAG;IAC1D;IACA;;;;;;;;KAQC,GACD,MAAM,0BAA0B,OAAO,EAAE;QACrC,+DAA+D;QAC/D,wEAAwE;QACxE,IAAI,IAAI,CAAC,CAAA,0BAA2B,EAAE;YAClC,OAAO,IAAI,CAAC,CAAA,0BAA2B;QAC3C;QACA,MAAM,kBAAkB,IAAI;QAC5B,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,gBAAgB,GAAG,CAAC,4BAA4B,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC,QAAQ,WAAW;QACzG;QACA,gEAAgE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,CAAA,cAAe,CAAC,iBAAiB,QAAQ,WAAW;QAChF,0DAA0D;QAC1D,sCAAsC;QACtC,iEAAiE;QACjE,kCAAkC;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,CAAA,8BAA+B,CAAC,UAAU,iBAAiB,QAAQ,WAAW;QAC1G,OAAO;YACH,aAAa,SAAS,WAAW;YACjC,iBAAiB,SAAS,eAAe;YACzC,OAAO,SAAS,KAAK;QACzB;IACJ;IACA;;;KAGC,GACD,MAAM,CAAA,qBAAsB,CAAC,WAAW;QACpC,MAAM,OAAO;YACT,GAAG,IAAI,CAAC,uBAAuB;YAC/B,KAAK,IAAI,CAAC,qBAAqB;YAC/B,QAAQ;YACR,SAAS;gBAAE,wCAAwC;YAAM;QAC7D;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,MAAM,WAAW,MAAM,YAAY,OAAO,CAAC;QAC3C,OAAO,SAAS,IAAI;IACxB;IACA;;;;;KAKC,GACD,MAAM,CAAA,cAAe,CAAC,OAAO,EAAE,WAAW;QACtC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,MAAM,IAAI,MAAM,sDACZ;QACR;QACA,MAAM,OAAO;YACT,GAAG,IAAI,CAAC,uBAAuB;YAC/B,KAAK,IAAI,CAAC,sBAAsB;YAChC,QAAQ;YACR,SAAS;QACb;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,MAAM,WAAW,MAAM,YAAY,OAAO,CAAC;QAC3C,OAAO,SAAS,IAAI;IACxB;IACA;;;;;;;;KAQC,GACD,MAAM,CAAA,8BAA+B,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW;QAChE,MAAM,OAAO;YACT,GAAG,IAAI,CAAC,uBAAuB;YAC/B,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,UAAU;YACjD,SAAS;QACb;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,MAAM,WAAW,MAAM,YAAY,OAAO,CAAC;QAC3C,OAAO,SAAS,IAAI;IACxB;IACA,IAAI,CAAA,aAAc;QACd,2EAA2E;QAC3E,wBAAwB;QACxB,OAAQ,QAAQ,GAAG,CAAC,aAAa,IAAI,QAAQ,GAAG,CAAC,qBAAqB,IAAI;IAC9E;IACA,IAAI,CAAA,0BAA2B;QAC3B,iEAAiE;QACjE,IAAI,QAAQ,GAAG,CAAC,oBAAoB,IAChC,QAAQ,GAAG,CAAC,wBAAwB,EAAE;YACtC,OAAO;gBACH,aAAa,QAAQ,GAAG,CAAC,oBAAoB;gBAC7C,iBAAiB,QAAQ,GAAG,CAAC,wBAAwB;gBACrD,OAAO,QAAQ,GAAG,CAAC,oBAAoB;YAC3C;QACJ;QACA,OAAO;IACX;AACJ;AACA,QAAQ,qCAAqC,GAAG,uCAChD,iEAAiE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/awsclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AwsClient = void 0;\nconst awsrequestsigner_1 = require(\"./awsrequestsigner\");\nconst baseexternalclient_1 = require(\"./baseexternalclient\");\nconst defaultawssecuritycredentialssupplier_1 = require(\"./defaultawssecuritycredentialssupplier\");\nconst util_1 = require(\"../util\");\nconst gaxios_1 = require(\"gaxios\");\n/**\n * AWS external account client. This is used for AWS workloads, where\n * AWS STS GetCallerIdentity serialized signed requests are exchanged for\n * GCP access token.\n */\nclass AwsClient extends baseexternalclient_1.BaseExternalAccountClient {\n    environmentId;\n    awsSecurityCredentialsSupplier;\n    regionalCredVerificationUrl;\n    awsRequestSigner;\n    region;\n    static #DEFAULT_AWS_REGIONAL_CREDENTIAL_VERIFICATION_URL = 'https://sts.{region}.amazonaws.com?Action=GetCallerIdentity&Version=2011-06-15';\n    /**\n     * @deprecated AWS client no validates the EC2 metadata address.\n     **/\n    static AWS_EC2_METADATA_IPV4_ADDRESS = '***************';\n    /**\n     * @deprecated AWS client no validates the EC2 metadata address.\n     **/\n    static AWS_EC2_METADATA_IPV6_ADDRESS = 'fd00:ec2::254';\n    /**\n     * Instantiates an AwsClient instance using the provided JSON\n     * object loaded from an external account credentials file.\n     * An error is thrown if the credential is not a valid AWS credential.\n     * @param options The external account options object typically loaded\n     *   from the external account JSON credential file.\n     */\n    constructor(options) {\n        super(options);\n        const opts = (0, util_1.originalOrCamelOptions)(options);\n        const credentialSource = opts.get('credential_source');\n        const awsSecurityCredentialsSupplier = opts.get('aws_security_credentials_supplier');\n        // Validate credential sourcing configuration.\n        if (!credentialSource && !awsSecurityCredentialsSupplier) {\n            throw new Error('A credential source or AWS security credentials supplier must be specified.');\n        }\n        if (credentialSource && awsSecurityCredentialsSupplier) {\n            throw new Error('Only one of credential source or AWS security credentials supplier can be specified.');\n        }\n        if (awsSecurityCredentialsSupplier) {\n            this.awsSecurityCredentialsSupplier = awsSecurityCredentialsSupplier;\n            this.regionalCredVerificationUrl =\n                AwsClient.#DEFAULT_AWS_REGIONAL_CREDENTIAL_VERIFICATION_URL;\n            this.credentialSourceType = 'programmatic';\n        }\n        else {\n            const credentialSourceOpts = (0, util_1.originalOrCamelOptions)(credentialSource);\n            this.environmentId = credentialSourceOpts.get('environment_id');\n            // This is only required if the AWS region is not available in the\n            // AWS_REGION or AWS_DEFAULT_REGION environment variables.\n            const regionUrl = credentialSourceOpts.get('region_url');\n            // This is only required if AWS security credentials are not available in\n            // environment variables.\n            const securityCredentialsUrl = credentialSourceOpts.get('url');\n            const imdsV2SessionTokenUrl = credentialSourceOpts.get('imdsv2_session_token_url');\n            this.awsSecurityCredentialsSupplier =\n                new defaultawssecuritycredentialssupplier_1.DefaultAwsSecurityCredentialsSupplier({\n                    regionUrl: regionUrl,\n                    securityCredentialsUrl: securityCredentialsUrl,\n                    imdsV2SessionTokenUrl: imdsV2SessionTokenUrl,\n                });\n            this.regionalCredVerificationUrl = credentialSourceOpts.get('regional_cred_verification_url');\n            this.credentialSourceType = 'aws';\n            // Data validators.\n            this.validateEnvironmentId();\n        }\n        this.awsRequestSigner = null;\n        this.region = '';\n    }\n    validateEnvironmentId() {\n        const match = this.environmentId?.match(/^(aws)(\\d+)$/);\n        if (!match || !this.regionalCredVerificationUrl) {\n            throw new Error('No valid AWS \"credential_source\" provided');\n        }\n        else if (parseInt(match[2], 10) !== 1) {\n            throw new Error(`aws version \"${match[2]}\" is not supported in the current build.`);\n        }\n    }\n    /**\n     * Triggered when an external subject token is needed to be exchanged for a\n     * GCP access token via GCP STS endpoint. This will call the\n     * {@link AwsSecurityCredentialsSupplier} to retrieve an AWS region and AWS\n     * Security Credentials, then use them to create a signed AWS STS request that\n     * can be exchanged for a GCP access token.\n     * @return A promise that resolves with the external subject token.\n     */\n    async retrieveSubjectToken() {\n        // Initialize AWS request signer if not already initialized.\n        if (!this.awsRequestSigner) {\n            this.region = await this.awsSecurityCredentialsSupplier.getAwsRegion(this.supplierContext);\n            this.awsRequestSigner = new awsrequestsigner_1.AwsRequestSigner(async () => {\n                return this.awsSecurityCredentialsSupplier.getAwsSecurityCredentials(this.supplierContext);\n            }, this.region);\n        }\n        // Generate signed request to AWS STS GetCallerIdentity API.\n        // Use the required regional endpoint. Otherwise, the request will fail.\n        const options = await this.awsRequestSigner.getRequestOptions({\n            ...AwsClient.RETRY_CONFIG,\n            url: this.regionalCredVerificationUrl.replace('{region}', this.region),\n            method: 'POST',\n        });\n        // The GCP STS endpoint expects the headers to be formatted as:\n        // [\n        //   {key: 'x-amz-date', value: '...'},\n        //   {key: 'authorization', value: '...'},\n        //   ...\n        // ]\n        // And then serialized as:\n        // encodeURIComponent(JSON.stringify({\n        //   url: '...',\n        //   method: 'POST',\n        //   headers: [{key: 'x-amz-date', value: '...'}, ...]\n        // }))\n        const reformattedHeader = [];\n        const extendedHeaders = gaxios_1.Gaxios.mergeHeaders({\n            // The full, canonical resource name of the workload identity pool\n            // provider, with or without the HTTPS prefix.\n            // Including this header as part of the signature is recommended to\n            // ensure data integrity.\n            'x-goog-cloud-target-resource': this.audience,\n        }, options.headers);\n        // Reformat header to GCP STS expected format.\n        extendedHeaders.forEach((value, key) => reformattedHeader.push({ key, value }));\n        // Serialize the reformatted signed request.\n        return encodeURIComponent(JSON.stringify({\n            url: options.url,\n            method: options.method,\n            headers: reformattedHeader,\n        }));\n    }\n}\nexports.AwsClient = AwsClient;\n//# sourceMappingURL=awsclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;;CAIC,GACD,MAAM,kBAAkB,qBAAqB,yBAAyB;IAClE,cAAc;IACd,+BAA+B;IAC/B,4BAA4B;IAC5B,iBAAiB;IACjB,OAAO;IACP,OAAO,CAAA,gDAAiD,GAAG,iFAAiF;IAC5I;;MAEE,GACF,OAAO,gCAAgC,kBAAkB;IACzD;;MAEE,GACF,OAAO,gCAAgC,gBAAgB;IACvD;;;;;;KAMC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,MAAM,OAAO,CAAC,GAAG,OAAO,sBAAsB,EAAE;QAChD,MAAM,mBAAmB,KAAK,GAAG,CAAC;QAClC,MAAM,iCAAiC,KAAK,GAAG,CAAC;QAChD,8CAA8C;QAC9C,IAAI,CAAC,oBAAoB,CAAC,gCAAgC;YACtD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,oBAAoB,gCAAgC;YACpD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,gCAAgC;YAChC,IAAI,CAAC,8BAA8B,GAAG;YACtC,IAAI,CAAC,2BAA2B,GAC5B,UAAU,CAAA,gDAAiD;YAC/D,IAAI,CAAC,oBAAoB,GAAG;QAChC,OACK;YACD,MAAM,uBAAuB,CAAC,GAAG,OAAO,sBAAsB,EAAE;YAChE,IAAI,CAAC,aAAa,GAAG,qBAAqB,GAAG,CAAC;YAC9C,kEAAkE;YAClE,0DAA0D;YAC1D,MAAM,YAAY,qBAAqB,GAAG,CAAC;YAC3C,yEAAyE;YACzE,yBAAyB;YACzB,MAAM,yBAAyB,qBAAqB,GAAG,CAAC;YACxD,MAAM,wBAAwB,qBAAqB,GAAG,CAAC;YACvD,IAAI,CAAC,8BAA8B,GAC/B,IAAI,wCAAwC,qCAAqC,CAAC;gBAC9E,WAAW;gBACX,wBAAwB;gBACxB,uBAAuB;YAC3B;YACJ,IAAI,CAAC,2BAA2B,GAAG,qBAAqB,GAAG,CAAC;YAC5D,IAAI,CAAC,oBAAoB,GAAG;YAC5B,mBAAmB;YACnB,IAAI,CAAC,qBAAqB;QAC9B;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,wBAAwB;QACpB,MAAM,QAAQ,IAAI,CAAC,aAAa,EAAE,MAAM;QACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,2BAA2B,EAAE;YAC7C,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG;YACnC,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,CAAC,wCAAwC,CAAC;QACtF;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,uBAAuB;QACzB,4DAA4D;QAC5D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe;YACzF,IAAI,CAAC,gBAAgB,GAAG,IAAI,mBAAmB,gBAAgB,CAAC;gBAC5D,OAAO,IAAI,CAAC,8BAA8B,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe;YAC7F,GAAG,IAAI,CAAC,MAAM;QAClB;QACA,4DAA4D;QAC5D,wEAAwE;QACxE,MAAM,UAAU,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;YAC1D,GAAG,UAAU,YAAY;YACzB,KAAK,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,MAAM;YACrE,QAAQ;QACZ;QACA,+DAA+D;QAC/D,IAAI;QACJ,uCAAuC;QACvC,0CAA0C;QAC1C,QAAQ;QACR,IAAI;QACJ,0BAA0B;QAC1B,sCAAsC;QACtC,gBAAgB;QAChB,oBAAoB;QACpB,sDAAsD;QACtD,MAAM;QACN,MAAM,oBAAoB,EAAE;QAC5B,MAAM,kBAAkB,SAAS,MAAM,CAAC,YAAY,CAAC;YACjD,kEAAkE;YAClE,8CAA8C;YAC9C,mEAAmE;YACnE,yBAAyB;YACzB,gCAAgC,IAAI,CAAC,QAAQ;QACjD,GAAG,QAAQ,OAAO;QAClB,8CAA8C;QAC9C,gBAAgB,OAAO,CAAC,CAAC,OAAO,MAAQ,kBAAkB,IAAI,CAAC;gBAAE;gBAAK;YAAM;QAC5E,4CAA4C;QAC5C,OAAO,mBAAmB,KAAK,SAAS,CAAC;YACrC,KAAK,QAAQ,GAAG;YAChB,QAAQ,QAAQ,MAAM;YACtB,SAAS;QACb;IACJ;AACJ;AACA,QAAQ,SAAS,GAAG,WACpB,qCAAqC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4522, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/executable-response.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2022 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.InvalidSubjectTokenError = exports.InvalidMessageFieldError = exports.InvalidCodeFieldError = exports.InvalidTokenTypeFieldError = exports.InvalidExpirationTimeFieldError = exports.InvalidSuccessFieldError = exports.InvalidVersionFieldError = exports.ExecutableResponseError = exports.ExecutableResponse = void 0;\nconst SAML_SUBJECT_TOKEN_TYPE = 'urn:ietf:params:oauth:token-type:saml2';\nconst OIDC_SUBJECT_TOKEN_TYPE1 = 'urn:ietf:params:oauth:token-type:id_token';\nconst OIDC_SUBJECT_TOKEN_TYPE2 = 'urn:ietf:params:oauth:token-type:jwt';\n/**\n * Defines the response of a 3rd party executable run by the pluggable auth client.\n */\nclass ExecutableResponse {\n    /**\n     * The version of the Executable response. Only version 1 is currently supported.\n     */\n    version;\n    /**\n     * Whether the executable ran successfully.\n     */\n    success;\n    /**\n     * The epoch time for expiration of the token in seconds.\n     */\n    expirationTime;\n    /**\n     * The type of subject token in the response, currently supported values are:\n     * urn:ietf:params:oauth:token-type:saml2\n     * urn:ietf:params:oauth:token-type:id_token\n     * urn:ietf:params:oauth:token-type:jwt\n     */\n    tokenType;\n    /**\n     * The error code from the executable.\n     */\n    errorCode;\n    /**\n     * The error message from the executable.\n     */\n    errorMessage;\n    /**\n     * The subject token from the executable, format depends on tokenType.\n     */\n    subjectToken;\n    /**\n     * Instantiates an ExecutableResponse instance using the provided JSON object\n     * from the output of the executable.\n     * @param responseJson Response from a 3rd party executable, loaded from a\n     * run of the executable or a cached output file.\n     */\n    constructor(responseJson) {\n        // Check that the required fields exist in the json response.\n        if (!responseJson.version) {\n            throw new InvalidVersionFieldError(\"Executable response must contain a 'version' field.\");\n        }\n        if (responseJson.success === undefined) {\n            throw new InvalidSuccessFieldError(\"Executable response must contain a 'success' field.\");\n        }\n        this.version = responseJson.version;\n        this.success = responseJson.success;\n        // Validate required fields for a successful response.\n        if (this.success) {\n            this.expirationTime = responseJson.expiration_time;\n            this.tokenType = responseJson.token_type;\n            // Validate token type field.\n            if (this.tokenType !== SAML_SUBJECT_TOKEN_TYPE &&\n                this.tokenType !== OIDC_SUBJECT_TOKEN_TYPE1 &&\n                this.tokenType !== OIDC_SUBJECT_TOKEN_TYPE2) {\n                throw new InvalidTokenTypeFieldError(\"Executable response must contain a 'token_type' field when successful \" +\n                    `and it must be one of ${OIDC_SUBJECT_TOKEN_TYPE1}, ${OIDC_SUBJECT_TOKEN_TYPE2}, or ${SAML_SUBJECT_TOKEN_TYPE}.`);\n            }\n            // Validate subject token.\n            if (this.tokenType === SAML_SUBJECT_TOKEN_TYPE) {\n                if (!responseJson.saml_response) {\n                    throw new InvalidSubjectTokenError(`Executable response must contain a 'saml_response' field when token_type=${SAML_SUBJECT_TOKEN_TYPE}.`);\n                }\n                this.subjectToken = responseJson.saml_response;\n            }\n            else {\n                if (!responseJson.id_token) {\n                    throw new InvalidSubjectTokenError(\"Executable response must contain a 'id_token' field when \" +\n                        `token_type=${OIDC_SUBJECT_TOKEN_TYPE1} or ${OIDC_SUBJECT_TOKEN_TYPE2}.`);\n                }\n                this.subjectToken = responseJson.id_token;\n            }\n        }\n        else {\n            // Both code and message must be provided for unsuccessful responses.\n            if (!responseJson.code) {\n                throw new InvalidCodeFieldError(\"Executable response must contain a 'code' field when unsuccessful.\");\n            }\n            if (!responseJson.message) {\n                throw new InvalidMessageFieldError(\"Executable response must contain a 'message' field when unsuccessful.\");\n            }\n            this.errorCode = responseJson.code;\n            this.errorMessage = responseJson.message;\n        }\n    }\n    /**\n     * @return A boolean representing if the response has a valid token. Returns\n     * true when the response was successful and the token is not expired.\n     */\n    isValid() {\n        return !this.isExpired() && this.success;\n    }\n    /**\n     * @return A boolean representing if the response is expired. Returns true if the\n     * provided timeout has passed.\n     */\n    isExpired() {\n        return (this.expirationTime !== undefined &&\n            this.expirationTime < Math.round(Date.now() / 1000));\n    }\n}\nexports.ExecutableResponse = ExecutableResponse;\n/**\n * An error thrown by the ExecutableResponse class.\n */\nclass ExecutableResponseError extends Error {\n    constructor(message) {\n        super(message);\n        Object.setPrototypeOf(this, new.target.prototype);\n    }\n}\nexports.ExecutableResponseError = ExecutableResponseError;\n/**\n * An error thrown when the 'version' field in an executable response is missing or invalid.\n */\nclass InvalidVersionFieldError extends ExecutableResponseError {\n}\nexports.InvalidVersionFieldError = InvalidVersionFieldError;\n/**\n * An error thrown when the 'success' field in an executable response is missing or invalid.\n */\nclass InvalidSuccessFieldError extends ExecutableResponseError {\n}\nexports.InvalidSuccessFieldError = InvalidSuccessFieldError;\n/**\n * An error thrown when the 'expiration_time' field in an executable response is missing or invalid.\n */\nclass InvalidExpirationTimeFieldError extends ExecutableResponseError {\n}\nexports.InvalidExpirationTimeFieldError = InvalidExpirationTimeFieldError;\n/**\n * An error thrown when the 'token_type' field in an executable response is missing or invalid.\n */\nclass InvalidTokenTypeFieldError extends ExecutableResponseError {\n}\nexports.InvalidTokenTypeFieldError = InvalidTokenTypeFieldError;\n/**\n * An error thrown when the 'code' field in an executable response is missing or invalid.\n */\nclass InvalidCodeFieldError extends ExecutableResponseError {\n}\nexports.InvalidCodeFieldError = InvalidCodeFieldError;\n/**\n * An error thrown when the 'message' field in an executable response is missing or invalid.\n */\nclass InvalidMessageFieldError extends ExecutableResponseError {\n}\nexports.InvalidMessageFieldError = InvalidMessageFieldError;\n/**\n * An error thrown when the subject token in an executable response is missing or invalid.\n */\nclass InvalidSubjectTokenError extends ExecutableResponseError {\n}\nexports.InvalidSubjectTokenError = InvalidSubjectTokenError;\n//# sourceMappingURL=executable-response.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,wBAAwB,GAAG,QAAQ,wBAAwB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,0BAA0B,GAAG,QAAQ,+BAA+B,GAAG,QAAQ,wBAAwB,GAAG,QAAQ,wBAAwB,GAAG,QAAQ,uBAAuB,GAAG,QAAQ,kBAAkB,GAAG,KAAK;AAC/T,MAAM,0BAA0B;AAChC,MAAM,2BAA2B;AACjC,MAAM,2BAA2B;AACjC;;CAEC,GACD,MAAM;IACF;;KAEC,GACD,QAAQ;IACR;;KAEC,GACD,QAAQ;IACR;;KAEC,GACD,eAAe;IACf;;;;;KAKC,GACD,UAAU;IACV;;KAEC,GACD,UAAU;IACV;;KAEC,GACD,aAAa;IACb;;KAEC,GACD,aAAa;IACb;;;;;KAKC,GACD,YAAY,YAAY,CAAE;QACtB,6DAA6D;QAC7D,IAAI,CAAC,aAAa,OAAO,EAAE;YACvB,MAAM,IAAI,yBAAyB;QACvC;QACA,IAAI,aAAa,OAAO,KAAK,WAAW;YACpC,MAAM,IAAI,yBAAyB;QACvC;QACA,IAAI,CAAC,OAAO,GAAG,aAAa,OAAO;QACnC,IAAI,CAAC,OAAO,GAAG,aAAa,OAAO;QACnC,sDAAsD;QACtD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,cAAc,GAAG,aAAa,eAAe;YAClD,IAAI,CAAC,SAAS,GAAG,aAAa,UAAU;YACxC,6BAA6B;YAC7B,IAAI,IAAI,CAAC,SAAS,KAAK,2BACnB,IAAI,CAAC,SAAS,KAAK,4BACnB,IAAI,CAAC,SAAS,KAAK,0BAA0B;gBAC7C,MAAM,IAAI,2BAA2B,2EACjC,CAAC,sBAAsB,EAAE,yBAAyB,EAAE,EAAE,yBAAyB,KAAK,EAAE,wBAAwB,CAAC,CAAC;YACxH;YACA,0BAA0B;YAC1B,IAAI,IAAI,CAAC,SAAS,KAAK,yBAAyB;gBAC5C,IAAI,CAAC,aAAa,aAAa,EAAE;oBAC7B,MAAM,IAAI,yBAAyB,CAAC,yEAAyE,EAAE,wBAAwB,CAAC,CAAC;gBAC7I;gBACA,IAAI,CAAC,YAAY,GAAG,aAAa,aAAa;YAClD,OACK;gBACD,IAAI,CAAC,aAAa,QAAQ,EAAE;oBACxB,MAAM,IAAI,yBAAyB,8DAC/B,CAAC,WAAW,EAAE,yBAAyB,IAAI,EAAE,yBAAyB,CAAC,CAAC;gBAChF;gBACA,IAAI,CAAC,YAAY,GAAG,aAAa,QAAQ;YAC7C;QACJ,OACK;YACD,qEAAqE;YACrE,IAAI,CAAC,aAAa,IAAI,EAAE;gBACpB,MAAM,IAAI,sBAAsB;YACpC;YACA,IAAI,CAAC,aAAa,OAAO,EAAE;gBACvB,MAAM,IAAI,yBAAyB;YACvC;YACA,IAAI,CAAC,SAAS,GAAG,aAAa,IAAI;YAClC,IAAI,CAAC,YAAY,GAAG,aAAa,OAAO;QAC5C;IACJ;IACA;;;KAGC,GACD,UAAU;QACN,OAAO,CAAC,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,OAAO;IAC5C;IACA;;;KAGC,GACD,YAAY;QACR,OAAQ,IAAI,CAAC,cAAc,KAAK,aAC5B,IAAI,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACtD;AACJ;AACA,QAAQ,kBAAkB,GAAG;AAC7B;;CAEC,GACD,MAAM,gCAAgC;IAClC,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW,SAAS;IACpD;AACJ;AACA,QAAQ,uBAAuB,GAAG;AAClC;;CAEC,GACD,MAAM,iCAAiC;AACvC;AACA,QAAQ,wBAAwB,GAAG;AACnC;;CAEC,GACD,MAAM,iCAAiC;AACvC;AACA,QAAQ,wBAAwB,GAAG;AACnC;;CAEC,GACD,MAAM,wCAAwC;AAC9C;AACA,QAAQ,+BAA+B,GAAG;AAC1C;;CAEC,GACD,MAAM,mCAAmC;AACzC;AACA,QAAQ,0BAA0B,GAAG;AACrC;;CAEC,GACD,MAAM,8BAA8B;AACpC;AACA,QAAQ,qBAAqB,GAAG;AAChC;;CAEC,GACD,MAAM,iCAAiC;AACvC;AACA,QAAQ,wBAAwB,GAAG;AACnC;;CAEC,GACD,MAAM,iCAAiC;AACvC;AACA,QAAQ,wBAAwB,GAAG,0BACnC,+CAA+C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4680, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/pluggable-auth-handler.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2022 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PluggableAuthHandler = exports.ExecutableError = void 0;\nconst executable_response_1 = require(\"./executable-response\");\nconst childProcess = require(\"child_process\");\nconst fs = require(\"fs\");\n/**\n * Error thrown from the executable run by PluggableAuthClient.\n */\nclass ExecutableError extends Error {\n    /**\n     * The exit code returned by the executable.\n     */\n    code;\n    constructor(message, code) {\n        super(`The executable failed with exit code: ${code} and error message: ${message}.`);\n        this.code = code;\n        Object.setPrototypeOf(this, new.target.prototype);\n    }\n}\nexports.ExecutableError = ExecutableError;\n/**\n * A handler used to retrieve 3rd party token responses from user defined\n * executables and cached file output for the PluggableAuthClient class.\n */\nclass PluggableAuthHandler {\n    commandComponents;\n    timeoutMillis;\n    outputFile;\n    /**\n     * Instantiates a PluggableAuthHandler instance using the provided\n     * PluggableAuthHandlerOptions object.\n     */\n    constructor(options) {\n        if (!options.command) {\n            throw new Error('No command provided.');\n        }\n        this.commandComponents = PluggableAuthHandler.parseCommand(options.command);\n        this.timeoutMillis = options.timeoutMillis;\n        if (!this.timeoutMillis) {\n            throw new Error('No timeoutMillis provided.');\n        }\n        this.outputFile = options.outputFile;\n    }\n    /**\n     * Calls user provided executable to get a 3rd party subject token and\n     * returns the response.\n     * @param envMap a Map of additional Environment Variables required for\n     *   the executable.\n     * @return A promise that resolves with the executable response.\n     */\n    retrieveResponseFromExecutable(envMap) {\n        return new Promise((resolve, reject) => {\n            // Spawn process to run executable using added environment variables.\n            const child = childProcess.spawn(this.commandComponents[0], this.commandComponents.slice(1), {\n                env: { ...process.env, ...Object.fromEntries(envMap) },\n            });\n            let output = '';\n            // Append stdout to output as executable runs.\n            child.stdout.on('data', (data) => {\n                output += data;\n            });\n            // Append stderr as executable runs.\n            child.stderr.on('data', (err) => {\n                output += err;\n            });\n            // Set up a timeout to end the child process and throw an error.\n            const timeout = setTimeout(() => {\n                // Kill child process and remove listeners so 'close' event doesn't get\n                // read after child process is killed.\n                child.removeAllListeners();\n                child.kill();\n                return reject(new Error('The executable failed to finish within the timeout specified.'));\n            }, this.timeoutMillis);\n            child.on('close', (code) => {\n                // Cancel timeout if executable closes before timeout is reached.\n                clearTimeout(timeout);\n                if (code === 0) {\n                    // If the executable completed successfully, try to return the parsed response.\n                    try {\n                        const responseJson = JSON.parse(output);\n                        const response = new executable_response_1.ExecutableResponse(responseJson);\n                        return resolve(response);\n                    }\n                    catch (error) {\n                        if (error instanceof executable_response_1.ExecutableResponseError) {\n                            return reject(error);\n                        }\n                        return reject(new executable_response_1.ExecutableResponseError(`The executable returned an invalid response: ${output}`));\n                    }\n                }\n                else {\n                    return reject(new ExecutableError(output, code.toString()));\n                }\n            });\n        });\n    }\n    /**\n     * Checks user provided output file for response from previous run of\n     * executable and return the response if it exists, is formatted correctly, and is not expired.\n     */\n    async retrieveCachedResponse() {\n        if (!this.outputFile || this.outputFile.length === 0) {\n            return undefined;\n        }\n        let filePath;\n        try {\n            filePath = await fs.promises.realpath(this.outputFile);\n        }\n        catch {\n            // If file path cannot be resolved, return undefined.\n            return undefined;\n        }\n        if (!(await fs.promises.lstat(filePath)).isFile()) {\n            // If path does not lead to file, return undefined.\n            return undefined;\n        }\n        const responseString = await fs.promises.readFile(filePath, {\n            encoding: 'utf8',\n        });\n        if (responseString === '') {\n            return undefined;\n        }\n        try {\n            const responseJson = JSON.parse(responseString);\n            const response = new executable_response_1.ExecutableResponse(responseJson);\n            // Check if response is successful and unexpired.\n            if (response.isValid()) {\n                return new executable_response_1.ExecutableResponse(responseJson);\n            }\n            return undefined;\n        }\n        catch (error) {\n            if (error instanceof executable_response_1.ExecutableResponseError) {\n                throw error;\n            }\n            throw new executable_response_1.ExecutableResponseError(`The output file contained an invalid response: ${responseString}`);\n        }\n    }\n    /**\n     * Parses given command string into component array, splitting on spaces unless\n     * spaces are between quotation marks.\n     */\n    static parseCommand(command) {\n        // Split the command into components by splitting on spaces,\n        // unless spaces are contained in quotation marks.\n        const components = command.match(/(?:[^\\s\"]+|\"[^\"]*\")+/g);\n        if (!components) {\n            throw new Error(`Provided command: \"${command}\" could not be parsed.`);\n        }\n        // Remove quotation marks from the beginning and end of each component if they are present.\n        for (let i = 0; i < components.length; i++) {\n            if (components[i][0] === '\"' && components[i].slice(-1) === '\"') {\n                components[i] = components[i].slice(1, -1);\n            }\n        }\n        return components;\n    }\n}\nexports.PluggableAuthHandler = PluggableAuthHandler;\n//# sourceMappingURL=pluggable-auth-handler.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,oBAAoB,GAAG,QAAQ,eAAe,GAAG,KAAK;AAC9D,MAAM;AACN,MAAM;AACN,MAAM;AACN;;CAEC,GACD,MAAM,wBAAwB;IAC1B;;KAEC,GACD,KAAK;IACL,YAAY,OAAO,EAAE,IAAI,CAAE;QACvB,KAAK,CAAC,CAAC,sCAAsC,EAAE,KAAK,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QACpF,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW,SAAS;IACpD;AACJ;AACA,QAAQ,eAAe,GAAG;AAC1B;;;CAGC,GACD,MAAM;IACF,kBAAkB;IAClB,cAAc;IACd,WAAW;IACX;;;KAGC,GACD,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,QAAQ,OAAO,EAAE;YAClB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,iBAAiB,GAAG,qBAAqB,YAAY,CAAC,QAAQ,OAAO;QAC1E,IAAI,CAAC,aAAa,GAAG,QAAQ,aAAa;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;IACxC;IACA;;;;;;KAMC,GACD,+BAA+B,MAAM,EAAE;QACnC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,qEAAqE;YACrE,MAAM,QAAQ,aAAa,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI;gBACzF,KAAK;oBAAE,GAAG,QAAQ,GAAG;oBAAE,GAAG,OAAO,WAAW,CAAC,OAAO;gBAAC;YACzD;YACA,IAAI,SAAS;YACb,8CAA8C;YAC9C,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC;gBACrB,UAAU;YACd;YACA,oCAAoC;YACpC,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC;gBACrB,UAAU;YACd;YACA,gEAAgE;YAChE,MAAM,UAAU,WAAW;gBACvB,uEAAuE;gBACvE,sCAAsC;gBACtC,MAAM,kBAAkB;gBACxB,MAAM,IAAI;gBACV,OAAO,OAAO,IAAI,MAAM;YAC5B,GAAG,IAAI,CAAC,aAAa;YACrB,MAAM,EAAE,CAAC,SAAS,CAAC;gBACf,iEAAiE;gBACjE,aAAa;gBACb,IAAI,SAAS,GAAG;oBACZ,+EAA+E;oBAC/E,IAAI;wBACA,MAAM,eAAe,KAAK,KAAK,CAAC;wBAChC,MAAM,WAAW,IAAI,sBAAsB,kBAAkB,CAAC;wBAC9D,OAAO,QAAQ;oBACnB,EACA,OAAO,OAAO;wBACV,IAAI,iBAAiB,sBAAsB,uBAAuB,EAAE;4BAChE,OAAO,OAAO;wBAClB;wBACA,OAAO,OAAO,IAAI,sBAAsB,uBAAuB,CAAC,CAAC,6CAA6C,EAAE,QAAQ;oBAC5H;gBACJ,OACK;oBACD,OAAO,OAAO,IAAI,gBAAgB,QAAQ,KAAK,QAAQ;gBAC3D;YACJ;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,yBAAyB;QAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;YAClD,OAAO;QACX;QACA,IAAI;QACJ,IAAI;YACA,WAAW,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU;QACzD,EACA,OAAM;YACF,qDAAqD;YACrD,OAAO;QACX;QACA,IAAI,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,IAAI;YAC/C,mDAAmD;YACnD,OAAO;QACX;QACA,MAAM,iBAAiB,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU;YACxD,UAAU;QACd;QACA,IAAI,mBAAmB,IAAI;YACvB,OAAO;QACX;QACA,IAAI;YACA,MAAM,eAAe,KAAK,KAAK,CAAC;YAChC,MAAM,WAAW,IAAI,sBAAsB,kBAAkB,CAAC;YAC9D,iDAAiD;YACjD,IAAI,SAAS,OAAO,IAAI;gBACpB,OAAO,IAAI,sBAAsB,kBAAkB,CAAC;YACxD;YACA,OAAO;QACX,EACA,OAAO,OAAO;YACV,IAAI,iBAAiB,sBAAsB,uBAAuB,EAAE;gBAChE,MAAM;YACV;YACA,MAAM,IAAI,sBAAsB,uBAAuB,CAAC,CAAC,+CAA+C,EAAE,gBAAgB;QAC9H;IACJ;IACA;;;KAGC,GACD,OAAO,aAAa,OAAO,EAAE;QACzB,4DAA4D;QAC5D,kDAAkD;QAClD,MAAM,aAAa,QAAQ,KAAK,CAAC;QACjC,IAAI,CAAC,YAAY;YACb,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,QAAQ,sBAAsB,CAAC;QACzE;QACA,2FAA2F;QAC3F,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,KAAK,OAAO,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK;gBAC7D,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;YAC5C;QACJ;QACA,OAAO;IACX;AACJ;AACA,QAAQ,oBAAoB,GAAG,sBAC/B,kDAAkD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4852, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/pluggable-auth-client.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2022 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PluggableAuthClient = exports.ExecutableError = void 0;\nconst baseexternalclient_1 = require(\"./baseexternalclient\");\nconst executable_response_1 = require(\"./executable-response\");\nconst pluggable_auth_handler_1 = require(\"./pluggable-auth-handler\");\nvar pluggable_auth_handler_2 = require(\"./pluggable-auth-handler\");\nObject.defineProperty(exports, \"ExecutableError\", { enumerable: true, get: function () { return pluggable_auth_handler_2.ExecutableError; } });\n/**\n * The default executable timeout when none is provided, in milliseconds.\n */\nconst DEFAULT_EXECUTABLE_TIMEOUT_MILLIS = 30 * 1000;\n/**\n * The minimum allowed executable timeout in milliseconds.\n */\nconst MINIMUM_EXECUTABLE_TIMEOUT_MILLIS = 5 * 1000;\n/**\n * The maximum allowed executable timeout in milliseconds.\n */\nconst MAXIMUM_EXECUTABLE_TIMEOUT_MILLIS = 120 * 1000;\n/**\n * The environment variable to check to see if executable can be run.\n * Value must be set to '1' for the executable to run.\n */\nconst GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES = 'GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES';\n/**\n * The maximum currently supported executable version.\n */\nconst MAXIMUM_EXECUTABLE_VERSION = 1;\n/**\n * PluggableAuthClient enables the exchange of workload identity pool external credentials for\n * Google access tokens by retrieving 3rd party tokens through a user supplied executable. These\n * scripts/executables are completely independent of the Google Cloud Auth libraries. These\n * credentials plug into ADC and will call the specified executable to retrieve the 3rd party token\n * to be exchanged for a Google access token.\n *\n * <p>To use these credentials, the GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES environment variable\n * must be set to '1'. This is for security reasons.\n *\n * <p>Both OIDC and SAML are supported. The executable must adhere to a specific response format\n * defined below.\n *\n * <p>The executable must print out the 3rd party token to STDOUT in JSON format. When an\n * output_file is specified in the credential configuration, the executable must also handle writing the\n * JSON response to this file.\n *\n * <pre>\n * OIDC response sample:\n * {\n *   \"version\": 1,\n *   \"success\": true,\n *   \"token_type\": \"urn:ietf:params:oauth:token-type:id_token\",\n *   \"id_token\": \"HEADER.PAYLOAD.SIGNATURE\",\n *   \"expiration_time\": **********\n * }\n *\n * SAML2 response sample:\n * {\n *   \"version\": 1,\n *   \"success\": true,\n *   \"token_type\": \"urn:ietf:params:oauth:token-type:saml2\",\n *   \"saml_response\": \"...\",\n *   \"expiration_time\": **********\n * }\n *\n * Error response sample:\n * {\n *   \"version\": 1,\n *   \"success\": false,\n *   \"code\": \"401\",\n *   \"message\": \"Error message.\"\n * }\n * </pre>\n *\n * <p>The \"expiration_time\" field in the JSON response is only required for successful\n * responses when an output file was specified in the credential configuration\n *\n * <p>The auth libraries will populate certain environment variables that will be accessible by the\n * executable, such as: GOOGLE_EXTERNAL_ACCOUNT_AUDIENCE, GOOGLE_EXTERNAL_ACCOUNT_TOKEN_TYPE,\n * GOOGLE_EXTERNAL_ACCOUNT_INTERACTIVE, GOOGLE_EXTERNAL_ACCOUNT_IMPERSONATED_EMAIL, and\n * GOOGLE_EXTERNAL_ACCOUNT_OUTPUT_FILE.\n *\n * <p>Please see this repositories README for a complete executable request/response specification.\n */\nclass PluggableAuthClient extends baseexternalclient_1.BaseExternalAccountClient {\n    /**\n     * The command used to retrieve the third party token.\n     */\n    command;\n    /**\n     * The timeout in milliseconds for running executable,\n     * set to default if none provided.\n     */\n    timeoutMillis;\n    /**\n     * The path to file to check for cached executable response.\n     */\n    outputFile;\n    /**\n     * Executable and output file handler.\n     */\n    handler;\n    /**\n     * Instantiates a PluggableAuthClient instance using the provided JSON\n     * object loaded from an external account credentials file.\n     * An error is thrown if the credential is not a valid pluggable auth credential.\n     * @param options The external account options object typically loaded from\n     *   the external account JSON credential file.\n     */\n    constructor(options) {\n        super(options);\n        if (!options.credential_source.executable) {\n            throw new Error('No valid Pluggable Auth \"credential_source\" provided.');\n        }\n        this.command = options.credential_source.executable.command;\n        if (!this.command) {\n            throw new Error('No valid Pluggable Auth \"credential_source\" provided.');\n        }\n        // Check if the provided timeout exists and if it is valid.\n        if (options.credential_source.executable.timeout_millis === undefined) {\n            this.timeoutMillis = DEFAULT_EXECUTABLE_TIMEOUT_MILLIS;\n        }\n        else {\n            this.timeoutMillis = options.credential_source.executable.timeout_millis;\n            if (this.timeoutMillis < MINIMUM_EXECUTABLE_TIMEOUT_MILLIS ||\n                this.timeoutMillis > MAXIMUM_EXECUTABLE_TIMEOUT_MILLIS) {\n                throw new Error(`Timeout must be between ${MINIMUM_EXECUTABLE_TIMEOUT_MILLIS} and ` +\n                    `${MAXIMUM_EXECUTABLE_TIMEOUT_MILLIS} milliseconds.`);\n            }\n        }\n        this.outputFile = options.credential_source.executable.output_file;\n        this.handler = new pluggable_auth_handler_1.PluggableAuthHandler({\n            command: this.command,\n            timeoutMillis: this.timeoutMillis,\n            outputFile: this.outputFile,\n        });\n        this.credentialSourceType = 'executable';\n    }\n    /**\n     * Triggered when an external subject token is needed to be exchanged for a\n     * GCP access token via GCP STS endpoint.\n     * This uses the `options.credential_source` object to figure out how\n     * to retrieve the token using the current environment. In this case,\n     * this calls a user provided executable which returns the subject token.\n     * The logic is summarized as:\n     * 1. Validated that the executable is allowed to run. The\n     *    GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES environment must be set to\n     *    1 for security reasons.\n     * 2. If an output file is specified by the user, check the file location\n     *    for a response. If the file exists and contains a valid response,\n     *    return the subject token from the file.\n     * 3. Call the provided executable and return response.\n     * @return A promise that resolves with the external subject token.\n     */\n    async retrieveSubjectToken() {\n        // Check if the executable is allowed to run.\n        if (process.env[GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES] !== '1') {\n            throw new Error('Pluggable Auth executables need to be explicitly allowed to run by ' +\n                'setting the GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES environment ' +\n                'Variable to 1.');\n        }\n        let executableResponse = undefined;\n        // Try to get cached executable response from output file.\n        if (this.outputFile) {\n            executableResponse = await this.handler.retrieveCachedResponse();\n        }\n        // If no response from output file, call the executable.\n        if (!executableResponse) {\n            // Set up environment map with required values for the executable.\n            const envMap = new Map();\n            envMap.set('GOOGLE_EXTERNAL_ACCOUNT_AUDIENCE', this.audience);\n            envMap.set('GOOGLE_EXTERNAL_ACCOUNT_TOKEN_TYPE', this.subjectTokenType);\n            // Always set to 0 because interactive mode is not supported.\n            envMap.set('GOOGLE_EXTERNAL_ACCOUNT_INTERACTIVE', '0');\n            if (this.outputFile) {\n                envMap.set('GOOGLE_EXTERNAL_ACCOUNT_OUTPUT_FILE', this.outputFile);\n            }\n            const serviceAccountEmail = this.getServiceAccountEmail();\n            if (serviceAccountEmail) {\n                envMap.set('GOOGLE_EXTERNAL_ACCOUNT_IMPERSONATED_EMAIL', serviceAccountEmail);\n            }\n            executableResponse =\n                await this.handler.retrieveResponseFromExecutable(envMap);\n        }\n        if (executableResponse.version > MAXIMUM_EXECUTABLE_VERSION) {\n            throw new Error(`Version of executable is not currently supported, maximum supported version is ${MAXIMUM_EXECUTABLE_VERSION}.`);\n        }\n        // Check that response was successful.\n        if (!executableResponse.success) {\n            throw new pluggable_auth_handler_1.ExecutableError(executableResponse.errorMessage, executableResponse.errorCode);\n        }\n        // Check that response contains expiration time if output file was specified.\n        if (this.outputFile) {\n            if (!executableResponse.expirationTime) {\n                throw new executable_response_1.InvalidExpirationTimeFieldError('The executable response must contain the `expiration_time` field for successful responses when an output_file has been specified in the configuration.');\n            }\n        }\n        // Check that response is not expired.\n        if (executableResponse.isExpired()) {\n            throw new Error('Executable response is expired.');\n        }\n        // Return subject token from response.\n        return executableResponse.subjectToken;\n    }\n}\nexports.PluggableAuthClient = PluggableAuthClient;\n//# sourceMappingURL=pluggable-auth-client.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mBAAmB,GAAG,QAAQ,eAAe,GAAG,KAAK;AAC7D,MAAM;AACN,MAAM;AACN,MAAM;AACN,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,mBAAmB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,yBAAyB,eAAe;IAAE;AAAE;AAC5I;;CAEC,GACD,MAAM,oCAAoC,KAAK;AAC/C;;CAEC,GACD,MAAM,oCAAoC,IAAI;AAC9C;;CAEC,GACD,MAAM,oCAAoC,MAAM;AAChD;;;CAGC,GACD,MAAM,4CAA4C;AAClD;;CAEC,GACD,MAAM,6BAA6B;AACnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsDC,GACD,MAAM,4BAA4B,qBAAqB,yBAAyB;IAC5E;;KAEC,GACD,QAAQ;IACR;;;KAGC,GACD,cAAc;IACd;;KAEC,GACD,WAAW;IACX;;KAEC,GACD,QAAQ;IACR;;;;;;KAMC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,QAAQ,iBAAiB,CAAC,UAAU,EAAE;YACvC,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,OAAO,GAAG,QAAQ,iBAAiB,CAAC,UAAU,CAAC,OAAO;QAC3D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,IAAI,MAAM;QACpB;QACA,2DAA2D;QAC3D,IAAI,QAAQ,iBAAiB,CAAC,UAAU,CAAC,cAAc,KAAK,WAAW;YACnE,IAAI,CAAC,aAAa,GAAG;QACzB,OACK;YACD,IAAI,CAAC,aAAa,GAAG,QAAQ,iBAAiB,CAAC,UAAU,CAAC,cAAc;YACxE,IAAI,IAAI,CAAC,aAAa,GAAG,qCACrB,IAAI,CAAC,aAAa,GAAG,mCAAmC;gBACxD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,kCAAkC,KAAK,CAAC,GAC/E,GAAG,kCAAkC,cAAc,CAAC;YAC5D;QACJ;QACA,IAAI,CAAC,UAAU,GAAG,QAAQ,iBAAiB,CAAC,UAAU,CAAC,WAAW;QAClE,IAAI,CAAC,OAAO,GAAG,IAAI,yBAAyB,oBAAoB,CAAC;YAC7D,SAAS,IAAI,CAAC,OAAO;YACrB,eAAe,IAAI,CAAC,aAAa;YACjC,YAAY,IAAI,CAAC,UAAU;QAC/B;QACA,IAAI,CAAC,oBAAoB,GAAG;IAChC;IACA;;;;;;;;;;;;;;;KAeC,GACD,MAAM,uBAAuB;QACzB,6CAA6C;QAC7C,IAAI,QAAQ,GAAG,CAAC,0CAA0C,KAAK,KAAK;YAChE,MAAM,IAAI,MAAM,wEACZ,uEACA;QACR;QACA,IAAI,qBAAqB;QACzB,0DAA0D;QAC1D,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,qBAAqB,MAAM,IAAI,CAAC,OAAO,CAAC,sBAAsB;QAClE;QACA,wDAAwD;QACxD,IAAI,CAAC,oBAAoB;YACrB,kEAAkE;YAClE,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,oCAAoC,IAAI,CAAC,QAAQ;YAC5D,OAAO,GAAG,CAAC,sCAAsC,IAAI,CAAC,gBAAgB;YACtE,6DAA6D;YAC7D,OAAO,GAAG,CAAC,uCAAuC;YAClD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,OAAO,GAAG,CAAC,uCAAuC,IAAI,CAAC,UAAU;YACrE;YACA,MAAM,sBAAsB,IAAI,CAAC,sBAAsB;YACvD,IAAI,qBAAqB;gBACrB,OAAO,GAAG,CAAC,8CAA8C;YAC7D;YACA,qBACI,MAAM,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC;QAC1D;QACA,IAAI,mBAAmB,OAAO,GAAG,4BAA4B;YACzD,MAAM,IAAI,MAAM,CAAC,+EAA+E,EAAE,2BAA2B,CAAC,CAAC;QACnI;QACA,sCAAsC;QACtC,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC7B,MAAM,IAAI,yBAAyB,eAAe,CAAC,mBAAmB,YAAY,EAAE,mBAAmB,SAAS;QACpH;QACA,6EAA6E;QAC7E,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,mBAAmB,cAAc,EAAE;gBACpC,MAAM,IAAI,sBAAsB,+BAA+B,CAAC;YACpE;QACJ;QACA,sCAAsC;QACtC,IAAI,mBAAmB,SAAS,IAAI;YAChC,MAAM,IAAI,MAAM;QACpB;QACA,sCAAsC;QACtC,OAAO,mBAAmB,YAAY;IAC1C;AACJ;AACA,QAAQ,mBAAmB,GAAG,qBAC9B,iDAAiD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5065, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/externalclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ExternalAccountClient = void 0;\nconst baseexternalclient_1 = require(\"./baseexternalclient\");\nconst identitypoolclient_1 = require(\"./identitypoolclient\");\nconst awsclient_1 = require(\"./awsclient\");\nconst pluggable_auth_client_1 = require(\"./pluggable-auth-client\");\n/**\n * Dummy class with no constructor. Developers are expected to use fromJSON.\n */\nclass ExternalAccountClient {\n    constructor() {\n        throw new Error('ExternalAccountClients should be initialized via: ' +\n            'ExternalAccountClient.fromJSON(), ' +\n            'directly via explicit constructors, eg. ' +\n            'new AwsClient(options), new IdentityPoolClient(options), new' +\n            'PluggableAuthClientOptions, or via ' +\n            'new GoogleAuth(options).getClient()');\n    }\n    /**\n     * This static method will instantiate the\n     * corresponding type of external account credential depending on the\n     * underlying credential source.\n     * @param options The external account options object typically loaded\n     *   from the external account JSON credential file.\n     * @return A BaseExternalAccountClient instance or null if the options\n     *   provided do not correspond to an external account credential.\n     */\n    static fromJSON(options) {\n        if (options && options.type === baseexternalclient_1.EXTERNAL_ACCOUNT_TYPE) {\n            if (options.credential_source?.environment_id) {\n                return new awsclient_1.AwsClient(options);\n            }\n            else if (options.credential_source?.executable) {\n                return new pluggable_auth_client_1.PluggableAuthClient(options);\n            }\n            else {\n                return new identitypoolclient_1.IdentityPoolClient(options);\n            }\n        }\n        else {\n            return null;\n        }\n    }\n}\nexports.ExternalAccountClient = ExternalAccountClient;\n//# sourceMappingURL=externalclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qBAAqB,GAAG,KAAK;AACrC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;CAEC,GACD,MAAM;IACF,aAAc;QACV,MAAM,IAAI,MAAM,uDACZ,uCACA,6CACA,iEACA,wCACA;IACR;IACA;;;;;;;;KAQC,GACD,OAAO,SAAS,OAAO,EAAE;QACrB,IAAI,WAAW,QAAQ,IAAI,KAAK,qBAAqB,qBAAqB,EAAE;YACxE,IAAI,QAAQ,iBAAiB,EAAE,gBAAgB;gBAC3C,OAAO,IAAI,YAAY,SAAS,CAAC;YACrC,OACK,IAAI,QAAQ,iBAAiB,EAAE,YAAY;gBAC5C,OAAO,IAAI,wBAAwB,mBAAmB,CAAC;YAC3D,OACK;gBACD,OAAO,IAAI,qBAAqB,kBAAkB,CAAC;YACvD;QACJ,OACK;YACD,OAAO;QACX;IACJ;AACJ;AACA,QAAQ,qBAAqB,GAAG,uBAChC,0CAA0C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/externalAccountAuthorizedUserClient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2023 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ExternalAccountAuthorizedUserClient = exports.EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE = void 0;\nconst authclient_1 = require(\"./authclient\");\nconst oauth2common_1 = require(\"./oauth2common\");\nconst gaxios_1 = require(\"gaxios\");\nconst stream = require(\"stream\");\nconst baseexternalclient_1 = require(\"./baseexternalclient\");\n/**\n * The credentials JSON file type for external account authorized user clients.\n */\nexports.EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE = 'external_account_authorized_user';\nconst DEFAULT_TOKEN_URL = 'https://sts.{universeDomain}/v1/oauthtoken';\n/**\n * Handler for token refresh requests sent to the token_url endpoint for external\n * authorized user credentials.\n */\nclass ExternalAccountAuthorizedUserHandler extends oauth2common_1.OAuthClientAuthHandler {\n    #tokenRefreshEndpoint;\n    /**\n     * Initializes an ExternalAccountAuthorizedUserHandler instance.\n     * @param url The URL of the token refresh endpoint.\n     * @param transporter The transporter to use for the refresh request.\n     * @param clientAuthentication The client authentication credentials to use\n     *   for the refresh request.\n     */\n    constructor(options) {\n        super(options);\n        this.#tokenRefreshEndpoint = options.tokenRefreshEndpoint;\n    }\n    /**\n     * Requests a new access token from the token_url endpoint using the provided\n     *   refresh token.\n     * @param refreshToken The refresh token to use to generate a new access token.\n     * @param additionalHeaders Optional additional headers to pass along the\n     *   request.\n     * @return A promise that resolves with the token refresh response containing\n     *   the requested access token and its expiration time.\n     */\n    async refreshToken(refreshToken, headers) {\n        const opts = {\n            ...ExternalAccountAuthorizedUserHandler.RETRY_CONFIG,\n            url: this.#tokenRefreshEndpoint,\n            method: 'POST',\n            headers,\n            data: new URLSearchParams({\n                grant_type: 'refresh_token',\n                refresh_token: refreshToken,\n            }),\n        };\n        authclient_1.AuthClient.setMethodName(opts, 'refreshToken');\n        // Apply OAuth client authentication.\n        this.applyClientAuthenticationOptions(opts);\n        try {\n            const response = await this.transporter.request(opts);\n            // Successful response.\n            const tokenRefreshResponse = response.data;\n            tokenRefreshResponse.res = response;\n            return tokenRefreshResponse;\n        }\n        catch (error) {\n            // Translate error to OAuthError.\n            if (error instanceof gaxios_1.GaxiosError && error.response) {\n                throw (0, oauth2common_1.getErrorFromOAuthErrorResponse)(error.response.data, \n                // Preserve other fields from the original error.\n                error);\n            }\n            // Request could fail before the server responds.\n            throw error;\n        }\n    }\n}\n/**\n * External Account Authorized User Client. This is used for OAuth2 credentials\n * sourced using external identities through Workforce Identity Federation.\n * Obtaining the initial access and refresh token can be done through the\n * Google Cloud CLI.\n */\nclass ExternalAccountAuthorizedUserClient extends authclient_1.AuthClient {\n    cachedAccessToken;\n    externalAccountAuthorizedUserHandler;\n    refreshToken;\n    /**\n     * Instantiates an ExternalAccountAuthorizedUserClient instances using the\n     * provided JSON object loaded from a credentials files.\n     * An error is throws if the credential is not valid.\n     * @param options The external account authorized user option object typically\n     *   from the external accoutn authorized user JSON credential file.\n     */\n    constructor(options) {\n        super(options);\n        if (options.universe_domain) {\n            this.universeDomain = options.universe_domain;\n        }\n        this.refreshToken = options.refresh_token;\n        const clientAuthentication = {\n            confidentialClientType: 'basic',\n            clientId: options.client_id,\n            clientSecret: options.client_secret,\n        };\n        this.externalAccountAuthorizedUserHandler =\n            new ExternalAccountAuthorizedUserHandler({\n                tokenRefreshEndpoint: options.token_url ??\n                    DEFAULT_TOKEN_URL.replace('{universeDomain}', this.universeDomain),\n                transporter: this.transporter,\n                clientAuthentication,\n            });\n        this.cachedAccessToken = null;\n        this.quotaProjectId = options.quota_project_id;\n        // As threshold could be zero,\n        // eagerRefreshThresholdMillis || EXPIRATION_TIME_OFFSET will override the\n        // zero value.\n        if (typeof options?.eagerRefreshThresholdMillis !== 'number') {\n            this.eagerRefreshThresholdMillis = baseexternalclient_1.EXPIRATION_TIME_OFFSET;\n        }\n        else {\n            this.eagerRefreshThresholdMillis = options\n                .eagerRefreshThresholdMillis;\n        }\n        this.forceRefreshOnFailure = !!options?.forceRefreshOnFailure;\n    }\n    async getAccessToken() {\n        // If cached access token is unavailable or expired, force refresh.\n        if (!this.cachedAccessToken || this.isExpired(this.cachedAccessToken)) {\n            await this.refreshAccessTokenAsync();\n        }\n        // Return GCP access token in GetAccessTokenResponse format.\n        return {\n            token: this.cachedAccessToken.access_token,\n            res: this.cachedAccessToken.res,\n        };\n    }\n    async getRequestHeaders() {\n        const accessTokenResponse = await this.getAccessToken();\n        const headers = new Headers({\n            authorization: `Bearer ${accessTokenResponse.token}`,\n        });\n        return this.addSharedMetadataHeaders(headers);\n    }\n    request(opts, callback) {\n        if (callback) {\n            this.requestAsync(opts).then(r => callback(null, r), e => {\n                return callback(e, e.response);\n            });\n        }\n        else {\n            return this.requestAsync(opts);\n        }\n    }\n    /**\n     * Authenticates the provided HTTP request, processes it and resolves with the\n     * returned response.\n     * @param opts The HTTP request options.\n     * @param reAuthRetried Whether the current attempt is a retry after a failed attempt due to an auth failure.\n     * @return A promise that resolves with the successful response.\n     */\n    async requestAsync(opts, reAuthRetried = false) {\n        let response;\n        try {\n            const requestHeaders = await this.getRequestHeaders();\n            opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers);\n            this.addUserProjectAndAuthHeaders(opts.headers, requestHeaders);\n            response = await this.transporter.request(opts);\n        }\n        catch (e) {\n            const res = e.response;\n            if (res) {\n                const statusCode = res.status;\n                // Retry the request for metadata if the following criteria are true:\n                // - We haven't already retried.  It only makes sense to retry once.\n                // - The response was a 401 or a 403\n                // - The request didn't send a readableStream\n                // - forceRefreshOnFailure is true\n                const isReadableStream = res.config.data instanceof stream.Readable;\n                const isAuthErr = statusCode === 401 || statusCode === 403;\n                if (!reAuthRetried &&\n                    isAuthErr &&\n                    !isReadableStream &&\n                    this.forceRefreshOnFailure) {\n                    await this.refreshAccessTokenAsync();\n                    return await this.requestAsync(opts, true);\n                }\n            }\n            throw e;\n        }\n        return response;\n    }\n    /**\n     * Forces token refresh, even if unexpired tokens are currently cached.\n     * @return A promise that resolves with the refreshed credential.\n     */\n    async refreshAccessTokenAsync() {\n        // Refresh the access token using the refresh token.\n        const refreshResponse = await this.externalAccountAuthorizedUserHandler.refreshToken(this.refreshToken);\n        this.cachedAccessToken = {\n            access_token: refreshResponse.access_token,\n            expiry_date: new Date().getTime() + refreshResponse.expires_in * 1000,\n            res: refreshResponse.res,\n        };\n        if (refreshResponse.refresh_token !== undefined) {\n            this.refreshToken = refreshResponse.refresh_token;\n        }\n        return this.cachedAccessToken;\n    }\n    /**\n     * Returns whether the provided credentials are expired or not.\n     * If there is no expiry time, assumes the token is not expired or expiring.\n     * @param credentials The credentials to check for expiration.\n     * @return Whether the credentials are expired or not.\n     */\n    isExpired(credentials) {\n        const now = new Date().getTime();\n        return credentials.expiry_date\n            ? now >= credentials.expiry_date - this.eagerRefreshThresholdMillis\n            : false;\n    }\n}\nexports.ExternalAccountAuthorizedUserClient = ExternalAccountAuthorizedUserClient;\n//# sourceMappingURL=externalAccountAuthorizedUserClient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mCAAmC,GAAG,QAAQ,qCAAqC,GAAG,KAAK;AACnG,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;CAEC,GACD,QAAQ,qCAAqC,GAAG;AAChD,MAAM,oBAAoB;AAC1B;;;CAGC,GACD,MAAM,6CAA6C,eAAe,sBAAsB;IACpF,CAAA,oBAAqB,CAAC;IACtB;;;;;;KAMC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,CAAA,oBAAqB,GAAG,QAAQ,oBAAoB;IAC7D;IACA;;;;;;;;KAQC,GACD,MAAM,aAAa,YAAY,EAAE,OAAO,EAAE;QACtC,MAAM,OAAO;YACT,GAAG,qCAAqC,YAAY;YACpD,KAAK,IAAI,CAAC,CAAA,oBAAqB;YAC/B,QAAQ;YACR;YACA,MAAM,IAAI,gBAAgB;gBACtB,YAAY;gBACZ,eAAe;YACnB;QACJ;QACA,aAAa,UAAU,CAAC,aAAa,CAAC,MAAM;QAC5C,qCAAqC;QACrC,IAAI,CAAC,gCAAgC,CAAC;QACtC,IAAI;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAChD,uBAAuB;YACvB,MAAM,uBAAuB,SAAS,IAAI;YAC1C,qBAAqB,GAAG,GAAG;YAC3B,OAAO;QACX,EACA,OAAO,OAAO;YACV,iCAAiC;YACjC,IAAI,iBAAiB,SAAS,WAAW,IAAI,MAAM,QAAQ,EAAE;gBACzD,MAAM,CAAC,GAAG,eAAe,8BAA8B,EAAE,MAAM,QAAQ,CAAC,IAAI,EAC5E,iDAAiD;gBACjD;YACJ;YACA,iDAAiD;YACjD,MAAM;QACV;IACJ;AACJ;AACA;;;;;CAKC,GACD,MAAM,4CAA4C,aAAa,UAAU;IACrE,kBAAkB;IAClB,qCAAqC;IACrC,aAAa;IACb;;;;;;KAMC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,QAAQ,eAAe,EAAE;YACzB,IAAI,CAAC,cAAc,GAAG,QAAQ,eAAe;QACjD;QACA,IAAI,CAAC,YAAY,GAAG,QAAQ,aAAa;QACzC,MAAM,uBAAuB;YACzB,wBAAwB;YACxB,UAAU,QAAQ,SAAS;YAC3B,cAAc,QAAQ,aAAa;QACvC;QACA,IAAI,CAAC,oCAAoC,GACrC,IAAI,qCAAqC;YACrC,sBAAsB,QAAQ,SAAS,IACnC,kBAAkB,OAAO,CAAC,oBAAoB,IAAI,CAAC,cAAc;YACrE,aAAa,IAAI,CAAC,WAAW;YAC7B;QACJ;QACJ,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,cAAc,GAAG,QAAQ,gBAAgB;QAC9C,8BAA8B;QAC9B,0EAA0E;QAC1E,cAAc;QACd,IAAI,OAAO,SAAS,gCAAgC,UAAU;YAC1D,IAAI,CAAC,2BAA2B,GAAG,qBAAqB,sBAAsB;QAClF,OACK;YACD,IAAI,CAAC,2BAA2B,GAAG,QAC9B,2BAA2B;QACpC;QACA,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,SAAS;IAC5C;IACA,MAAM,iBAAiB;QACnB,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,GAAG;YACnE,MAAM,IAAI,CAAC,uBAAuB;QACtC;QACA,4DAA4D;QAC5D,OAAO;YACH,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY;YAC1C,KAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG;QACnC;IACJ;IACA,MAAM,oBAAoB;QACtB,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc;QACrD,MAAM,UAAU,IAAI,QAAQ;YACxB,eAAe,CAAC,OAAO,EAAE,oBAAoB,KAAK,EAAE;QACxD;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,QAAQ,IAAI,EAAE,QAAQ,EAAE;QACpB,IAAI,UAAU;YACV,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI,CAAA;gBACjD,OAAO,SAAS,GAAG,EAAE,QAAQ;YACjC;QACJ,OACK;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B;IACJ;IACA;;;;;;KAMC,GACD,MAAM,aAAa,IAAI,EAAE,gBAAgB,KAAK,EAAE;QAC5C,IAAI;QACJ,IAAI;YACA,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB;YACnD,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO;YACxD,IAAI,CAAC,4BAA4B,CAAC,KAAK,OAAO,EAAE;YAChD,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC9C,EACA,OAAO,GAAG;YACN,MAAM,MAAM,EAAE,QAAQ;YACtB,IAAI,KAAK;gBACL,MAAM,aAAa,IAAI,MAAM;gBAC7B,qEAAqE;gBACrE,oEAAoE;gBACpE,oCAAoC;gBACpC,6CAA6C;gBAC7C,kCAAkC;gBAClC,MAAM,mBAAmB,IAAI,MAAM,CAAC,IAAI,YAAY,OAAO,QAAQ;gBACnE,MAAM,YAAY,eAAe,OAAO,eAAe;gBACvD,IAAI,CAAC,iBACD,aACA,CAAC,oBACD,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,MAAM,IAAI,CAAC,uBAAuB;oBAClC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM;gBACzC;YACJ;YACA,MAAM;QACV;QACA,OAAO;IACX;IACA;;;KAGC,GACD,MAAM,0BAA0B;QAC5B,oDAAoD;QACpD,MAAM,kBAAkB,MAAM,IAAI,CAAC,oCAAoC,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY;QACtG,IAAI,CAAC,iBAAiB,GAAG;YACrB,cAAc,gBAAgB,YAAY;YAC1C,aAAa,IAAI,OAAO,OAAO,KAAK,gBAAgB,UAAU,GAAG;YACjE,KAAK,gBAAgB,GAAG;QAC5B;QACA,IAAI,gBAAgB,aAAa,KAAK,WAAW;YAC7C,IAAI,CAAC,YAAY,GAAG,gBAAgB,aAAa;QACrD;QACA,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA;;;;;KAKC,GACD,UAAU,WAAW,EAAE;QACnB,MAAM,MAAM,IAAI,OAAO,OAAO;QAC9B,OAAO,YAAY,WAAW,GACxB,OAAO,YAAY,WAAW,GAAG,IAAI,CAAC,2BAA2B,GACjE;IACV;AACJ;AACA,QAAQ,mCAAmC,GAAG,qCAC9C,+DAA+D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/googleauth.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2019 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GoogleAuth = exports.GoogleAuthExceptionMessages = void 0;\nconst child_process_1 = require(\"child_process\");\nconst fs = require(\"fs\");\nconst gaxios_1 = require(\"gaxios\");\nconst gcpMetadata = require(\"gcp-metadata\");\nconst os = require(\"os\");\nconst path = require(\"path\");\nconst crypto_1 = require(\"../crypto/crypto\");\nconst computeclient_1 = require(\"./computeclient\");\nconst idtokenclient_1 = require(\"./idtokenclient\");\nconst envDetect_1 = require(\"./envDetect\");\nconst jwtclient_1 = require(\"./jwtclient\");\nconst refreshclient_1 = require(\"./refreshclient\");\nconst impersonated_1 = require(\"./impersonated\");\nconst externalclient_1 = require(\"./externalclient\");\nconst baseexternalclient_1 = require(\"./baseexternalclient\");\nconst authclient_1 = require(\"./authclient\");\nconst externalAccountAuthorizedUserClient_1 = require(\"./externalAccountAuthorizedUserClient\");\nconst util_1 = require(\"../util\");\nexports.GoogleAuthExceptionMessages = {\n    API_KEY_WITH_CREDENTIALS: 'API Keys and Credentials are mutually exclusive authentication methods and cannot be used together.',\n    NO_PROJECT_ID_FOUND: 'Unable to detect a Project Id in the current environment. \\n' +\n        'To learn more about authentication and Google APIs, visit: \\n' +\n        'https://cloud.google.com/docs/authentication/getting-started',\n    NO_CREDENTIALS_FOUND: 'Unable to find credentials in current environment. \\n' +\n        'To learn more about authentication and Google APIs, visit: \\n' +\n        'https://cloud.google.com/docs/authentication/getting-started',\n    NO_ADC_FOUND: 'Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.',\n    NO_UNIVERSE_DOMAIN_FOUND: 'Unable to detect a Universe Domain in the current environment.\\n' +\n        'To learn more about Universe Domain retrieval, visit: \\n' +\n        'https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys',\n};\nclass GoogleAuth {\n    /**\n     * Caches a value indicating whether the auth layer is running on Google\n     * Compute Engine.\n     * @private\n     */\n    checkIsGCE = undefined;\n    useJWTAccessWithScope;\n    defaultServicePath;\n    // Note:  this properly is only public to satisfy unit tests.\n    // https://github.com/Microsoft/TypeScript/issues/5228\n    get isGCE() {\n        return this.checkIsGCE;\n    }\n    _findProjectIdPromise;\n    _cachedProjectId;\n    // To save the contents of the JSON credential file\n    jsonContent = null;\n    apiKey;\n    cachedCredential = null;\n    /**\n     * A pending {@link AuthClient}. Used for concurrent {@link GoogleAuth.getClient} calls.\n     */\n    #pendingAuthClient = null;\n    /**\n     * Scopes populated by the client library by default. We differentiate between\n     * these and user defined scopes when deciding whether to use a self-signed JWT.\n     */\n    defaultScopes;\n    keyFilename;\n    scopes;\n    clientOptions = {};\n    /**\n     * Configuration is resolved in the following order of precedence:\n     * - {@link GoogleAuthOptions.credentials `credentials`}\n     * - {@link GoogleAuthOptions.keyFilename `keyFilename`}\n     * - {@link GoogleAuthOptions.keyFile `keyFile`}\n     *\n     * {@link GoogleAuthOptions.clientOptions `clientOptions`} are passed to the\n     * {@link AuthClient `AuthClient`s}.\n     *\n     * @param opts\n     */\n    constructor(opts = {}) {\n        this._cachedProjectId = opts.projectId || null;\n        this.cachedCredential = opts.authClient || null;\n        this.keyFilename = opts.keyFilename || opts.keyFile;\n        this.scopes = opts.scopes;\n        this.clientOptions = opts.clientOptions || {};\n        this.jsonContent = opts.credentials || null;\n        this.apiKey = opts.apiKey || this.clientOptions.apiKey || null;\n        // Cannot use both API Key + Credentials\n        if (this.apiKey && (this.jsonContent || this.clientOptions.credentials)) {\n            throw new RangeError(exports.GoogleAuthExceptionMessages.API_KEY_WITH_CREDENTIALS);\n        }\n        if (opts.universeDomain) {\n            this.clientOptions.universeDomain = opts.universeDomain;\n        }\n    }\n    // GAPIC client libraries should always use self-signed JWTs. The following\n    // variables are set on the JWT client in order to indicate the type of library,\n    // and sign the JWT with the correct audience and scopes (if not supplied).\n    setGapicJWTValues(client) {\n        client.defaultServicePath = this.defaultServicePath;\n        client.useJWTAccessWithScope = this.useJWTAccessWithScope;\n        client.defaultScopes = this.defaultScopes;\n    }\n    getProjectId(callback) {\n        if (callback) {\n            this.getProjectIdAsync().then(r => callback(null, r), callback);\n        }\n        else {\n            return this.getProjectIdAsync();\n        }\n    }\n    /**\n     * A temporary method for internal `getProjectId` usages where `null` is\n     * acceptable. In a future major release, `getProjectId` should return `null`\n     * (as the `Promise<string | null>` base signature describes) and this private\n     * method should be removed.\n     *\n     * @returns Promise that resolves with project id (or `null`)\n     */\n    async getProjectIdOptional() {\n        try {\n            return await this.getProjectId();\n        }\n        catch (e) {\n            if (e instanceof Error &&\n                e.message === exports.GoogleAuthExceptionMessages.NO_PROJECT_ID_FOUND) {\n                return null;\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    /**\n     * A private method for finding and caching a projectId.\n     *\n     * Supports environments in order of precedence:\n     * - GCLOUD_PROJECT or GOOGLE_CLOUD_PROJECT environment variable\n     * - GOOGLE_APPLICATION_CREDENTIALS JSON file\n     * - Cloud SDK: `gcloud config config-helper --format json`\n     * - GCE project ID from metadata server\n     *\n     * @returns projectId\n     */\n    async findAndCacheProjectId() {\n        let projectId = null;\n        projectId ||= await this.getProductionProjectId();\n        projectId ||= await this.getFileProjectId();\n        projectId ||= await this.getDefaultServiceProjectId();\n        projectId ||= await this.getGCEProjectId();\n        projectId ||= await this.getExternalAccountClientProjectId();\n        if (projectId) {\n            this._cachedProjectId = projectId;\n            return projectId;\n        }\n        else {\n            throw new Error(exports.GoogleAuthExceptionMessages.NO_PROJECT_ID_FOUND);\n        }\n    }\n    async getProjectIdAsync() {\n        if (this._cachedProjectId) {\n            return this._cachedProjectId;\n        }\n        if (!this._findProjectIdPromise) {\n            this._findProjectIdPromise = this.findAndCacheProjectId();\n        }\n        return this._findProjectIdPromise;\n    }\n    /**\n     * Retrieves a universe domain from the metadata server via\n     * {@link gcpMetadata.universe}.\n     *\n     * @returns a universe domain\n     */\n    async getUniverseDomainFromMetadataServer() {\n        let universeDomain;\n        try {\n            universeDomain = await gcpMetadata.universe('universe-domain');\n            universeDomain ||= authclient_1.DEFAULT_UNIVERSE;\n        }\n        catch (e) {\n            if (e && e?.response?.status === 404) {\n                universeDomain = authclient_1.DEFAULT_UNIVERSE;\n            }\n            else {\n                throw e;\n            }\n        }\n        return universeDomain;\n    }\n    /**\n     * Retrieves, caches, and returns the universe domain in the following order\n     * of precedence:\n     * - The universe domain in {@link GoogleAuth.clientOptions}\n     * - An existing or ADC {@link AuthClient}'s universe domain\n     * - {@link gcpMetadata.universe}, if {@link Compute} client\n     *\n     * @returns The universe domain\n     */\n    async getUniverseDomain() {\n        let universeDomain = (0, util_1.originalOrCamelOptions)(this.clientOptions).get('universe_domain');\n        try {\n            universeDomain ??= (await this.getClient()).universeDomain;\n        }\n        catch {\n            // client or ADC is not available\n            universeDomain ??= authclient_1.DEFAULT_UNIVERSE;\n        }\n        return universeDomain;\n    }\n    /**\n     * @returns Any scopes (user-specified or default scopes specified by the\n     *   client library) that need to be set on the current Auth client.\n     */\n    getAnyScopes() {\n        return this.scopes || this.defaultScopes;\n    }\n    getApplicationDefault(optionsOrCallback = {}, callback) {\n        let options;\n        if (typeof optionsOrCallback === 'function') {\n            callback = optionsOrCallback;\n        }\n        else {\n            options = optionsOrCallback;\n        }\n        if (callback) {\n            this.getApplicationDefaultAsync(options).then(r => callback(null, r.credential, r.projectId), callback);\n        }\n        else {\n            return this.getApplicationDefaultAsync(options);\n        }\n    }\n    async getApplicationDefaultAsync(options = {}) {\n        // If we've already got a cached credential, return it.\n        // This will also preserve one's configured quota project, in case they\n        // set one directly on the credential previously.\n        if (this.cachedCredential) {\n            // cache, while preserving existing quota project preferences\n            return await this.#prepareAndCacheClient(this.cachedCredential, null);\n        }\n        let credential;\n        // Check for the existence of a local environment variable pointing to the\n        // location of the credential file. This is typically used in local\n        // developer scenarios.\n        credential =\n            await this._tryGetApplicationCredentialsFromEnvironmentVariable(options);\n        if (credential) {\n            if (credential instanceof jwtclient_1.JWT) {\n                credential.scopes = this.scopes;\n            }\n            else if (credential instanceof baseexternalclient_1.BaseExternalAccountClient) {\n                credential.scopes = this.getAnyScopes();\n            }\n            return await this.#prepareAndCacheClient(credential);\n        }\n        // Look in the well-known credential file location.\n        credential =\n            await this._tryGetApplicationCredentialsFromWellKnownFile(options);\n        if (credential) {\n            if (credential instanceof jwtclient_1.JWT) {\n                credential.scopes = this.scopes;\n            }\n            else if (credential instanceof baseexternalclient_1.BaseExternalAccountClient) {\n                credential.scopes = this.getAnyScopes();\n            }\n            return await this.#prepareAndCacheClient(credential);\n        }\n        // Determine if we're running on GCE.\n        if (await this._checkIsGCE()) {\n            options.scopes = this.getAnyScopes();\n            return await this.#prepareAndCacheClient(new computeclient_1.Compute(options));\n        }\n        throw new Error(exports.GoogleAuthExceptionMessages.NO_ADC_FOUND);\n    }\n    async #prepareAndCacheClient(credential, quotaProjectIdOverride = process.env['GOOGLE_CLOUD_QUOTA_PROJECT'] || null) {\n        const projectId = await this.getProjectIdOptional();\n        if (quotaProjectIdOverride) {\n            credential.quotaProjectId = quotaProjectIdOverride;\n        }\n        this.cachedCredential = credential;\n        return { credential, projectId };\n    }\n    /**\n     * Determines whether the auth layer is running on Google Compute Engine.\n     * Checks for GCP Residency, then fallback to checking if metadata server\n     * is available.\n     *\n     * @returns A promise that resolves with the boolean.\n     * @api private\n     */\n    async _checkIsGCE() {\n        if (this.checkIsGCE === undefined) {\n            this.checkIsGCE =\n                gcpMetadata.getGCPResidency() || (await gcpMetadata.isAvailable());\n        }\n        return this.checkIsGCE;\n    }\n    /**\n     * Attempts to load default credentials from the environment variable path..\n     * @returns Promise that resolves with the OAuth2Client or null.\n     * @api private\n     */\n    async _tryGetApplicationCredentialsFromEnvironmentVariable(options) {\n        const credentialsPath = process.env['GOOGLE_APPLICATION_CREDENTIALS'] ||\n            process.env['google_application_credentials'];\n        if (!credentialsPath || credentialsPath.length === 0) {\n            return null;\n        }\n        try {\n            return this._getApplicationCredentialsFromFilePath(credentialsPath, options);\n        }\n        catch (e) {\n            if (e instanceof Error) {\n                e.message = `Unable to read the credential file specified by the GOOGLE_APPLICATION_CREDENTIALS environment variable: ${e.message}`;\n            }\n            throw e;\n        }\n    }\n    /**\n     * Attempts to load default credentials from a well-known file location\n     * @return Promise that resolves with the OAuth2Client or null.\n     * @api private\n     */\n    async _tryGetApplicationCredentialsFromWellKnownFile(options) {\n        // First, figure out the location of the file, depending upon the OS type.\n        let location = null;\n        if (this._isWindows()) {\n            // Windows\n            location = process.env['APPDATA'];\n        }\n        else {\n            // Linux or Mac\n            const home = process.env['HOME'];\n            if (home) {\n                location = path.join(home, '.config');\n            }\n        }\n        // If we found the root path, expand it.\n        if (location) {\n            location = path.join(location, 'gcloud', 'application_default_credentials.json');\n            if (!fs.existsSync(location)) {\n                location = null;\n            }\n        }\n        // The file does not exist.\n        if (!location) {\n            return null;\n        }\n        // The file seems to exist. Try to use it.\n        const client = await this._getApplicationCredentialsFromFilePath(location, options);\n        return client;\n    }\n    /**\n     * Attempts to load default credentials from a file at the given path..\n     * @param filePath The path to the file to read.\n     * @returns Promise that resolves with the OAuth2Client\n     * @api private\n     */\n    async _getApplicationCredentialsFromFilePath(filePath, options = {}) {\n        // Make sure the path looks like a string.\n        if (!filePath || filePath.length === 0) {\n            throw new Error('The file path is invalid.');\n        }\n        // Make sure there is a file at the path. lstatSync will throw if there is\n        // nothing there.\n        try {\n            // Resolve path to actual file in case of symlink. Expect a thrown error\n            // if not resolvable.\n            filePath = fs.realpathSync(filePath);\n            if (!fs.lstatSync(filePath).isFile()) {\n                throw new Error();\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                err.message = `The file at ${filePath} does not exist, or it is not a file. ${err.message}`;\n            }\n            throw err;\n        }\n        // Now open a read stream on the file, and parse it.\n        const readStream = fs.createReadStream(filePath);\n        return this.fromStream(readStream, options);\n    }\n    /**\n     * Create a credentials instance using a given impersonated input options.\n     * @param json The impersonated input object.\n     * @returns JWT or UserRefresh Client with data\n     */\n    fromImpersonatedJSON(json) {\n        if (!json) {\n            throw new Error('Must pass in a JSON object containing an  impersonated refresh token');\n        }\n        if (json.type !== impersonated_1.IMPERSONATED_ACCOUNT_TYPE) {\n            throw new Error(`The incoming JSON object does not have the \"${impersonated_1.IMPERSONATED_ACCOUNT_TYPE}\" type`);\n        }\n        if (!json.source_credentials) {\n            throw new Error('The incoming JSON object does not contain a source_credentials field');\n        }\n        if (!json.service_account_impersonation_url) {\n            throw new Error('The incoming JSON object does not contain a service_account_impersonation_url field');\n        }\n        const sourceClient = this.fromJSON(json.source_credentials);\n        if (json.service_account_impersonation_url?.length > 256) {\n            /**\n             * Prevents DOS attacks.\n             * @see {@link https://github.com/googleapis/google-auth-library-nodejs/security/code-scanning/85}\n             **/\n            throw new RangeError(`Target principal is too long: ${json.service_account_impersonation_url}`);\n        }\n        // Extract service account from service_account_impersonation_url\n        const targetPrincipal = /(?<target>[^/]+):(generateAccessToken|generateIdToken)$/.exec(json.service_account_impersonation_url)?.groups?.target;\n        if (!targetPrincipal) {\n            throw new RangeError(`Cannot extract target principal from ${json.service_account_impersonation_url}`);\n        }\n        const targetScopes = this.getAnyScopes() ?? [];\n        return new impersonated_1.Impersonated({\n            ...json,\n            sourceClient,\n            targetPrincipal,\n            targetScopes: Array.isArray(targetScopes) ? targetScopes : [targetScopes],\n        });\n    }\n    /**\n     * Create a credentials instance using the given input options.\n     * This client is not cached.\n     *\n     * **Important**: If you accept a credential configuration (credential JSON/File/Stream) from an external source for authentication to Google Cloud, you must validate it before providing it to any Google API or library. Providing an unvalidated credential configuration to Google APIs can compromise the security of your systems and data. For more information, refer to {@link https://cloud.google.com/docs/authentication/external/externally-sourced-credentials Validate credential configurations from external sources}.\n     *\n     * @param json The input object.\n     * @param options The JWT or UserRefresh options for the client\n     * @returns JWT or UserRefresh Client with data\n     */\n    fromJSON(json, options = {}) {\n        let client;\n        // user's preferred universe domain\n        const preferredUniverseDomain = (0, util_1.originalOrCamelOptions)(options).get('universe_domain');\n        if (json.type === refreshclient_1.USER_REFRESH_ACCOUNT_TYPE) {\n            client = new refreshclient_1.UserRefreshClient(options);\n            client.fromJSON(json);\n        }\n        else if (json.type === impersonated_1.IMPERSONATED_ACCOUNT_TYPE) {\n            client = this.fromImpersonatedJSON(json);\n        }\n        else if (json.type === baseexternalclient_1.EXTERNAL_ACCOUNT_TYPE) {\n            client = externalclient_1.ExternalAccountClient.fromJSON({\n                ...json,\n                ...options,\n            });\n            client.scopes = this.getAnyScopes();\n        }\n        else if (json.type === externalAccountAuthorizedUserClient_1.EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE) {\n            client = new externalAccountAuthorizedUserClient_1.ExternalAccountAuthorizedUserClient({\n                ...json,\n                ...options,\n            });\n        }\n        else {\n            options.scopes = this.scopes;\n            client = new jwtclient_1.JWT(options);\n            this.setGapicJWTValues(client);\n            client.fromJSON(json);\n        }\n        if (preferredUniverseDomain) {\n            client.universeDomain = preferredUniverseDomain;\n        }\n        return client;\n    }\n    /**\n     * Return a JWT or UserRefreshClient from JavaScript object, caching both the\n     * object used to instantiate and the client.\n     * @param json The input object.\n     * @param options The JWT or UserRefresh options for the client\n     * @returns JWT or UserRefresh Client with data\n     */\n    _cacheClientFromJSON(json, options) {\n        const client = this.fromJSON(json, options);\n        // cache both raw data used to instantiate client and client itself.\n        this.jsonContent = json;\n        this.cachedCredential = client;\n        return client;\n    }\n    fromStream(inputStream, optionsOrCallback = {}, callback) {\n        let options = {};\n        if (typeof optionsOrCallback === 'function') {\n            callback = optionsOrCallback;\n        }\n        else {\n            options = optionsOrCallback;\n        }\n        if (callback) {\n            this.fromStreamAsync(inputStream, options).then(r => callback(null, r), callback);\n        }\n        else {\n            return this.fromStreamAsync(inputStream, options);\n        }\n    }\n    fromStreamAsync(inputStream, options) {\n        return new Promise((resolve, reject) => {\n            if (!inputStream) {\n                throw new Error('Must pass in a stream containing the Google auth settings.');\n            }\n            const chunks = [];\n            inputStream\n                .setEncoding('utf8')\n                .on('error', reject)\n                .on('data', chunk => chunks.push(chunk))\n                .on('end', () => {\n                try {\n                    try {\n                        const data = JSON.parse(chunks.join(''));\n                        const r = this._cacheClientFromJSON(data, options);\n                        return resolve(r);\n                    }\n                    catch (err) {\n                        // If we failed parsing this.keyFileName, assume that it\n                        // is a PEM or p12 certificate:\n                        if (!this.keyFilename)\n                            throw err;\n                        const client = new jwtclient_1.JWT({\n                            ...this.clientOptions,\n                            keyFile: this.keyFilename,\n                        });\n                        this.cachedCredential = client;\n                        this.setGapicJWTValues(client);\n                        return resolve(client);\n                    }\n                }\n                catch (err) {\n                    return reject(err);\n                }\n            });\n        });\n    }\n    /**\n     * Create a credentials instance using the given API key string.\n     * The created client is not cached. In order to create and cache it use the {@link GoogleAuth.getClient `getClient`} method after first providing an {@link GoogleAuth.apiKey `apiKey`}.\n     *\n     * @param apiKey The API key string\n     * @param options An optional options object.\n     * @returns A JWT loaded from the key\n     */\n    fromAPIKey(apiKey, options = {}) {\n        return new jwtclient_1.JWT({ ...options, apiKey });\n    }\n    /**\n     * Determines whether the current operating system is Windows.\n     * @api private\n     */\n    _isWindows() {\n        const sys = os.platform();\n        if (sys && sys.length >= 3) {\n            if (sys.substring(0, 3).toLowerCase() === 'win') {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Run the Google Cloud SDK command that prints the default project ID\n     */\n    async getDefaultServiceProjectId() {\n        return new Promise(resolve => {\n            (0, child_process_1.exec)('gcloud config config-helper --format json', (err, stdout) => {\n                if (!err && stdout) {\n                    try {\n                        const projectId = JSON.parse(stdout).configuration.properties.core.project;\n                        resolve(projectId);\n                        return;\n                    }\n                    catch (e) {\n                        // ignore errors\n                    }\n                }\n                resolve(null);\n            });\n        });\n    }\n    /**\n     * Loads the project id from environment variables.\n     * @api private\n     */\n    getProductionProjectId() {\n        return (process.env['GCLOUD_PROJECT'] ||\n            process.env['GOOGLE_CLOUD_PROJECT'] ||\n            process.env['gcloud_project'] ||\n            process.env['google_cloud_project']);\n    }\n    /**\n     * Loads the project id from the GOOGLE_APPLICATION_CREDENTIALS json file.\n     * @api private\n     */\n    async getFileProjectId() {\n        if (this.cachedCredential) {\n            // Try to read the project ID from the cached credentials file\n            return this.cachedCredential.projectId;\n        }\n        // Ensure the projectId is loaded from the keyFile if available.\n        if (this.keyFilename) {\n            const creds = await this.getClient();\n            if (creds && creds.projectId) {\n                return creds.projectId;\n            }\n        }\n        // Try to load a credentials file and read its project ID\n        const r = await this._tryGetApplicationCredentialsFromEnvironmentVariable();\n        if (r) {\n            return r.projectId;\n        }\n        else {\n            return null;\n        }\n    }\n    /**\n     * Gets the project ID from external account client if available.\n     */\n    async getExternalAccountClientProjectId() {\n        if (!this.jsonContent || this.jsonContent.type !== baseexternalclient_1.EXTERNAL_ACCOUNT_TYPE) {\n            return null;\n        }\n        const creds = await this.getClient();\n        // Do not suppress the underlying error, as the error could contain helpful\n        // information for debugging and fixing. This is especially true for\n        // external account creds as in order to get the project ID, the following\n        // operations have to succeed:\n        // 1. Valid credentials file should be supplied.\n        // 2. Ability to retrieve access tokens from STS token exchange API.\n        // 3. Ability to exchange for service account impersonated credentials (if\n        //    enabled).\n        // 4. Ability to get project info using the access token from step 2 or 3.\n        // Without surfacing the error, it is harder for developers to determine\n        // which step went wrong.\n        return await creds.getProjectId();\n    }\n    /**\n     * Gets the Compute Engine project ID if it can be inferred.\n     */\n    async getGCEProjectId() {\n        try {\n            const r = await gcpMetadata.project('project-id');\n            return r;\n        }\n        catch (e) {\n            // Ignore any errors\n            return null;\n        }\n    }\n    getCredentials(callback) {\n        if (callback) {\n            this.getCredentialsAsync().then(r => callback(null, r), callback);\n        }\n        else {\n            return this.getCredentialsAsync();\n        }\n    }\n    async getCredentialsAsync() {\n        const client = await this.getClient();\n        if (client instanceof impersonated_1.Impersonated) {\n            return { client_email: client.getTargetPrincipal() };\n        }\n        if (client instanceof baseexternalclient_1.BaseExternalAccountClient) {\n            const serviceAccountEmail = client.getServiceAccountEmail();\n            if (serviceAccountEmail) {\n                return {\n                    client_email: serviceAccountEmail,\n                    universe_domain: client.universeDomain,\n                };\n            }\n        }\n        if (this.jsonContent) {\n            return {\n                client_email: this.jsonContent.client_email,\n                private_key: this.jsonContent.private_key,\n                universe_domain: this.jsonContent.universe_domain,\n            };\n        }\n        if (await this._checkIsGCE()) {\n            const [client_email, universe_domain] = await Promise.all([\n                gcpMetadata.instance('service-accounts/default/email'),\n                this.getUniverseDomain(),\n            ]);\n            return { client_email, universe_domain };\n        }\n        throw new Error(exports.GoogleAuthExceptionMessages.NO_CREDENTIALS_FOUND);\n    }\n    /**\n     * Automatically obtain an {@link AuthClient `AuthClient`} based on the\n     * provided configuration. If no options were passed, use Application\n     * Default Credentials.\n     */\n    async getClient() {\n        if (this.cachedCredential) {\n            return this.cachedCredential;\n        }\n        // Use an existing auth client request, or cache a new one\n        this.#pendingAuthClient =\n            this.#pendingAuthClient || this.#determineClient();\n        try {\n            return await this.#pendingAuthClient;\n        }\n        finally {\n            // reset the pending auth client in case it is changed later\n            this.#pendingAuthClient = null;\n        }\n    }\n    async #determineClient() {\n        if (this.jsonContent) {\n            return this._cacheClientFromJSON(this.jsonContent, this.clientOptions);\n        }\n        else if (this.keyFilename) {\n            const filePath = path.resolve(this.keyFilename);\n            const stream = fs.createReadStream(filePath);\n            return await this.fromStreamAsync(stream, this.clientOptions);\n        }\n        else if (this.apiKey) {\n            const client = await this.fromAPIKey(this.apiKey, this.clientOptions);\n            client.scopes = this.scopes;\n            const { credential } = await this.#prepareAndCacheClient(client);\n            return credential;\n        }\n        else {\n            const { credential } = await this.getApplicationDefaultAsync(this.clientOptions);\n            return credential;\n        }\n    }\n    /**\n     * Creates a client which will fetch an ID token for authorization.\n     * @param targetAudience the audience for the fetched ID token.\n     * @returns IdTokenClient for making HTTP calls authenticated with ID tokens.\n     */\n    async getIdTokenClient(targetAudience) {\n        const client = await this.getClient();\n        if (!('fetchIdToken' in client)) {\n            throw new Error('Cannot fetch ID token in this environment, use GCE or set the GOOGLE_APPLICATION_CREDENTIALS environment variable to a service account credentials JSON file.');\n        }\n        return new idtokenclient_1.IdTokenClient({ targetAudience, idTokenProvider: client });\n    }\n    /**\n     * Automatically obtain application default credentials, and return\n     * an access token for making requests.\n     */\n    async getAccessToken() {\n        const client = await this.getClient();\n        return (await client.getAccessToken()).token;\n    }\n    /**\n     * Obtain the HTTP headers that will provide authorization for a given\n     * request.\n     */\n    async getRequestHeaders(url) {\n        const client = await this.getClient();\n        return client.getRequestHeaders(url);\n    }\n    /**\n     * Obtain credentials for a request, then attach the appropriate headers to\n     * the request options.\n     * @param opts Axios or Request options on which to attach the headers\n     */\n    async authorizeRequest(opts = {}) {\n        const url = opts.url;\n        const client = await this.getClient();\n        const headers = await client.getRequestHeaders(url);\n        opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers, headers);\n        return opts;\n    }\n    /**\n     * A {@link fetch `fetch`} compliant API for {@link GoogleAuth}.\n     *\n     * @see {@link GoogleAuth.request} for the classic method.\n     *\n     * @remarks\n     *\n     * This is useful as a drop-in replacement for `fetch` API usage.\n     *\n     * @example\n     *\n     * ```ts\n     * const auth = new GoogleAuth();\n     * const fetchWithAuth: typeof fetch = (...args) => auth.fetch(...args);\n     * await fetchWithAuth('https://example.com');\n     * ```\n     *\n     * @param args `fetch` API or {@link Gaxios.fetch `Gaxios#fetch`} parameters\n     * @returns the {@link GaxiosResponse} with Gaxios-added properties\n     */\n    async fetch(...args) {\n        const client = await this.getClient();\n        return client.fetch(...args);\n    }\n    /**\n     * Automatically obtain application default credentials, and make an\n     * HTTP request using the given options.\n     *\n     * @see {@link GoogleAuth.fetch} for the modern method.\n     *\n     * @param opts Axios request options for the HTTP request.\n     */\n    async request(opts) {\n        const client = await this.getClient();\n        return client.request(opts);\n    }\n    /**\n     * Determine the compute environment in which the code is running.\n     */\n    getEnv() {\n        return (0, envDetect_1.getEnv)();\n    }\n    /**\n     * Sign the given data with the current private key, or go out\n     * to the IAM API to sign it.\n     * @param data The data to be signed.\n     * @param endpoint A custom endpoint to use.\n     *\n     * @example\n     * ```\n     * sign('data', 'https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/');\n     * ```\n     */\n    async sign(data, endpoint) {\n        const client = await this.getClient();\n        const universe = await this.getUniverseDomain();\n        endpoint =\n            endpoint ||\n                `https://iamcredentials.${universe}/v1/projects/-/serviceAccounts/`;\n        if (client instanceof impersonated_1.Impersonated) {\n            const signed = await client.sign(data);\n            return signed.signedBlob;\n        }\n        const crypto = (0, crypto_1.createCrypto)();\n        if (client instanceof jwtclient_1.JWT && client.key) {\n            const sign = await crypto.sign(client.key, data);\n            return sign;\n        }\n        const creds = await this.getCredentials();\n        if (!creds.client_email) {\n            throw new Error('Cannot sign data without `client_email`.');\n        }\n        return this.signBlob(crypto, creds.client_email, data, endpoint);\n    }\n    async signBlob(crypto, emailOrUniqueId, data, endpoint) {\n        const url = new URL(endpoint + `${emailOrUniqueId}:signBlob`);\n        const res = await this.request({\n            method: 'POST',\n            url: url.href,\n            data: {\n                payload: crypto.encodeBase64StringUtf8(data),\n            },\n            retry: true,\n            retryConfig: {\n                httpMethodsToRetry: ['POST'],\n            },\n        });\n        return res.data.signedBlob;\n    }\n}\nexports.GoogleAuth = GoogleAuth;\n//# sourceMappingURL=googleauth.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,QAAQ,2BAA2B,GAAG,KAAK;AAChE,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,2BAA2B,GAAG;IAClC,0BAA0B;IAC1B,qBAAqB,iEACjB,kEACA;IACJ,sBAAsB,0DAClB,kEACA;IACJ,cAAc;IACd,0BAA0B,qEACtB,6DACA;AACR;AACA,MAAM;IACF;;;;KAIC,GACD,aAAa,UAAU;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,6DAA6D;IAC7D,sDAAsD;IACtD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,sBAAsB;IACtB,iBAAiB;IACjB,mDAAmD;IACnD,cAAc,KAAK;IACnB,OAAO;IACP,mBAAmB,KAAK;IACxB;;KAEC,GACD,CAAA,iBAAkB,GAAG,KAAK;IAC1B;;;KAGC,GACD,cAAc;IACd,YAAY;IACZ,OAAO;IACP,gBAAgB,CAAC,EAAE;IACnB;;;;;;;;;;KAUC,GACD,YAAY,OAAO,CAAC,CAAC,CAAE;QACnB,IAAI,CAAC,gBAAgB,GAAG,KAAK,SAAS,IAAI;QAC1C,IAAI,CAAC,gBAAgB,GAAG,KAAK,UAAU,IAAI;QAC3C,IAAI,CAAC,WAAW,GAAG,KAAK,WAAW,IAAI,KAAK,OAAO;QACnD,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa,IAAI,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,KAAK,WAAW,IAAI;QACvC,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI;QAC1D,wCAAwC;QACxC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG;YACrE,MAAM,IAAI,WAAW,QAAQ,2BAA2B,CAAC,wBAAwB;QACrF;QACA,IAAI,KAAK,cAAc,EAAE;YACrB,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,KAAK,cAAc;QAC3D;IACJ;IACA,2EAA2E;IAC3E,gFAAgF;IAChF,2EAA2E;IAC3E,kBAAkB,MAAM,EAAE;QACtB,OAAO,kBAAkB,GAAG,IAAI,CAAC,kBAAkB;QACnD,OAAO,qBAAqB,GAAG,IAAI,CAAC,qBAAqB;QACzD,OAAO,aAAa,GAAG,IAAI,CAAC,aAAa;IAC7C;IACA,aAAa,QAAQ,EAAE;QACnB,IAAI,UAAU;YACV,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI;QAC1D,OACK;YACD,OAAO,IAAI,CAAC,iBAAiB;QACjC;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,uBAAuB;QACzB,IAAI;YACA,OAAO,MAAM,IAAI,CAAC,YAAY;QAClC,EACA,OAAO,GAAG;YACN,IAAI,aAAa,SACb,EAAE,OAAO,KAAK,QAAQ,2BAA2B,CAAC,mBAAmB,EAAE;gBACvE,OAAO;YACX,OACK;gBACD,MAAM;YACV;QACJ;IACJ;IACA;;;;;;;;;;KAUC,GACD,MAAM,wBAAwB;QAC1B,IAAI,YAAY;QAChB,cAAc,MAAM,IAAI,CAAC,sBAAsB;QAC/C,cAAc,MAAM,IAAI,CAAC,gBAAgB;QACzC,cAAc,MAAM,IAAI,CAAC,0BAA0B;QACnD,cAAc,MAAM,IAAI,CAAC,eAAe;QACxC,cAAc,MAAM,IAAI,CAAC,iCAAiC;QAC1D,IAAI,WAAW;YACX,IAAI,CAAC,gBAAgB,GAAG;YACxB,OAAO;QACX,OACK;YACD,MAAM,IAAI,MAAM,QAAQ,2BAA2B,CAAC,mBAAmB;QAC3E;IACJ;IACA,MAAM,oBAAoB;QACtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB;QAC3D;QACA,OAAO,IAAI,CAAC,qBAAqB;IACrC;IACA;;;;;KAKC,GACD,MAAM,sCAAsC;QACxC,IAAI;QACJ,IAAI;YACA,iBAAiB,MAAM,YAAY,QAAQ,CAAC;YAC5C,mBAAmB,aAAa,gBAAgB;QACpD,EACA,OAAO,GAAG;YACN,IAAI,KAAK,GAAG,UAAU,WAAW,KAAK;gBAClC,iBAAiB,aAAa,gBAAgB;YAClD,OACK;gBACD,MAAM;YACV;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,MAAM,oBAAoB;QACtB,IAAI,iBAAiB,CAAC,GAAG,OAAO,sBAAsB,EAAE,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC;QAChF,IAAI;YACA,mBAAmB,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE,cAAc;QAC9D,EACA,OAAM;YACF,iCAAiC;YACjC,mBAAmB,aAAa,gBAAgB;QACpD;QACA,OAAO;IACX;IACA;;;KAGC,GACD,eAAe;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;IAC5C;IACA,sBAAsB,oBAAoB,CAAC,CAAC,EAAE,QAAQ,EAAE;QACpD,IAAI;QACJ,IAAI,OAAO,sBAAsB,YAAY;YACzC,WAAW;QACf,OACK;YACD,UAAU;QACd;QACA,IAAI,UAAU;YACV,IAAI,CAAC,0BAA0B,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,EAAE,UAAU,EAAE,EAAE,SAAS,GAAG;QAClG,OACK;YACD,OAAO,IAAI,CAAC,0BAA0B,CAAC;QAC3C;IACJ;IACA,MAAM,2BAA2B,UAAU,CAAC,CAAC,EAAE;QAC3C,uDAAuD;QACvD,uEAAuE;QACvE,iDAAiD;QACjD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,6DAA6D;YAC7D,OAAO,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACpE;QACA,IAAI;QACJ,0EAA0E;QAC1E,mEAAmE;QACnE,uBAAuB;QACvB,aACI,MAAM,IAAI,CAAC,oDAAoD,CAAC;QACpE,IAAI,YAAY;YACZ,IAAI,sBAAsB,YAAY,GAAG,EAAE;gBACvC,WAAW,MAAM,GAAG,IAAI,CAAC,MAAM;YACnC,OACK,IAAI,sBAAsB,qBAAqB,yBAAyB,EAAE;gBAC3E,WAAW,MAAM,GAAG,IAAI,CAAC,YAAY;YACzC;YACA,OAAO,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC;QAC7C;QACA,mDAAmD;QACnD,aACI,MAAM,IAAI,CAAC,8CAA8C,CAAC;QAC9D,IAAI,YAAY;YACZ,IAAI,sBAAsB,YAAY,GAAG,EAAE;gBACvC,WAAW,MAAM,GAAG,IAAI,CAAC,MAAM;YACnC,OACK,IAAI,sBAAsB,qBAAqB,yBAAyB,EAAE;gBAC3E,WAAW,MAAM,GAAG,IAAI,CAAC,YAAY;YACzC;YACA,OAAO,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC;QAC7C;QACA,qCAAqC;QACrC,IAAI,MAAM,IAAI,CAAC,WAAW,IAAI;YAC1B,QAAQ,MAAM,GAAG,IAAI,CAAC,YAAY;YAClC,OAAO,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC,IAAI,gBAAgB,OAAO,CAAC;QACzE;QACA,MAAM,IAAI,MAAM,QAAQ,2BAA2B,CAAC,YAAY;IACpE;IACA,MAAM,CAAA,qBAAsB,CAAC,UAAU,EAAE,yBAAyB,QAAQ,GAAG,CAAC,6BAA6B,IAAI,IAAI;QAC/G,MAAM,YAAY,MAAM,IAAI,CAAC,oBAAoB;QACjD,IAAI,wBAAwB;YACxB,WAAW,cAAc,GAAG;QAChC;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO;YAAE;YAAY;QAAU;IACnC;IACA;;;;;;;KAOC,GACD,MAAM,cAAc;QAChB,IAAI,IAAI,CAAC,UAAU,KAAK,WAAW;YAC/B,IAAI,CAAC,UAAU,GACX,YAAY,eAAe,MAAO,MAAM,YAAY,WAAW;QACvE;QACA,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;;;;KAIC,GACD,MAAM,qDAAqD,OAAO,EAAE;QAChE,MAAM,kBAAkB,QAAQ,GAAG,CAAC,iCAAiC,IACjE,QAAQ,GAAG,CAAC,iCAAiC;QACjD,IAAI,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,GAAG;YAClD,OAAO;QACX;QACA,IAAI;YACA,OAAO,IAAI,CAAC,sCAAsC,CAAC,iBAAiB;QACxE,EACA,OAAO,GAAG;YACN,IAAI,aAAa,OAAO;gBACpB,EAAE,OAAO,GAAG,CAAC,yGAAyG,EAAE,EAAE,OAAO,EAAE;YACvI;YACA,MAAM;QACV;IACJ;IACA;;;;KAIC,GACD,MAAM,+CAA+C,OAAO,EAAE;QAC1D,0EAA0E;QAC1E,IAAI,WAAW;QACf,IAAI,IAAI,CAAC,UAAU,IAAI;YACnB,UAAU;YACV,WAAW,QAAQ,GAAG,CAAC,UAAU;QACrC,OACK;YACD,eAAe;YACf,MAAM,OAAO,QAAQ,GAAG,CAAC,OAAO;YAChC,IAAI,MAAM;gBACN,WAAW,KAAK,IAAI,CAAC,MAAM;YAC/B;QACJ;QACA,wCAAwC;QACxC,IAAI,UAAU;YACV,WAAW,KAAK,IAAI,CAAC,UAAU,UAAU;YACzC,IAAI,CAAC,GAAG,UAAU,CAAC,WAAW;gBAC1B,WAAW;YACf;QACJ;QACA,2BAA2B;QAC3B,IAAI,CAAC,UAAU;YACX,OAAO;QACX;QACA,0CAA0C;QAC1C,MAAM,SAAS,MAAM,IAAI,CAAC,sCAAsC,CAAC,UAAU;QAC3E,OAAO;IACX;IACA;;;;;KAKC,GACD,MAAM,uCAAuC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE;QACjE,0CAA0C;QAC1C,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACpC,MAAM,IAAI,MAAM;QACpB;QACA,0EAA0E;QAC1E,iBAAiB;QACjB,IAAI;YACA,wEAAwE;YACxE,qBAAqB;YACrB,WAAW,GAAG,YAAY,CAAC;YAC3B,IAAI,CAAC,GAAG,SAAS,CAAC,UAAU,MAAM,IAAI;gBAClC,MAAM,IAAI;YACd;QACJ,EACA,OAAO,KAAK;YACR,IAAI,eAAe,OAAO;gBACtB,IAAI,OAAO,GAAG,CAAC,YAAY,EAAE,SAAS,sCAAsC,EAAE,IAAI,OAAO,EAAE;YAC/F;YACA,MAAM;QACV;QACA,oDAAoD;QACpD,MAAM,aAAa,GAAG,gBAAgB,CAAC;QACvC,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY;IACvC;IACA;;;;KAIC,GACD,qBAAqB,IAAI,EAAE;QACvB,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,KAAK,IAAI,KAAK,eAAe,yBAAyB,EAAE;YACxD,MAAM,IAAI,MAAM,CAAC,4CAA4C,EAAE,eAAe,yBAAyB,CAAC,MAAM,CAAC;QACnH;QACA,IAAI,CAAC,KAAK,kBAAkB,EAAE;YAC1B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,iCAAiC,EAAE;YACzC,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,KAAK,kBAAkB;QAC1D,IAAI,KAAK,iCAAiC,EAAE,SAAS,KAAK;YACtD;;;cAGE,GACF,MAAM,IAAI,WAAW,CAAC,8BAA8B,EAAE,KAAK,iCAAiC,EAAE;QAClG;QACA,iEAAiE;QACjE,MAAM,kBAAkB,0DAA0D,IAAI,CAAC,KAAK,iCAAiC,GAAG,QAAQ;QACxI,IAAI,CAAC,iBAAiB;YAClB,MAAM,IAAI,WAAW,CAAC,qCAAqC,EAAE,KAAK,iCAAiC,EAAE;QACzG;QACA,MAAM,eAAe,IAAI,CAAC,YAAY,MAAM,EAAE;QAC9C,OAAO,IAAI,eAAe,YAAY,CAAC;YACnC,GAAG,IAAI;YACP;YACA;YACA,cAAc,MAAM,OAAO,CAAC,gBAAgB,eAAe;gBAAC;aAAa;QAC7E;IACJ;IACA;;;;;;;;;KASC,GACD,SAAS,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE;QACzB,IAAI;QACJ,mCAAmC;QACnC,MAAM,0BAA0B,CAAC,GAAG,OAAO,sBAAsB,EAAE,SAAS,GAAG,CAAC;QAChF,IAAI,KAAK,IAAI,KAAK,gBAAgB,yBAAyB,EAAE;YACzD,SAAS,IAAI,gBAAgB,iBAAiB,CAAC;YAC/C,OAAO,QAAQ,CAAC;QACpB,OACK,IAAI,KAAK,IAAI,KAAK,eAAe,yBAAyB,EAAE;YAC7D,SAAS,IAAI,CAAC,oBAAoB,CAAC;QACvC,OACK,IAAI,KAAK,IAAI,KAAK,qBAAqB,qBAAqB,EAAE;YAC/D,SAAS,iBAAiB,qBAAqB,CAAC,QAAQ,CAAC;gBACrD,GAAG,IAAI;gBACP,GAAG,OAAO;YACd;YACA,OAAO,MAAM,GAAG,IAAI,CAAC,YAAY;QACrC,OACK,IAAI,KAAK,IAAI,KAAK,sCAAsC,qCAAqC,EAAE;YAChG,SAAS,IAAI,sCAAsC,mCAAmC,CAAC;gBACnF,GAAG,IAAI;gBACP,GAAG,OAAO;YACd;QACJ,OACK;YACD,QAAQ,MAAM,GAAG,IAAI,CAAC,MAAM;YAC5B,SAAS,IAAI,YAAY,GAAG,CAAC;YAC7B,IAAI,CAAC,iBAAiB,CAAC;YACvB,OAAO,QAAQ,CAAC;QACpB;QACA,IAAI,yBAAyB;YACzB,OAAO,cAAc,GAAG;QAC5B;QACA,OAAO;IACX;IACA;;;;;;KAMC,GACD,qBAAqB,IAAI,EAAE,OAAO,EAAE;QAChC,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;QACnC,oEAAoE;QACpE,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO;IACX;IACA,WAAW,WAAW,EAAE,oBAAoB,CAAC,CAAC,EAAE,QAAQ,EAAE;QACtD,IAAI,UAAU,CAAC;QACf,IAAI,OAAO,sBAAsB,YAAY;YACzC,WAAW;QACf,OACK;YACD,UAAU;QACd;QACA,IAAI,UAAU;YACV,IAAI,CAAC,eAAe,CAAC,aAAa,SAAS,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI;QAC5E,OACK;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa;QAC7C;IACJ;IACA,gBAAgB,WAAW,EAAE,OAAO,EAAE;QAClC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,aAAa;gBACd,MAAM,IAAI,MAAM;YACpB;YACA,MAAM,SAAS,EAAE;YACjB,YACK,WAAW,CAAC,QACZ,EAAE,CAAC,SAAS,QACZ,EAAE,CAAC,QAAQ,CAAA,QAAS,OAAO,IAAI,CAAC,QAChC,EAAE,CAAC,OAAO;gBACX,IAAI;oBACA,IAAI;wBACA,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC;wBACpC,MAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM;wBAC1C,OAAO,QAAQ;oBACnB,EACA,OAAO,KAAK;wBACR,wDAAwD;wBACxD,+BAA+B;wBAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,EACjB,MAAM;wBACV,MAAM,SAAS,IAAI,YAAY,GAAG,CAAC;4BAC/B,GAAG,IAAI,CAAC,aAAa;4BACrB,SAAS,IAAI,CAAC,WAAW;wBAC7B;wBACA,IAAI,CAAC,gBAAgB,GAAG;wBACxB,IAAI,CAAC,iBAAiB,CAAC;wBACvB,OAAO,QAAQ;oBACnB;gBACJ,EACA,OAAO,KAAK;oBACR,OAAO,OAAO;gBAClB;YACJ;QACJ;IACJ;IACA;;;;;;;KAOC,GACD,WAAW,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QAC7B,OAAO,IAAI,YAAY,GAAG,CAAC;YAAE,GAAG,OAAO;YAAE;QAAO;IACpD;IACA;;;KAGC,GACD,aAAa;QACT,MAAM,MAAM,GAAG,QAAQ;QACvB,IAAI,OAAO,IAAI,MAAM,IAAI,GAAG;YACxB,IAAI,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,OAAO,OAAO;gBAC7C,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA;;KAEC,GACD,MAAM,6BAA6B;QAC/B,OAAO,IAAI,QAAQ,CAAA;YACf,CAAC,GAAG,gBAAgB,IAAI,EAAE,6CAA6C,CAAC,KAAK;gBACzE,IAAI,CAAC,OAAO,QAAQ;oBAChB,IAAI;wBACA,MAAM,YAAY,KAAK,KAAK,CAAC,QAAQ,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO;wBAC1E,QAAQ;wBACR;oBACJ,EACA,OAAO,GAAG;oBACN,gBAAgB;oBACpB;gBACJ;gBACA,QAAQ;YACZ;QACJ;IACJ;IACA;;;KAGC,GACD,yBAAyB;QACrB,OAAQ,QAAQ,GAAG,CAAC,iBAAiB,IACjC,QAAQ,GAAG,CAAC,uBAAuB,IACnC,QAAQ,GAAG,CAAC,iBAAiB,IAC7B,QAAQ,GAAG,CAAC,uBAAuB;IAC3C;IACA;;;KAGC,GACD,MAAM,mBAAmB;QACrB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,8DAA8D;YAC9D,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAC1C;QACA,gEAAgE;QAChE,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS;YAClC,IAAI,SAAS,MAAM,SAAS,EAAE;gBAC1B,OAAO,MAAM,SAAS;YAC1B;QACJ;QACA,yDAAyD;QACzD,MAAM,IAAI,MAAM,IAAI,CAAC,oDAAoD;QACzE,IAAI,GAAG;YACH,OAAO,EAAE,SAAS;QACtB,OACK;YACD,OAAO;QACX;IACJ;IACA;;KAEC,GACD,MAAM,oCAAoC;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,qBAAqB,qBAAqB,EAAE;YAC3F,OAAO;QACX;QACA,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS;QAClC,2EAA2E;QAC3E,oEAAoE;QACpE,0EAA0E;QAC1E,8BAA8B;QAC9B,gDAAgD;QAChD,oEAAoE;QACpE,0EAA0E;QAC1E,eAAe;QACf,0EAA0E;QAC1E,wEAAwE;QACxE,yBAAyB;QACzB,OAAO,MAAM,MAAM,YAAY;IACnC;IACA;;KAEC,GACD,MAAM,kBAAkB;QACpB,IAAI;YACA,MAAM,IAAI,MAAM,YAAY,OAAO,CAAC;YACpC,OAAO;QACX,EACA,OAAO,GAAG;YACN,oBAAoB;YACpB,OAAO;QACX;IACJ;IACA,eAAe,QAAQ,EAAE;QACrB,IAAI,UAAU;YACV,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI;QAC5D,OACK;YACD,OAAO,IAAI,CAAC,mBAAmB;QACnC;IACJ;IACA,MAAM,sBAAsB;QACxB,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,IAAI,kBAAkB,eAAe,YAAY,EAAE;YAC/C,OAAO;gBAAE,cAAc,OAAO,kBAAkB;YAAG;QACvD;QACA,IAAI,kBAAkB,qBAAqB,yBAAyB,EAAE;YAClE,MAAM,sBAAsB,OAAO,sBAAsB;YACzD,IAAI,qBAAqB;gBACrB,OAAO;oBACH,cAAc;oBACd,iBAAiB,OAAO,cAAc;gBAC1C;YACJ;QACJ;QACA,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO;gBACH,cAAc,IAAI,CAAC,WAAW,CAAC,YAAY;gBAC3C,aAAa,IAAI,CAAC,WAAW,CAAC,WAAW;gBACzC,iBAAiB,IAAI,CAAC,WAAW,CAAC,eAAe;YACrD;QACJ;QACA,IAAI,MAAM,IAAI,CAAC,WAAW,IAAI;YAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtD,YAAY,QAAQ,CAAC;gBACrB,IAAI,CAAC,iBAAiB;aACzB;YACD,OAAO;gBAAE;gBAAc;YAAgB;QAC3C;QACA,MAAM,IAAI,MAAM,QAAQ,2BAA2B,CAAC,oBAAoB;IAC5E;IACA;;;;KAIC,GACD,MAAM,YAAY;QACd,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA,0DAA0D;QAC1D,IAAI,CAAC,CAAA,iBAAkB,GACnB,IAAI,CAAC,CAAA,iBAAkB,IAAI,IAAI,CAAC,CAAA,eAAgB;QACpD,IAAI;YACA,OAAO,MAAM,IAAI,CAAC,CAAA,iBAAkB;QACxC,SACQ;YACJ,4DAA4D;YAC5D,IAAI,CAAC,CAAA,iBAAkB,GAAG;QAC9B;IACJ;IACA,MAAM,CAAA,eAAgB;QAClB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa;QACzE,OACK,IAAI,IAAI,CAAC,WAAW,EAAE;YACvB,MAAM,WAAW,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW;YAC9C,MAAM,SAAS,GAAG,gBAAgB,CAAC;YACnC,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,IAAI,CAAC,aAAa;QAChE,OACK,IAAI,IAAI,CAAC,MAAM,EAAE;YAClB,MAAM,SAAS,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa;YACpE,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM;YAC3B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC;YACzD,OAAO;QACX,OACK;YACD,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,aAAa;YAC/E,OAAO;QACX;IACJ;IACA;;;;KAIC,GACD,MAAM,iBAAiB,cAAc,EAAE;QACnC,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,IAAI,CAAC,CAAC,kBAAkB,MAAM,GAAG;YAC7B,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,gBAAgB,aAAa,CAAC;YAAE;YAAgB,iBAAiB;QAAO;IACvF;IACA;;;KAGC,GACD,MAAM,iBAAiB;QACnB,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,OAAO,CAAC,MAAM,OAAO,cAAc,EAAE,EAAE,KAAK;IAChD;IACA;;;KAGC,GACD,MAAM,kBAAkB,GAAG,EAAE;QACzB,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,OAAO,OAAO,iBAAiB,CAAC;IACpC;IACA;;;;KAIC,GACD,MAAM,iBAAiB,OAAO,CAAC,CAAC,EAAE;QAC9B,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,MAAM,UAAU,MAAM,OAAO,iBAAiB,CAAC;QAC/C,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO,EAAE;QAC1D,OAAO;IACX;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,MAAM,MAAM,GAAG,IAAI,EAAE;QACjB,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,OAAO,OAAO,KAAK,IAAI;IAC3B;IACA;;;;;;;KAOC,GACD,MAAM,QAAQ,IAAI,EAAE;QAChB,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,OAAO,OAAO,OAAO,CAAC;IAC1B;IACA;;KAEC,GACD,SAAS;QACL,OAAO,CAAC,GAAG,YAAY,MAAM;IACjC;IACA;;;;;;;;;;KAUC,GACD,MAAM,KAAK,IAAI,EAAE,QAAQ,EAAE;QACvB,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB;QAC7C,WACI,YACI,CAAC,uBAAuB,EAAE,SAAS,+BAA+B,CAAC;QAC3E,IAAI,kBAAkB,eAAe,YAAY,EAAE;YAC/C,MAAM,SAAS,MAAM,OAAO,IAAI,CAAC;YACjC,OAAO,OAAO,UAAU;QAC5B;QACA,MAAM,SAAS,CAAC,GAAG,SAAS,YAAY;QACxC,IAAI,kBAAkB,YAAY,GAAG,IAAI,OAAO,GAAG,EAAE;YACjD,MAAM,OAAO,MAAM,OAAO,IAAI,CAAC,OAAO,GAAG,EAAE;YAC3C,OAAO;QACX;QACA,MAAM,QAAQ,MAAM,IAAI,CAAC,cAAc;QACvC,IAAI,CAAC,MAAM,YAAY,EAAE;YACrB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,YAAY,EAAE,MAAM;IAC3D;IACA,MAAM,SAAS,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE;QACpD,MAAM,MAAM,IAAI,IAAI,WAAW,GAAG,gBAAgB,SAAS,CAAC;QAC5D,MAAM,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;YAC3B,QAAQ;YACR,KAAK,IAAI,IAAI;YACb,MAAM;gBACF,SAAS,OAAO,sBAAsB,CAAC;YAC3C;YACA,OAAO;YACP,aAAa;gBACT,oBAAoB;oBAAC;iBAAO;YAChC;QACJ;QACA,OAAO,IAAI,IAAI,CAAC,UAAU;IAC9B;AACJ;AACA,QAAQ,UAAU,GAAG,YACrB,sCAAsC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/iam.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2014 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.IAMAuth = void 0;\nclass IAMAuth {\n    selector;\n    token;\n    /**\n     * IAM credentials.\n     *\n     * @param selector the iam authority selector\n     * @param token the token\n     * @constructor\n     */\n    constructor(selector, token) {\n        this.selector = selector;\n        this.token = token;\n        this.selector = selector;\n        this.token = token;\n    }\n    /**\n     * Acquire the HTTP headers required to make an authenticated request.\n     */\n    getRequestHeaders() {\n        return {\n            'x-goog-iam-authority-selector': this.selector,\n            'x-goog-iam-authorization-token': this.token,\n        };\n    }\n}\nexports.IAMAuth = IAMAuth;\n//# sourceMappingURL=iam.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,KAAK;AACvB,MAAM;IACF,SAAS;IACT,MAAM;IACN;;;;;;KAMC,GACD,YAAY,QAAQ,EAAE,KAAK,CAAE;QACzB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;KAEC,GACD,oBAAoB;QAChB,OAAO;YACH,iCAAiC,IAAI,CAAC,QAAQ;YAC9C,kCAAkC,IAAI,CAAC,KAAK;QAChD;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG,SAClB,+BAA+B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6191, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/downscopedclient.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2021 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DownscopedClient = exports.EXPIRATION_TIME_OFFSET = exports.MAX_ACCESS_BOUNDARY_RULES_COUNT = void 0;\nconst gaxios_1 = require(\"gaxios\");\nconst stream = require(\"stream\");\nconst authclient_1 = require(\"./authclient\");\nconst sts = require(\"./stscredentials\");\n/**\n * The required token exchange grant_type: rfc8693#section-2.1\n */\nconst STS_GRANT_TYPE = 'urn:ietf:params:oauth:grant-type:token-exchange';\n/**\n * The requested token exchange requested_token_type: rfc8693#section-2.1\n */\nconst STS_REQUEST_TOKEN_TYPE = 'urn:ietf:params:oauth:token-type:access_token';\n/**\n * The requested token exchange subject_token_type: rfc8693#section-2.1\n */\nconst STS_SUBJECT_TOKEN_TYPE = 'urn:ietf:params:oauth:token-type:access_token';\n/**\n * The maximum number of access boundary rules a Credential Access Boundary\n * can contain.\n */\nexports.MAX_ACCESS_BOUNDARY_RULES_COUNT = 10;\n/**\n * Offset to take into account network delays and server clock skews.\n */\nexports.EXPIRATION_TIME_OFFSET = 5 * 60 * 1000;\n/**\n * Defines a set of Google credentials that are downscoped from an existing set\n * of Google OAuth2 credentials. This is useful to restrict the Identity and\n * Access Management (IAM) permissions that a short-lived credential can use.\n * The common pattern of usage is to have a token broker with elevated access\n * generate these downscoped credentials from higher access source credentials\n * and pass the downscoped short-lived access tokens to a token consumer via\n * some secure authenticated channel for limited access to Google Cloud Storage\n * resources.\n */\nclass DownscopedClient extends authclient_1.AuthClient {\n    authClient;\n    credentialAccessBoundary;\n    cachedDownscopedAccessToken;\n    stsCredential;\n    /**\n     * Instantiates a downscoped client object using the provided source\n     * AuthClient and credential access boundary rules.\n     * To downscope permissions of a source AuthClient, a Credential Access\n     * Boundary that specifies which resources the new credential can access, as\n     * well as an upper bound on the permissions that are available on each\n     * resource, has to be defined. A downscoped client can then be instantiated\n     * using the source AuthClient and the Credential Access Boundary.\n     * @param options the {@link DownscopedClientOptions `DownscopedClientOptions`} to use. Passing an `AuthClient` directly is **@DEPRECATED**.\n     * @param credentialAccessBoundary **@DEPRECATED**. Provide a {@link DownscopedClientOptions `DownscopedClientOptions`} object in the first parameter instead.\n     */\n    constructor(\n    /**\n     * AuthClient is for backwards-compatibility.\n     */\n    options, \n    /**\n     * @deprecated - provide a {@link DownscopedClientOptions `DownscopedClientOptions`} object in the first parameter instead\n     */\n    credentialAccessBoundary = {\n        accessBoundary: {\n            accessBoundaryRules: [],\n        },\n    }) {\n        super(options instanceof authclient_1.AuthClient ? {} : options);\n        if (options instanceof authclient_1.AuthClient) {\n            this.authClient = options;\n            this.credentialAccessBoundary = credentialAccessBoundary;\n        }\n        else {\n            this.authClient = options.authClient;\n            this.credentialAccessBoundary = options.credentialAccessBoundary;\n        }\n        // Check 1-10 Access Boundary Rules are defined within Credential Access\n        // Boundary.\n        if (this.credentialAccessBoundary.accessBoundary.accessBoundaryRules\n            .length === 0) {\n            throw new Error('At least one access boundary rule needs to be defined.');\n        }\n        else if (this.credentialAccessBoundary.accessBoundary.accessBoundaryRules.length >\n            exports.MAX_ACCESS_BOUNDARY_RULES_COUNT) {\n            throw new Error('The provided access boundary has more than ' +\n                `${exports.MAX_ACCESS_BOUNDARY_RULES_COUNT} access boundary rules.`);\n        }\n        // Check at least one permission should be defined in each Access Boundary\n        // Rule.\n        for (const rule of this.credentialAccessBoundary.accessBoundary\n            .accessBoundaryRules) {\n            if (rule.availablePermissions.length === 0) {\n                throw new Error('At least one permission should be defined in access boundary rules.');\n            }\n        }\n        this.stsCredential = new sts.StsCredentials({\n            tokenExchangeEndpoint: `https://sts.${this.universeDomain}/v1/token`,\n        });\n        this.cachedDownscopedAccessToken = null;\n    }\n    /**\n     * Provides a mechanism to inject Downscoped access tokens directly.\n     * The expiry_date field is required to facilitate determination of the token\n     * expiration which would make it easier for the token consumer to handle.\n     * @param credentials The Credentials object to set on the current client.\n     */\n    setCredentials(credentials) {\n        if (!credentials.expiry_date) {\n            throw new Error('The access token expiry_date field is missing in the provided ' +\n                'credentials.');\n        }\n        super.setCredentials(credentials);\n        this.cachedDownscopedAccessToken = credentials;\n    }\n    async getAccessToken() {\n        // If the cached access token is unavailable or expired, force refresh.\n        // The Downscoped access token will be returned in\n        // DownscopedAccessTokenResponse format.\n        if (!this.cachedDownscopedAccessToken ||\n            this.isExpired(this.cachedDownscopedAccessToken)) {\n            await this.refreshAccessTokenAsync();\n        }\n        // Return Downscoped access token in DownscopedAccessTokenResponse format.\n        return {\n            token: this.cachedDownscopedAccessToken.access_token,\n            expirationTime: this.cachedDownscopedAccessToken.expiry_date,\n            res: this.cachedDownscopedAccessToken.res,\n        };\n    }\n    /**\n     * The main authentication interface. It takes an optional url which when\n     * present is the endpoint being accessed, and returns a Promise which\n     * resolves with authorization header fields.\n     *\n     * The result has the form:\n     * { authorization: 'Bearer <access_token_value>' }\n     */\n    async getRequestHeaders() {\n        const accessTokenResponse = await this.getAccessToken();\n        const headers = new Headers({\n            authorization: `Bearer ${accessTokenResponse.token}`,\n        });\n        return this.addSharedMetadataHeaders(headers);\n    }\n    request(opts, callback) {\n        if (callback) {\n            this.requestAsync(opts).then(r => callback(null, r), e => {\n                return callback(e, e.response);\n            });\n        }\n        else {\n            return this.requestAsync(opts);\n        }\n    }\n    /**\n     * Authenticates the provided HTTP request, processes it and resolves with the\n     * returned response.\n     * @param opts The HTTP request options.\n     * @param reAuthRetried Whether the current attempt is a retry after a failed attempt due to an auth failure\n     * @return A promise that resolves with the successful response.\n     */\n    async requestAsync(opts, reAuthRetried = false) {\n        let response;\n        try {\n            const requestHeaders = await this.getRequestHeaders();\n            opts.headers = gaxios_1.Gaxios.mergeHeaders(opts.headers);\n            this.addUserProjectAndAuthHeaders(opts.headers, requestHeaders);\n            response = await this.transporter.request(opts);\n        }\n        catch (e) {\n            const res = e.response;\n            if (res) {\n                const statusCode = res.status;\n                // Retry the request for metadata if the following criteria are true:\n                // - We haven't already retried.  It only makes sense to retry once.\n                // - The response was a 401 or a 403\n                // - The request didn't send a readableStream\n                // - forceRefreshOnFailure is true\n                const isReadableStream = res.config.data instanceof stream.Readable;\n                const isAuthErr = statusCode === 401 || statusCode === 403;\n                if (!reAuthRetried &&\n                    isAuthErr &&\n                    !isReadableStream &&\n                    this.forceRefreshOnFailure) {\n                    await this.refreshAccessTokenAsync();\n                    return await this.requestAsync(opts, true);\n                }\n            }\n            throw e;\n        }\n        return response;\n    }\n    /**\n     * Forces token refresh, even if unexpired tokens are currently cached.\n     * GCP access tokens are retrieved from authclient object/source credential.\n     * Then GCP access tokens are exchanged for downscoped access tokens via the\n     * token exchange endpoint.\n     * @return A promise that resolves with the fresh downscoped access token.\n     */\n    async refreshAccessTokenAsync() {\n        // Retrieve GCP access token from source credential.\n        const subjectToken = (await this.authClient.getAccessToken()).token;\n        // Construct the STS credentials options.\n        const stsCredentialsOptions = {\n            grantType: STS_GRANT_TYPE,\n            requestedTokenType: STS_REQUEST_TOKEN_TYPE,\n            subjectToken: subjectToken,\n            subjectTokenType: STS_SUBJECT_TOKEN_TYPE,\n        };\n        // Exchange the source AuthClient access token for a Downscoped access\n        // token.\n        const stsResponse = await this.stsCredential.exchangeToken(stsCredentialsOptions, undefined, this.credentialAccessBoundary);\n        /**\n         * The STS endpoint will only return the expiration time for the downscoped\n         * access token if the original access token represents a service account.\n         * The downscoped token's expiration time will always match the source\n         * credential expiration. When no expires_in is returned, we can copy the\n         * source credential's expiration time.\n         */\n        const sourceCredExpireDate = this.authClient.credentials?.expiry_date || null;\n        const expiryDate = stsResponse.expires_in\n            ? new Date().getTime() + stsResponse.expires_in * 1000\n            : sourceCredExpireDate;\n        // Save response in cached access token.\n        this.cachedDownscopedAccessToken = {\n            access_token: stsResponse.access_token,\n            expiry_date: expiryDate,\n            res: stsResponse.res,\n        };\n        // Save credentials.\n        this.credentials = {};\n        Object.assign(this.credentials, this.cachedDownscopedAccessToken);\n        delete this.credentials.res;\n        // Trigger tokens event to notify external listeners.\n        this.emit('tokens', {\n            refresh_token: null,\n            expiry_date: this.cachedDownscopedAccessToken.expiry_date,\n            access_token: this.cachedDownscopedAccessToken.access_token,\n            token_type: 'Bearer',\n            id_token: null,\n        });\n        // Return the cached access token.\n        return this.cachedDownscopedAccessToken;\n    }\n    /**\n     * Returns whether the provided credentials are expired or not.\n     * If there is no expiry time, assumes the token is not expired or expiring.\n     * @param downscopedAccessToken The credentials to check for expiration.\n     * @return Whether the credentials are expired or not.\n     */\n    isExpired(downscopedAccessToken) {\n        const now = new Date().getTime();\n        return downscopedAccessToken.expiry_date\n            ? now >=\n                downscopedAccessToken.expiry_date - this.eagerRefreshThresholdMillis\n            : false;\n    }\n}\nexports.DownscopedClient = DownscopedClient;\n//# sourceMappingURL=downscopedclient.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,+BAA+B,GAAG,KAAK;AAC3G,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;CAEC,GACD,MAAM,iBAAiB;AACvB;;CAEC,GACD,MAAM,yBAAyB;AAC/B;;CAEC,GACD,MAAM,yBAAyB;AAC/B;;;CAGC,GACD,QAAQ,+BAA+B,GAAG;AAC1C;;CAEC,GACD,QAAQ,sBAAsB,GAAG,IAAI,KAAK;AAC1C;;;;;;;;;CASC,GACD,MAAM,yBAAyB,aAAa,UAAU;IAClD,WAAW;IACX,yBAAyB;IACzB,4BAA4B;IAC5B,cAAc;IACd;;;;;;;;;;KAUC,GACD,YACA;;KAEC,GACD,OAAO,EACP;;KAEC,GACD,2BAA2B;QACvB,gBAAgB;YACZ,qBAAqB,EAAE;QAC3B;IACJ,CAAC,CAAE;QACC,KAAK,CAAC,mBAAmB,aAAa,UAAU,GAAG,CAAC,IAAI;QACxD,IAAI,mBAAmB,aAAa,UAAU,EAAE;YAC5C,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,CAAC,wBAAwB,GAAG;QACpC,OACK;YACD,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;YACpC,IAAI,CAAC,wBAAwB,GAAG,QAAQ,wBAAwB;QACpE;QACA,wEAAwE;QACxE,YAAY;QACZ,IAAI,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,mBAAmB,CAC/D,MAAM,KAAK,GAAG;YACf,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,mBAAmB,CAAC,MAAM,GAC5E,QAAQ,+BAA+B,EAAE;YACzC,MAAM,IAAI,MAAM,gDACZ,GAAG,QAAQ,+BAA+B,CAAC,uBAAuB,CAAC;QAC3E;QACA,0EAA0E;QAC1E,QAAQ;QACR,KAAK,MAAM,QAAQ,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAC1D,mBAAmB,CAAE;YACtB,IAAI,KAAK,oBAAoB,CAAC,MAAM,KAAK,GAAG;gBACxC,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,cAAc,CAAC;YACxC,uBAAuB,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;QACxE;QACA,IAAI,CAAC,2BAA2B,GAAG;IACvC;IACA;;;;;KAKC,GACD,eAAe,WAAW,EAAE;QACxB,IAAI,CAAC,YAAY,WAAW,EAAE;YAC1B,MAAM,IAAI,MAAM,mEACZ;QACR;QACA,KAAK,CAAC,eAAe;QACrB,IAAI,CAAC,2BAA2B,GAAG;IACvC;IACA,MAAM,iBAAiB;QACnB,uEAAuE;QACvE,kDAAkD;QAClD,wCAAwC;QACxC,IAAI,CAAC,IAAI,CAAC,2BAA2B,IACjC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,2BAA2B,GAAG;YAClD,MAAM,IAAI,CAAC,uBAAuB;QACtC;QACA,0EAA0E;QAC1E,OAAO;YACH,OAAO,IAAI,CAAC,2BAA2B,CAAC,YAAY;YACpD,gBAAgB,IAAI,CAAC,2BAA2B,CAAC,WAAW;YAC5D,KAAK,IAAI,CAAC,2BAA2B,CAAC,GAAG;QAC7C;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,oBAAoB;QACtB,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc;QACrD,MAAM,UAAU,IAAI,QAAQ;YACxB,eAAe,CAAC,OAAO,EAAE,oBAAoB,KAAK,EAAE;QACxD;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,QAAQ,IAAI,EAAE,QAAQ,EAAE;QACpB,IAAI,UAAU;YACV,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,CAAA,IAAK,SAAS,MAAM,IAAI,CAAA;gBACjD,OAAO,SAAS,GAAG,EAAE,QAAQ;YACjC;QACJ,OACK;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B;IACJ;IACA;;;;;;KAMC,GACD,MAAM,aAAa,IAAI,EAAE,gBAAgB,KAAK,EAAE;QAC5C,IAAI;QACJ,IAAI;YACA,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB;YACnD,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO;YACxD,IAAI,CAAC,4BAA4B,CAAC,KAAK,OAAO,EAAE;YAChD,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC9C,EACA,OAAO,GAAG;YACN,MAAM,MAAM,EAAE,QAAQ;YACtB,IAAI,KAAK;gBACL,MAAM,aAAa,IAAI,MAAM;gBAC7B,qEAAqE;gBACrE,oEAAoE;gBACpE,oCAAoC;gBACpC,6CAA6C;gBAC7C,kCAAkC;gBAClC,MAAM,mBAAmB,IAAI,MAAM,CAAC,IAAI,YAAY,OAAO,QAAQ;gBACnE,MAAM,YAAY,eAAe,OAAO,eAAe;gBACvD,IAAI,CAAC,iBACD,aACA,CAAC,oBACD,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,MAAM,IAAI,CAAC,uBAAuB;oBAClC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM;gBACzC;YACJ;YACA,MAAM;QACV;QACA,OAAO;IACX;IACA;;;;;;KAMC,GACD,MAAM,0BAA0B;QAC5B,oDAAoD;QACpD,MAAM,eAAe,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,KAAK;QACnE,yCAAyC;QACzC,MAAM,wBAAwB;YAC1B,WAAW;YACX,oBAAoB;YACpB,cAAc;YACd,kBAAkB;QACtB;QACA,sEAAsE;QACtE,SAAS;QACT,MAAM,cAAc,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,uBAAuB,WAAW,IAAI,CAAC,wBAAwB;QAC1H;;;;;;SAMC,GACD,MAAM,uBAAuB,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe;QACzE,MAAM,aAAa,YAAY,UAAU,GACnC,IAAI,OAAO,OAAO,KAAK,YAAY,UAAU,GAAG,OAChD;QACN,wCAAwC;QACxC,IAAI,CAAC,2BAA2B,GAAG;YAC/B,cAAc,YAAY,YAAY;YACtC,aAAa;YACb,KAAK,YAAY,GAAG;QACxB;QACA,oBAAoB;QACpB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,2BAA2B;QAChE,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;QAC3B,qDAAqD;QACrD,IAAI,CAAC,IAAI,CAAC,UAAU;YAChB,eAAe;YACf,aAAa,IAAI,CAAC,2BAA2B,CAAC,WAAW;YACzD,cAAc,IAAI,CAAC,2BAA2B,CAAC,YAAY;YAC3D,YAAY;YACZ,UAAU;QACd;QACA,kCAAkC;QAClC,OAAO,IAAI,CAAC,2BAA2B;IAC3C;IACA;;;;;KAKC,GACD,UAAU,qBAAqB,EAAE;QAC7B,MAAM,MAAM,IAAI,OAAO,OAAO;QAC9B,OAAO,sBAAsB,WAAW,GAClC,OACE,sBAAsB,WAAW,GAAG,IAAI,CAAC,2BAA2B,GACtE;IACV;AACJ;AACA,QAAQ,gBAAgB,GAAG,kBAC3B,4CAA4C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6435, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/auth/passthrough.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PassThroughClient = void 0;\nconst authclient_1 = require(\"./authclient\");\n/**\n * An AuthClient without any Authentication information. Useful for:\n * - Anonymous access\n * - Local Emulators\n * - Testing Environments\n *\n */\nclass PassThroughClient extends authclient_1.AuthClient {\n    /**\n     * Creates a request without any authentication headers or checks.\n     *\n     * @remarks\n     *\n     * In testing environments it may be useful to change the provided\n     * {@link AuthClient.transporter} for any desired request overrides/handling.\n     *\n     * @param opts\n     * @returns The response of the request.\n     */\n    async request(opts) {\n        return this.transporter.request(opts);\n    }\n    /**\n     * A required method of the base class.\n     * Always will return an empty object.\n     *\n     * @returns {}\n     */\n    async getAccessToken() {\n        return {};\n    }\n    /**\n     * A required method of the base class.\n     * Always will return an empty object.\n     *\n     * @returns {}\n     */\n    async getRequestHeaders() {\n        return new Headers();\n    }\n}\nexports.PassThroughClient = PassThroughClient;\n//# sourceMappingURL=passthrough.js.map"], "names": [], "mappings": "AACA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,KAAK;AACjC,MAAM;AACN;;;;;;CAMC,GACD,MAAM,0BAA0B,aAAa,UAAU;IACnD;;;;;;;;;;KAUC,GACD,MAAM,QAAQ,IAAI,EAAE;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACpC;IACA;;;;;KAKC,GACD,MAAM,iBAAiB;QACnB,OAAO,CAAC;IACZ;IACA;;;;;KAKC,GACD,MAAM,oBAAoB;QACtB,OAAO,IAAI;IACf;AACJ;AACA,QAAQ,iBAAiB,GAAG,mBAC5B,uCAAuC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6497, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/google-auth-library/build/src/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GoogleAuth = exports.auth = exports.PassThroughClient = exports.ExecutableError = exports.PluggableAuthClient = exports.DownscopedClient = exports.BaseExternalAccountClient = exports.ExternalAccountClient = exports.IdentityPoolClient = exports.AwsRequestSigner = exports.AwsClient = exports.UserRefreshClient = exports.LoginTicket = exports.ClientAuthentication = exports.OAuth2Client = exports.CodeChallengeMethod = exports.Impersonated = exports.JWT = exports.JWTAccess = exports.IdTokenClient = exports.IAMAuth = exports.GCPEnv = exports.Compute = exports.DEFAULT_UNIVERSE = exports.AuthClient = exports.gaxios = exports.gcpMetadata = void 0;\n// Copyright 2017 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nconst googleauth_1 = require(\"./auth/googleauth\");\nObject.defineProperty(exports, \"GoogleAuth\", { enumerable: true, get: function () { return googleauth_1.GoogleAuth; } });\n// Export common deps to ensure types/instances are the exact match. Useful\n// for consistently configuring the library across versions.\nexports.gcpMetadata = require(\"gcp-metadata\");\nexports.gaxios = require(\"gaxios\");\nvar authclient_1 = require(\"./auth/authclient\");\nObject.defineProperty(exports, \"AuthClient\", { enumerable: true, get: function () { return authclient_1.AuthClient; } });\nObject.defineProperty(exports, \"DEFAULT_UNIVERSE\", { enumerable: true, get: function () { return authclient_1.DEFAULT_UNIVERSE; } });\nvar computeclient_1 = require(\"./auth/computeclient\");\nObject.defineProperty(exports, \"Compute\", { enumerable: true, get: function () { return computeclient_1.Compute; } });\nvar envDetect_1 = require(\"./auth/envDetect\");\nObject.defineProperty(exports, \"GCPEnv\", { enumerable: true, get: function () { return envDetect_1.GCPEnv; } });\nvar iam_1 = require(\"./auth/iam\");\nObject.defineProperty(exports, \"IAMAuth\", { enumerable: true, get: function () { return iam_1.IAMAuth; } });\nvar idtokenclient_1 = require(\"./auth/idtokenclient\");\nObject.defineProperty(exports, \"IdTokenClient\", { enumerable: true, get: function () { return idtokenclient_1.IdTokenClient; } });\nvar jwtaccess_1 = require(\"./auth/jwtaccess\");\nObject.defineProperty(exports, \"JWTAccess\", { enumerable: true, get: function () { return jwtaccess_1.JWTAccess; } });\nvar jwtclient_1 = require(\"./auth/jwtclient\");\nObject.defineProperty(exports, \"JWT\", { enumerable: true, get: function () { return jwtclient_1.JWT; } });\nvar impersonated_1 = require(\"./auth/impersonated\");\nObject.defineProperty(exports, \"Impersonated\", { enumerable: true, get: function () { return impersonated_1.Impersonated; } });\nvar oauth2client_1 = require(\"./auth/oauth2client\");\nObject.defineProperty(exports, \"CodeChallengeMethod\", { enumerable: true, get: function () { return oauth2client_1.CodeChallengeMethod; } });\nObject.defineProperty(exports, \"OAuth2Client\", { enumerable: true, get: function () { return oauth2client_1.OAuth2Client; } });\nObject.defineProperty(exports, \"ClientAuthentication\", { enumerable: true, get: function () { return oauth2client_1.ClientAuthentication; } });\nvar loginticket_1 = require(\"./auth/loginticket\");\nObject.defineProperty(exports, \"LoginTicket\", { enumerable: true, get: function () { return loginticket_1.LoginTicket; } });\nvar refreshclient_1 = require(\"./auth/refreshclient\");\nObject.defineProperty(exports, \"UserRefreshClient\", { enumerable: true, get: function () { return refreshclient_1.UserRefreshClient; } });\nvar awsclient_1 = require(\"./auth/awsclient\");\nObject.defineProperty(exports, \"AwsClient\", { enumerable: true, get: function () { return awsclient_1.AwsClient; } });\nvar awsrequestsigner_1 = require(\"./auth/awsrequestsigner\");\nObject.defineProperty(exports, \"AwsRequestSigner\", { enumerable: true, get: function () { return awsrequestsigner_1.AwsRequestSigner; } });\nvar identitypoolclient_1 = require(\"./auth/identitypoolclient\");\nObject.defineProperty(exports, \"IdentityPoolClient\", { enumerable: true, get: function () { return identitypoolclient_1.IdentityPoolClient; } });\nvar externalclient_1 = require(\"./auth/externalclient\");\nObject.defineProperty(exports, \"ExternalAccountClient\", { enumerable: true, get: function () { return externalclient_1.ExternalAccountClient; } });\nvar baseexternalclient_1 = require(\"./auth/baseexternalclient\");\nObject.defineProperty(exports, \"BaseExternalAccountClient\", { enumerable: true, get: function () { return baseexternalclient_1.BaseExternalAccountClient; } });\nvar downscopedclient_1 = require(\"./auth/downscopedclient\");\nObject.defineProperty(exports, \"DownscopedClient\", { enumerable: true, get: function () { return downscopedclient_1.DownscopedClient; } });\nvar pluggable_auth_client_1 = require(\"./auth/pluggable-auth-client\");\nObject.defineProperty(exports, \"PluggableAuthClient\", { enumerable: true, get: function () { return pluggable_auth_client_1.PluggableAuthClient; } });\nObject.defineProperty(exports, \"ExecutableError\", { enumerable: true, get: function () { return pluggable_auth_client_1.ExecutableError; } });\nvar passthrough_1 = require(\"./auth/passthrough\");\nObject.defineProperty(exports, \"PassThroughClient\", { enumerable: true, get: function () { return passthrough_1.PassThroughClient; } });\nconst auth = new googleauth_1.GoogleAuth();\nexports.auth = auth;\n//# sourceMappingURL=index.js.map"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,QAAQ,IAAI,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,eAAe,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,yBAAyB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,SAAS,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,WAAW,GAAG,QAAQ,oBAAoB,GAAG,QAAQ,YAAY,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,YAAY,GAAG,QAAQ,GAAG,GAAG,QAAQ,SAAS,GAAG,QAAQ,aAAa,GAAG,QAAQ,OAAO,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,UAAU,GAAG,QAAQ,MAAM,GAAG,QAAQ,WAAW,GAAG,KAAK;AAC3oB,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,UAAU;IAAE;AAAE;AACtH,2EAA2E;AAC3E,4DAA4D;AAC5D,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,UAAU;IAAE;AAAE;AACtH,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,gBAAgB;IAAE;AAAE;AAClI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,gBAAgB,OAAO;IAAE;AAAE;AACnH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,UAAU;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,MAAM;IAAE;AAAE;AAC7G,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,MAAM,OAAO;IAAE;AAAE;AACzG,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,gBAAgB,aAAa;IAAE;AAAE;AAC/H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,aAAa;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,SAAS;IAAE;AAAE;AACnH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,OAAO;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,GAAG;IAAE;AAAE;AACvG,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,gBAAgB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,eAAe,YAAY;IAAE;AAAE;AAC5H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,uBAAuB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,eAAe,mBAAmB;IAAE;AAAE;AAC1I,OAAO,cAAc,CAAC,SAAS,gBAAgB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,eAAe,YAAY;IAAE;AAAE;AAC5H,OAAO,cAAc,CAAC,SAAS,wBAAwB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,eAAe,oBAAoB;IAAE;AAAE;AAC5I,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,cAAc,WAAW;IAAE;AAAE;AACzH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,qBAAqB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,gBAAgB,iBAAiB;IAAE;AAAE;AACvI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,aAAa;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,SAAS;IAAE;AAAE;AACnH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,mBAAmB,gBAAgB;IAAE;AAAE;AACxI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,sBAAsB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,qBAAqB,kBAAkB;IAAE;AAAE;AAC9I,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,yBAAyB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,iBAAiB,qBAAqB;IAAE;AAAE;AAChJ,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,6BAA6B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,qBAAqB,yBAAyB;IAAE;AAAE;AAC5J,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,mBAAmB,gBAAgB;IAAE;AAAE;AACxI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,uBAAuB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,wBAAwB,mBAAmB;IAAE;AAAE;AACnJ,OAAO,cAAc,CAAC,SAAS,mBAAmB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,wBAAwB,eAAe;IAAE;AAAE;AAC3I,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,qBAAqB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,cAAc,iBAAiB;IAAE;AAAE;AACrI,MAAM,OAAO,IAAI,aAAa,UAAU;AACxC,QAAQ,IAAI,GAAG,MACf,iCAAiC", "ignoreList": [0], "debugId": null}}]}