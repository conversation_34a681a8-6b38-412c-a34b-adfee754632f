{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\nimport { Connector } from '@google-cloud/cloud-sql-connector'\nimport mysql from 'mysql2/promise'\n\ndeclare global {\n  var __prisma: PrismaClient | undefined\n}\n\n// Cloud SQL Connector instance\nlet connector: Connector | null = null\n\n// Function to create database connection for Cloud SQL\nasync function createCloudSqlConnection() {\n  if (!connector) {\n    connector = new Connector()\n  }\n\n  const clientOpts = await connector.getOptions({\n    instanceConnectionName: process.env.INSTANCE_CONNECTION_NAME!,\n    ipType: 'PUBLIC',\n  })\n\n  const pool = mysql.createPool({\n    ...clientOpts,\n    user: process.env.DB_USER,\n    password: process.env.DB_PASS,\n    database: process.env.DB_NAME,\n  })\n\n  return pool\n}\n\n// Prisma Client with connection pooling\nfunction createPrismaClient() {\n  return new PrismaClient({\n    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],\n    datasources: {\n      db: {\n        url: process.env.DATABASE_URL,\n      },\n    },\n  })\n}\n\n// Global Prisma instance\nconst prisma = globalThis.__prisma || createPrismaClient()\n\nif (process.env.NODE_ENV === 'development') {\n  globalThis.__prisma = prisma\n}\n\n// Graceful shutdown\nprocess.on('beforeExit', async () => {\n  await prisma.$disconnect()\n  if (connector) {\n    connector.close()\n  }\n})\n\nexport { prisma, createCloudSqlConnection }\nexport default prisma\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;;;;AAMA,+BAA+B;AAC/B,IAAI,YAA8B;AAElC,uDAAuD;AACvD,eAAe;IACb,IAAI,CAAC,WAAW;QACd,YAAY,IAAI,4LAAA,CAAA,YAAS;IAC3B;IAEA,MAAM,aAAa,MAAM,UAAU,UAAU,CAAC;QAC5C,wBAAwB,QAAQ,GAAG,CAAC,wBAAwB;QAC5D,QAAQ;IACV;IAEA,MAAM,OAAO,mIAAA,CAAA,UAAK,CAAC,UAAU,CAAC;QAC5B,GAAG,UAAU;QACb,MAAM,QAAQ,GAAG,CAAC,OAAO;QACzB,UAAU,QAAQ,GAAG,CAAC,OAAO;QAC7B,UAAU,QAAQ,GAAG,CAAC,OAAO;IAC/B;IAEA,OAAO;AACT;AAEA,wCAAwC;AACxC,SAAS;IACP,OAAO,IAAI,6HAAA,CAAA,eAAY,CAAC;QACtB,KAAK,uCAAyC;YAAC;YAAS;YAAS;SAAO,GAAG;QAC3E,aAAa;YACX,IAAI;gBACF,KAAK,QAAQ,GAAG,CAAC,YAAY;YAC/B;QACF;IACF;AACF;AAEA,yBAAyB;AACzB,MAAM,SAAS,WAAW,QAAQ,IAAI;AAEtC,wCAA4C;IAC1C,WAAW,QAAQ,GAAG;AACxB;AAEA,oBAAoB;AACpB,QAAQ,EAAE,CAAC,cAAc;IACvB,MAAM,OAAO,WAAW;IACxB,IAAI,WAAW;QACb,UAAU,KAAK;IACjB;AACF;;uCAGe", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/src/app/api/health/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport prisma from '@/lib/db'\n\n// GET /api/health - Health check endpoint\nexport async function GET() {\n  try {\n    // Test database connection\n    await prisma.$queryRaw`SELECT 1`\n    \n    const userCount = await prisma.user.count()\n    const postCount = await prisma.post.count()\n\n    return NextResponse.json({\n      success: true,\n      status: 'healthy',\n      database: 'connected',\n      timestamp: new Date().toISOString(),\n      stats: {\n        users: userCount,\n        posts: postCount,\n      },\n    })\n  } catch (error) {\n    console.error('Health check failed:', error)\n    return NextResponse.json(\n      {\n        success: false,\n        status: 'unhealthy',\n        database: 'disconnected',\n        timestamp: new Date().toISOString(),\n        error: 'Database connection failed',\n      },\n      { status: 503 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe;IACpB,IAAI;QACF,2BAA2B;QAC3B,MAAM,kHAAA,CAAA,UAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEhC,MAAM,YAAY,MAAM,kHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,KAAK;QACzC,MAAM,YAAY,MAAM,kHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,KAAK;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;YACR,UAAU;YACV,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,QAAQ;YACR,UAAU;YACV,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}