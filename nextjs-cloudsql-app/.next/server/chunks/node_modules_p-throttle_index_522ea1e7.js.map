{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/p-throttle/index.js"], "sourcesContent": ["const registry = new FinalizationRegistry(({signal, aborted}) => {\n\tsignal?.removeEventListener('abort', aborted);\n});\n\nexport default function pThrottle({limit, interval, strict, signal, onDelay}) {\n\tif (!Number.isFinite(limit)) {\n\t\tthrow new TypeError('Expected `limit` to be a finite number');\n\t}\n\n\tif (!Number.isFinite(interval)) {\n\t\tthrow new TypeError('Expected `interval` to be a finite number');\n\t}\n\n\tconst queue = new Map();\n\n\tlet currentTick = 0;\n\tlet activeCount = 0;\n\n\tfunction windowedDelay() {\n\t\tconst now = Date.now();\n\n\t\tif ((now - currentTick) > interval) {\n\t\t\tactiveCount = 1;\n\t\t\tcurrentTick = now;\n\t\t\treturn 0;\n\t\t}\n\n\t\tif (activeCount < limit) {\n\t\t\tactiveCount++;\n\t\t} else {\n\t\t\tcurrentTick += interval;\n\t\t\tactiveCount = 1;\n\t\t}\n\n\t\treturn currentTick - now;\n\t}\n\n\tconst strictTicks = [];\n\n\tfunction strictDelay() {\n\t\tconst now = Date.now();\n\n\t\t// Clear the queue if there's a significant delay since the last execution\n\t\tif (strictTicks.length > 0 && now - strictTicks.at(-1) > interval) {\n\t\t\tstrictTicks.length = 0;\n\t\t}\n\n\t\t// If the queue is not full, add the current time and execute immediately\n\t\tif (strictTicks.length < limit) {\n\t\t\tstrictTicks.push(now);\n\t\t\treturn 0;\n\t\t}\n\n\t\t// Calculate the next execution time based on the first item in the queue\n\t\tconst nextExecutionTime = strictTicks[0] + interval;\n\n\t\t// Shift the queue and add the new execution time\n\t\tstrictTicks.shift();\n\t\tstrictTicks.push(nextExecutionTime);\n\n\t\t// Calculate the delay for the current execution\n\t\treturn Math.max(0, nextExecutionTime - now);\n\t}\n\n\tconst getDelay = strict ? strictDelay : windowedDelay;\n\n\treturn function_ => {\n\t\tconst throttled = function (...arguments_) {\n\t\t\tif (!throttled.isEnabled) {\n\t\t\t\treturn (async () => function_.apply(this, arguments_))();\n\t\t\t}\n\n\t\t\tlet timeoutId;\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tconst execute = () => {\n\t\t\t\t\tresolve(function_.apply(this, arguments_));\n\t\t\t\t\tqueue.delete(timeoutId);\n\t\t\t\t};\n\n\t\t\t\tconst delay = getDelay();\n\t\t\t\tif (delay > 0) {\n\t\t\t\t\ttimeoutId = setTimeout(execute, delay);\n\t\t\t\t\tqueue.set(timeoutId, reject);\n\t\t\t\t\tonDelay?.(...arguments_);\n\t\t\t\t} else {\n\t\t\t\t\texecute();\n\t\t\t\t}\n\t\t\t});\n\t\t};\n\n\t\tconst aborted = () => {\n\t\t\tfor (const timeout of queue.keys()) {\n\t\t\t\tclearTimeout(timeout);\n\t\t\t\tqueue.get(timeout)(signal.reason);\n\t\t\t}\n\n\t\t\tqueue.clear();\n\t\t\tstrictTicks.splice(0, strictTicks.length);\n\t\t};\n\n\t\tregistry.register(throttled, {signal, aborted});\n\n\t\tsignal?.throwIfAborted();\n\t\tsignal?.addEventListener('abort', aborted, {once: true});\n\n\t\tthrottled.isEnabled = true;\n\n\t\tObject.defineProperty(throttled, 'queueSize', {\n\t\t\tget() {\n\t\t\t\treturn queue.size;\n\t\t\t},\n\t\t});\n\n\t\treturn throttled;\n\t};\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW,IAAI,qBAAqB,CAAC,EAAC,MAAM,EAAE,OAAO,EAAC;IAC3D,QAAQ,oBAAoB,SAAS;AACtC;AAEe,SAAS,UAAU,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAC;IAC3E,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;QAC5B,MAAM,IAAI,UAAU;IACrB;IAEA,IAAI,CAAC,OAAO,QAAQ,CAAC,WAAW;QAC/B,MAAM,IAAI,UAAU;IACrB;IAEA,MAAM,QAAQ,IAAI;IAElB,IAAI,cAAc;IAClB,IAAI,cAAc;IAElB,SAAS;QACR,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,AAAC,MAAM,cAAe,UAAU;YACnC,cAAc;YACd,cAAc;YACd,OAAO;QACR;QAEA,IAAI,cAAc,OAAO;YACxB;QACD,OAAO;YACN,eAAe;YACf,cAAc;QACf;QAEA,OAAO,cAAc;IACtB;IAEA,MAAM,cAAc,EAAE;IAEtB,SAAS;QACR,MAAM,MAAM,KAAK,GAAG;QAEpB,0EAA0E;QAC1E,IAAI,YAAY,MAAM,GAAG,KAAK,MAAM,YAAY,EAAE,CAAC,CAAC,KAAK,UAAU;YAClE,YAAY,MAAM,GAAG;QACtB;QAEA,yEAAyE;QACzE,IAAI,YAAY,MAAM,GAAG,OAAO;YAC/B,YAAY,IAAI,CAAC;YACjB,OAAO;QACR;QAEA,yEAAyE;QACzE,MAAM,oBAAoB,WAAW,CAAC,EAAE,GAAG;QAE3C,iDAAiD;QACjD,YAAY,KAAK;QACjB,YAAY,IAAI,CAAC;QAEjB,gDAAgD;QAChD,OAAO,KAAK,GAAG,CAAC,GAAG,oBAAoB;IACxC;IAEA,MAAM,WAAW,SAAS,cAAc;IAExC,OAAO,CAAA;QACN,MAAM,YAAY,SAAU,GAAG,UAAU;YACxC,IAAI,CAAC,UAAU,SAAS,EAAE;gBACzB,OAAO,CAAC,UAAY,UAAU,KAAK,CAAC,IAAI,EAAE,WAAW;YACtD;YAEA,IAAI;YACJ,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC5B,MAAM,UAAU;oBACf,QAAQ,UAAU,KAAK,CAAC,IAAI,EAAE;oBAC9B,MAAM,MAAM,CAAC;gBACd;gBAEA,MAAM,QAAQ;gBACd,IAAI,QAAQ,GAAG;oBACd,YAAY,WAAW,SAAS;oBAChC,MAAM,GAAG,CAAC,WAAW;oBACrB,aAAa;gBACd,OAAO;oBACN;gBACD;YACD;QACD;QAEA,MAAM,UAAU;YACf,KAAK,MAAM,WAAW,MAAM,IAAI,GAAI;gBACnC,aAAa;gBACb,MAAM,GAAG,CAAC,SAAS,OAAO,MAAM;YACjC;YAEA,MAAM,KAAK;YACX,YAAY,MAAM,CAAC,GAAG,YAAY,MAAM;QACzC;QAEA,SAAS,QAAQ,CAAC,WAAW;YAAC;YAAQ;QAAO;QAE7C,QAAQ;QACR,QAAQ,iBAAiB,SAAS,SAAS;YAAC,MAAM;QAAI;QAEtD,UAAU,SAAS,GAAG;QAEtB,OAAO,cAAc,CAAC,WAAW,aAAa;YAC7C;gBACC,OAAO,MAAM,IAAI;YAClB;QACD;QAEA,OAAO;IACR;AACD", "ignoreList": [0], "debugId": null}}]}