{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface HealthStatus {\n  success: boolean\n  status: string\n  database: string\n  timestamp: string\n  stats?: {\n    users: number\n    posts: number\n  }\n  error?: string\n}\n\nexport default function Home() {\n  const [health, setHealth] = useState<HealthStatus | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    const checkHealth = async () => {\n      try {\n        const response = await fetch('/api/health')\n        const data = await response.json()\n        setHealth(data)\n      } catch (error) {\n        console.error('Failed to check health:', error)\n        setHealth({\n          success: false,\n          status: 'error',\n          database: 'unknown',\n          timestamp: new Date().toISOString(),\n          error: 'Failed to connect to API'\n        })\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    checkHealth()\n  }, [])\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Header */}\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Next.js + Cloud SQL Application\n            </h1>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n              A modern web application with Google Cloud SQL integration\n            </p>\n          </div>\n\n          {/* Health Status */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\">\n            <h2 className=\"text-2xl font-semibold text-gray-900 dark:text-white mb-4\">\n              System Health\n            </h2>\n            {loading ? (\n              <div className=\"flex items-center justify-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n                <span className=\"ml-2 text-gray-600 dark:text-gray-300\">Checking system health...</span>\n              </div>\n            ) : health ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    health.success ? 'bg-green-500' : 'bg-red-500'\n                  }`}></div>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">\n                    Status: {health.status}\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    health.database === 'connected' ? 'bg-green-500' : 'bg-red-500'\n                  }`}></div>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">\n                    Database: {health.database}\n                  </span>\n                </div>\n                {health.stats && (\n                  <div className=\"grid grid-cols-2 gap-4 mt-4\">\n                    <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                        {health.stats.users}\n                      </div>\n                      <div className=\"text-sm text-gray-600 dark:text-gray-300\">Users</div>\n                    </div>\n                    <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                        {health.stats.posts}\n                      </div>\n                      <div className=\"text-sm text-gray-600 dark:text-gray-300\">Posts</div>\n                    </div>\n                  </div>\n                )}\n                {health.error && (\n                  <div className=\"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg\">\n                    <p className=\"text-red-600 dark:text-red-400\">{health.error}</p>\n                  </div>\n                )}\n                <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  Last checked: {new Date(health.timestamp).toLocaleString()}\n                </div>\n              </div>\n            ) : null}\n          </div>\n\n          {/* API Endpoints */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-900 dark:text-white mb-4\">\n              Available API Endpoints\n            </h2>\n            <div className=\"space-y-4\">\n              <div className=\"border-l-4 border-blue-500 pl-4\">\n                <h3 className=\"font-semibold text-gray-900 dark:text-white\">Users API</h3>\n                <ul className=\"mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300\">\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">GET /api/users</code> - Get all users</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">POST /api/users</code> - Create a new user</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">GET /api/users/[id]</code> - Get user by ID</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">PUT /api/users/[id]</code> - Update user</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">DELETE /api/users/[id]</code> - Delete user</li>\n                </ul>\n              </div>\n              <div className=\"border-l-4 border-green-500 pl-4\">\n                <h3 className=\"font-semibold text-gray-900 dark:text-white\">Posts API</h3>\n                <ul className=\"mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300\">\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">GET /api/posts</code> - Get all posts</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">POST /api/posts</code> - Create a new post</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">GET /api/posts/[id]</code> - Get post by ID</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">PUT /api/posts/[id]</code> - Update post</li>\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">DELETE /api/posts/[id]</code> - Delete post</li>\n                </ul>\n              </div>\n              <div className=\"border-l-4 border-purple-500 pl-4\">\n                <h3 className=\"font-semibold text-gray-900 dark:text-white\">System API</h3>\n                <ul className=\"mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300\">\n                  <li><code className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">GET /api/health</code> - Health check</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU;YACZ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,UAAU;oBACR,SAAS;oBACT,QAAQ;oBACR,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW;oBACjC,OAAO;gBACT;YACF,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAM1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;4BAGzE,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;uCAExD,uBACF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,OAAO,OAAO,GAAG,iBAAiB,cAClC;;;;;;0DACF,8OAAC;gDAAK,WAAU;;oDAA4C;oDACjD,OAAO,MAAM;;;;;;;;;;;;;kDAG1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,OAAO,QAAQ,KAAK,cAAc,iBAAiB,cACnD;;;;;;0DACF,8OAAC;gDAAK,WAAU;;oDAA4C;oDAC/C,OAAO,QAAQ;;;;;;;;;;;;;oCAG7B,OAAO,KAAK,kBACX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,OAAO,KAAK,CAAC,KAAK;;;;;;kEAErB,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,OAAO,KAAK,CAAC,KAAK;;;;;;kEAErB,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;oCAI/D,OAAO,KAAK,kBACX,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAkC,OAAO,KAAK;;;;;;;;;;;kDAG/D,8OAAC;wCAAI,WAAU;;4CAA2C;4CACzC,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;;;;;;;;;;;;uCAG1D;;;;;;;kCAIN,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAAqB;;;;;;;kEAC1F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAAsB;;;;;;;kEAC3F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAA0B;;;;;;;kEAC/F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAA0B;;;;;;;kEAC/F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAA6B;;;;;;;;;;;;;;;;;;;kDAGtG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAAqB;;;;;;;kEAC1F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAAsB;;;;;;;kEAC3F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAA0B;;;;;;;kEAC/F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAA0B;;;;;;;kEAC/F,8OAAC;;0EAAG,8OAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAA6B;;;;;;;;;;;;;;;;;;;kDAGtG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;;sEAAG,8OAAC;4DAAK,WAAU;sEAAiD;;;;;;wDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7G", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cloudtest/nextjs-cloudsql-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}