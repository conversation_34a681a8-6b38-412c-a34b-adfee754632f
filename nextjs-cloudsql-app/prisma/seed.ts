import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create sample users
  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
    },
  })

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
    },
  })

  const user3 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
    },
  })

  console.log('👥 Created users:', { user1, user2, user3 })

  // Create sample posts
  const post1 = await prisma.post.create({
    data: {
      title: 'Getting Started with Next.js and Cloud SQL',
      content: 'This is a comprehensive guide on how to build a Next.js application with Google Cloud SQL integration. We will cover everything from setup to deployment on Cloud Run.',
      published: true,
      authorId: user1.id,
    },
  })

  const post2 = await prisma.post.create({
    data: {
      title: 'Building Scalable APIs with Prisma',
      content: 'Learn how to create robust and scalable APIs using Prisma ORM. This post covers best practices, performance optimization, and advanced features.',
      published: true,
      authorId: user2.id,
    },
  })

  const post3 = await prisma.post.create({
    data: {
      title: 'Deploying to Google Cloud Run',
      content: 'A step-by-step guide to deploying your containerized applications to Google Cloud Run. Includes tips for optimization and monitoring.',
      published: false,
      authorId: user1.id,
    },
  })

  const post4 = await prisma.post.create({
    data: {
      title: 'Database Migrations Best Practices',
      content: 'Understanding database migrations and how to manage them effectively in production environments.',
      published: true,
      authorId: user3.id,
    },
  })

  const post5 = await prisma.post.create({
    data: {
      title: 'TypeScript Tips and Tricks',
      content: 'Advanced TypeScript techniques that will make your code more robust and maintainable.',
      published: false,
      authorId: user2.id,
    },
  })

  console.log('📝 Created posts:', { post1, post2, post3, post4, post5 })

  // Display summary
  const userCount = await prisma.user.count()
  const postCount = await prisma.post.count()
  const publishedPostCount = await prisma.post.count({ where: { published: true } })

  console.log('📊 Database seeding completed!')
  console.log(`   Users: ${userCount}`)
  console.log(`   Posts: ${postCount}`)
  console.log(`   Published Posts: ${publishedPostCount}`)
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Error during seeding:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
