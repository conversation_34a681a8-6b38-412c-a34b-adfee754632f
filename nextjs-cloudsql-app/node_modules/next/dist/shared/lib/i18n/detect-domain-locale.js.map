{"version": 3, "sources": ["../../../../src/shared/lib/i18n/detect-domain-locale.ts"], "sourcesContent": ["import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n"], "names": ["detectDomainLocale", "domainItems", "hostname", "detectedLocale", "toLowerCase", "item", "domainHostname", "domain", "split", "defaultLocale", "locales", "some", "locale"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,mBACdC,WAAqC,EACrCC,QAAiB,EACjBC,cAAuB;IAEvB,IAAI,CAACF,aAAa;IAElB,IAAIE,gBAAgB;QAClBA,iBAAiBA,eAAeC,WAAW;IAC7C;IAEA,KAAK,MAAMC,QAAQJ,YAAa;YAEPI,cAIrBA;QALF,yBAAyB;QACzB,MAAMC,kBAAiBD,eAAAA,KAAKE,MAAM,qBAAXF,aAAaG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACJ,WAAW;QAChE,IACEF,aAAaI,kBACbH,mBAAmBE,KAAKI,aAAa,CAACL,WAAW,QACjDC,gBAAAA,KAAKK,OAAO,qBAAZL,cAAcM,IAAI,CAAC,CAACC,SAAWA,OAAOR,WAAW,OAAOD,kBACxD;YACA,OAAOE;QACT;IACF;AACF", "ignoreList": [0]}