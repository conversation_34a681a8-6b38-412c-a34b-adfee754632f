{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/utils.ts"], "sourcesContent": ["import type {\n  Issue,\n  PlainTraceItem,\n  StyledString,\n  TurbopackResult,\n} from '../../../build/swc/types'\n\nimport { bold, green, magenta, red } from '../../../lib/picocolors'\nimport isInternal from '../is-internal'\nimport {\n  decodeMagicIdentifier,\n  MAGIC_IDENTIFIER_REGEX,\n} from '../magic-identifier'\nimport type { EntryKey } from './entry-key'\nimport * as Log from '../../../build/output/log'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { eventErrorThrown } from '../../../telemetry/events'\nimport { traceGlobals } from '../../../trace/shared'\n\ntype IssueKey = `${Issue['severity']}-${Issue['filePath']}-${string}-${string}`\nexport type IssuesMap = Map<IssueKey, Issue>\nexport type EntryIssuesMap = Map<EntryKey, IssuesMap>\nexport type TopLevelIssuesMap = IssuesMap\n\n// An error generated from emitted Turbopack issues. This can include build\n// errors caused by issues with user code.\nexport class ModuleBuildError extends Error {\n  name = 'ModuleBuildError'\n}\n\n// An error caused by an internal issue in Turbopack. These should be written\n// to a log file and details should not be shown to the user.\nexport class TurbopackInternalError extends Error {\n  name = 'TurbopackInternalError'\n\n  // Manually set this as this isn't statically determinable\n  __NEXT_ERROR_CODE = 'TurbopackInternalError'\n\n  static createAndRecordTelemetry(cause: Error) {\n    const error = new TurbopackInternalError(cause)\n\n    const telemetry = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(eventErrorThrown(error))\n    } else {\n      console.error('Expected `telemetry` to be set in globals')\n    }\n\n    return error\n  }\n\n  constructor(cause: Error) {\n    super(cause.message)\n    this.stack = cause.stack\n  }\n}\n\n/**\n * Thin stopgap workaround layer to mimic existing wellknown-errors-plugin in webpack's build\n * to emit certain type of errors into cli.\n */\nexport function isWellKnownError(issue: Issue): boolean {\n  const { title } = issue\n  const formattedTitle = renderStyledStringToErrorAnsi(title)\n  // TODO: add more well known errors\n  if (\n    formattedTitle.includes('Module not found') ||\n    formattedTitle.includes('Unknown module type')\n  ) {\n    return true\n  }\n\n  return false\n}\n\nexport function getIssueKey(issue: Issue): IssueKey {\n  return `${issue.severity}-${issue.filePath}-${JSON.stringify(\n    issue.title\n  )}-${JSON.stringify(issue.description)}`\n}\n\nexport async function getTurbopackJsConfig(\n  dir: string,\n  nextConfig: NextConfigComplete\n) {\n  const { jsConfig } = await loadJsConfig(dir, nextConfig)\n  return jsConfig ?? { compilerOptions: {} }\n}\n\nexport function processIssues(\n  currentEntryIssues: EntryIssuesMap,\n  key: EntryKey,\n  result: TurbopackResult,\n  throwIssue: boolean,\n  logErrors: boolean\n) {\n  const newIssues = new Map<IssueKey, Issue>()\n  currentEntryIssues.set(key, newIssues)\n\n  const relevantIssues = new Set()\n\n  for (const issue of result.issues) {\n    if (\n      issue.severity !== 'error' &&\n      issue.severity !== 'fatal' &&\n      issue.severity !== 'warning'\n    )\n      continue\n\n    const issueKey = getIssueKey(issue)\n    newIssues.set(issueKey, issue)\n\n    if (issue.severity !== 'warning') {\n      if (throwIssue) {\n        const formatted = formatIssue(issue)\n        relevantIssues.add(formatted)\n      }\n      // if we throw the issue it will most likely get handed and logged elsewhere\n      else if (logErrors && isWellKnownError(issue)) {\n        const formatted = formatIssue(issue)\n        Log.error(formatted)\n      }\n    }\n  }\n\n  if (relevantIssues.size && throwIssue) {\n    throw new ModuleBuildError([...relevantIssues].join('\\n\\n'))\n  }\n}\n\nexport function formatIssue(issue: Issue) {\n  const { filePath, title, description, source, importTraces } = issue\n  let { documentationLink } = issue\n  const formattedTitle = renderStyledStringToErrorAnsi(title).replace(\n    /\\n/g,\n    '\\n    '\n  )\n\n  // TODO: Use error codes to identify these\n  // TODO: Generalize adapting Turbopack errors to Next.js errors\n  if (formattedTitle.includes('Module not found')) {\n    // For compatiblity with webpack\n    // TODO: include columns in webpack errors.\n    documentationLink = 'https://nextjs.org/docs/messages/module-not-found'\n  }\n\n  const formattedFilePath = filePath\n    .replace('[project]/', './')\n    .replaceAll('/./', '/')\n    .replace('\\\\\\\\?\\\\', '')\n\n  let message = ''\n\n  if (source?.range) {\n    const { start } = source.range\n    message = `${formattedFilePath}:${start.line + 1}:${\n      start.column + 1\n    }\\n${formattedTitle}`\n  } else if (formattedFilePath) {\n    message = `${formattedFilePath}\\n${formattedTitle}`\n  } else {\n    message = formattedTitle\n  }\n  message += '\\n'\n\n  if (\n    source?.range &&\n    source.source.content &&\n    // ignore Next.js/React internals, as these can often be huge bundled files.\n    !isInternal(filePath)\n  ) {\n    const { start, end } = source.range\n    const { codeFrameColumns } =\n      require('next/dist/compiled/babel/code-frame') as typeof import('next/dist/compiled/babel/code-frame')\n\n    message +=\n      codeFrameColumns(\n        source.source.content,\n        {\n          start: {\n            line: start.line + 1,\n            column: start.column + 1,\n          },\n          end: {\n            line: end.line + 1,\n            column: end.column + 1,\n          },\n        },\n        { forceColor: true }\n      ).trim() + '\\n\\n'\n  }\n\n  if (description) {\n    if (\n      description.type === 'text' &&\n      description.value.includes(`Cannot find module 'sass'`)\n    ) {\n      message +=\n        \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\"\n      message += 'Run `npm i sass` or `yarn add sass` inside your workspace.\\n'\n      message += '\\nLearn more: https://nextjs.org/docs/messages/install-sass'\n    } else {\n      message += renderStyledStringToErrorAnsi(description) + '\\n\\n'\n    }\n  }\n\n  // TODO: make it possible to enable this for debugging, but not in tests.\n  // if (detail) {\n  //   message += renderStyledStringToErrorAnsi(detail) + '\\n\\n'\n  // }\n\n  if (importTraces?.length) {\n    // This is the same logic as in turbopack/crates/turbopack-cli-utils/src/issue.rs\n    if (importTraces.length === 1) {\n      const trace = importTraces[0]\n      // We only display the layer if there is more than one for the trace\n      message += `Import trace:\\n${formatIssueTrace(trace, '  ', !identicalLayers(trace))}`\n    } else {\n      // We end up with multiple traces when the file with the error is reachable from multiple\n      // different entry points (e.g. ssr, client)\n      message += 'Import traces:\\n'\n      const everyTraceHasADistinctRootLayer =\n        new Set(importTraces.map(leafLayerName).filter((l) => l != null))\n          .size === importTraces.length\n      for (let i = 0; i < importTraces.length; i++) {\n        const trace = importTraces[i]\n        const layer = leafLayerName(trace)\n        if (everyTraceHasADistinctRootLayer) {\n          message += `  ${layer}:\\n`\n        } else {\n          message += `  #${i + 1}`\n          if (layer) {\n            message += ` [${layer}]`\n          }\n          message += ':\\n'\n        }\n        message += formatIssueTrace(trace, '    ', !identicalLayers(trace))\n      }\n    }\n  }\n  if (documentationLink) {\n    message += documentationLink + '\\n\\n'\n  }\n  return message\n}\n\n/** Returns the first present layer name in the trace */\nfunction leafLayerName(items: PlainTraceItem[]): string | undefined {\n  for (const item of items) {\n    const layer = item.layer\n    if (layer != null) return layer\n  }\n  return undefined\n}\n\n/**\n * Returns whether or not all items share the same layer.\n * If a layer is absent we ignore it in this analysis\n */\nfunction identicalLayers(items: PlainTraceItem[]): boolean {\n  const firstPresentLayer = items.findIndex((t) => t.layer != null)\n  if (firstPresentLayer === -1) return true // all layers are absent\n  const layer = items[firstPresentLayer].layer\n  for (let i = firstPresentLayer + 1; i < items.length; i++) {\n    const itemLayer = items[i].layer\n    if (itemLayer == null || itemLayer !== layer) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction formatIssueTrace(\n  items: PlainTraceItem[],\n  indent: string,\n  printLayers: boolean\n): string {\n  return (\n    items\n      .map((item) => {\n        let r = indent\n        if (item.fsName !== 'project') {\n          r += `[${item.fsName}]/`\n        } else {\n          // This is consistent with webpack's output\n          r += './'\n        }\n        r += item.path\n        if (printLayers && item.layer) {\n          r += ` [${item.layer}]`\n        }\n        return r\n      })\n      .join('\\n') + '\\n\\n'\n  )\n}\n\nexport function isRelevantWarning(issue: Issue): boolean {\n  return issue.severity === 'warning' && !isNodeModulesIssue(issue)\n}\n\nfunction isNodeModulesIssue(issue: Issue): boolean {\n  if (issue.severity === 'warning' && issue.stage === 'config') {\n    // Override for the externalize issue\n    // `Package foo (serverExternalPackages or default list) can't be external`\n    if (\n      renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n    ) {\n      return false\n    }\n  }\n\n  return (\n    issue.severity === 'warning' &&\n    (issue.filePath.match(/^(?:.*[\\\\/])?node_modules(?:[\\\\/].*)?$/) !== null ||\n      // Ignore Next.js itself when running next directly in the monorepo where it is not inside\n      // node_modules anyway.\n      // TODO(mischnic) prevent matches when this is published to npm\n      issue.filePath.startsWith('[project]/packages/next/'))\n  )\n}\n\nexport function renderStyledStringToErrorAnsi(string: StyledString): string {\n  function decodeMagicIdentifiers(str: string): string {\n    return str.replaceAll(MAGIC_IDENTIFIER_REGEX, (ident) => {\n      try {\n        return magenta(`{${decodeMagicIdentifier(ident)}}`)\n      } catch (e) {\n        return magenta(`{${ident} (decoding failed: ${e})}`)\n      }\n    })\n  }\n\n  switch (string.type) {\n    case 'text':\n      return decodeMagicIdentifiers(string.value)\n    case 'strong':\n      return bold(red(decodeMagicIdentifiers(string.value)))\n    case 'code':\n      return green(decodeMagicIdentifiers(string.value))\n    case 'line':\n      return string.value.map(renderStyledStringToErrorAnsi).join('')\n    case 'stack':\n      return string.value.map(renderStyledStringToErrorAnsi).join('\\n')\n    default:\n      throw new Error('Unknown StyledString type', string)\n  }\n}\n\nexport function isPersistentCachingEnabled(\n  config: NextConfigComplete\n): boolean {\n  return config.experimental?.turbopackPersistentCaching || false\n}\n"], "names": ["ModuleBuildError", "TurbopackInternalError", "formatIssue", "getIssueKey", "getTurbopackJsConfig", "isPersistentCachingEnabled", "isRelevantWarning", "isWellKnownError", "processIssues", "renderStyledStringToErrorAnsi", "Error", "name", "createAndRecordTelemetry", "cause", "error", "telemetry", "traceGlobals", "get", "record", "eventErrorThrown", "console", "constructor", "message", "__NEXT_ERROR_CODE", "stack", "issue", "title", "formattedTitle", "includes", "severity", "filePath", "JSON", "stringify", "description", "dir", "nextConfig", "jsConfig", "loadJsConfig", "compilerOptions", "currentEntryIssues", "key", "result", "throwIssue", "logErrors", "newIssues", "Map", "set", "relevantIssues", "Set", "issues", "issue<PERSON><PERSON>", "formatted", "add", "Log", "size", "join", "source", "importTraces", "documentationLink", "replace", "formattedFilePath", "replaceAll", "range", "start", "line", "column", "content", "isInternal", "end", "codeFrameColumns", "require", "forceColor", "trim", "type", "value", "length", "trace", "formatIssueTrace", "identicalLayers", "everyTraceHasADistinctRootLayer", "map", "leafLayerName", "filter", "l", "i", "layer", "items", "item", "undefined", "firstPresentLayer", "findIndex", "t", "itemLayer", "indent", "printLayers", "r", "fsName", "path", "isNodeModulesIssue", "stage", "match", "startsWith", "string", "decodeMagicIdentifiers", "str", "MAGIC_IDENTIFIER_REGEX", "ident", "magenta", "decodeMagicIdentifier", "e", "bold", "red", "green", "config", "experimental", "turbopackPersistentCaching"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IA2BaA,gBAAgB;eAAhBA;;IAMAC,sBAAsB;eAAtBA;;IAkGGC,WAAW;eAAXA;;IAvDAC,WAAW;eAAXA;;IAMMC,oBAAoB;eAApBA;;IA4QNC,0BAA0B;eAA1BA;;IApDAC,iBAAiB;eAAjBA;;IA5OAC,gBAAgB;eAAhBA;;IA4BAC,aAAa;eAAbA;;IAyOAC,6BAA6B;eAA7BA;;;;;4BA5T0B;qEACnB;iCAIhB;+DAEc;uEAEI;wBACQ;wBACJ;AAStB,MAAMT,yBAAyBU;;QAA/B,qBACLC,OAAO;;AACT;AAIO,MAAMV,+BAA+BS;IAM1C,OAAOE,yBAAyBC,KAAY,EAAE;QAC5C,MAAMC,QAAQ,IAAIb,uBAAuBY;QAEzC,MAAME,YAAYC,oBAAY,CAACC,GAAG,CAAC;QACnC,IAAIF,WAAW;YACbA,UAAUG,MAAM,CAACC,IAAAA,wBAAgB,EAACL;QACpC,OAAO;YACLM,QAAQN,KAAK,CAAC;QAChB;QAEA,OAAOA;IACT;IAEAO,YAAYR,KAAY,CAAE;QACxB,KAAK,CAACA,MAAMS,OAAO,QAnBrBX,OAAO,0BAEP,0DAA0D;aAC1DY,oBAAoB;QAiBlB,IAAI,CAACC,KAAK,GAAGX,MAAMW,KAAK;IAC1B;AACF;AAMO,SAASjB,iBAAiBkB,KAAY;IAC3C,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,MAAME,iBAAiBlB,8BAA8BiB;IACrD,mCAAmC;IACnC,IACEC,eAAeC,QAAQ,CAAC,uBACxBD,eAAeC,QAAQ,CAAC,wBACxB;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASzB,YAAYsB,KAAY;IACtC,OAAO,AAAGA,MAAMI,QAAQ,GAAC,MAAGJ,MAAMK,QAAQ,GAAC,MAAGC,KAAKC,SAAS,CAC1DP,MAAMC,KAAK,IACX,MAAGK,KAAKC,SAAS,CAACP,MAAMQ,WAAW;AACvC;AAEO,eAAe7B,qBACpB8B,GAAW,EACXC,UAA8B;IAE9B,MAAM,EAAEC,QAAQ,EAAE,GAAG,MAAMC,IAAAA,qBAAY,EAACH,KAAKC;IAC7C,OAAOC,mBAAAA,WAAY;QAAEE,iBAAiB,CAAC;IAAE;AAC3C;AAEO,SAAS9B,cACd+B,kBAAkC,EAClCC,GAAa,EACbC,MAAuB,EACvBC,UAAmB,EACnBC,SAAkB;IAElB,MAAMC,YAAY,IAAIC;IACtBN,mBAAmBO,GAAG,CAACN,KAAKI;IAE5B,MAAMG,iBAAiB,IAAIC;IAE3B,KAAK,MAAMvB,SAASgB,OAAOQ,MAAM,CAAE;QACjC,IACExB,MAAMI,QAAQ,KAAK,WACnBJ,MAAMI,QAAQ,KAAK,WACnBJ,MAAMI,QAAQ,KAAK,WAEnB;QAEF,MAAMqB,WAAW/C,YAAYsB;QAC7BmB,UAAUE,GAAG,CAACI,UAAUzB;QAExB,IAAIA,MAAMI,QAAQ,KAAK,WAAW;YAChC,IAAIa,YAAY;gBACd,MAAMS,YAAYjD,YAAYuB;gBAC9BsB,eAAeK,GAAG,CAACD;YACrB,OAEK,IAAIR,aAAapC,iBAAiBkB,QAAQ;gBAC7C,MAAM0B,YAAYjD,YAAYuB;gBAC9B4B,KAAIvC,KAAK,CAACqC;YACZ;QACF;IACF;IAEA,IAAIJ,eAAeO,IAAI,IAAIZ,YAAY;QACrC,MAAM,qBAAsD,CAAtD,IAAI1C,iBAAiB;eAAI+C;SAAe,CAACQ,IAAI,CAAC,UAA9C,qBAAA;mBAAA;wBAAA;0BAAA;QAAqD;IAC7D;AACF;AAEO,SAASrD,YAAYuB,KAAY;IACtC,MAAM,EAAEK,QAAQ,EAAEJ,KAAK,EAAEO,WAAW,EAAEuB,MAAM,EAAEC,YAAY,EAAE,GAAGhC;IAC/D,IAAI,EAAEiC,iBAAiB,EAAE,GAAGjC;IAC5B,MAAME,iBAAiBlB,8BAA8BiB,OAAOiC,OAAO,CACjE,OACA;IAGF,0CAA0C;IAC1C,+DAA+D;IAC/D,IAAIhC,eAAeC,QAAQ,CAAC,qBAAqB;QAC/C,gCAAgC;QAChC,2CAA2C;QAC3C8B,oBAAoB;IACtB;IAEA,MAAME,oBAAoB9B,SACvB6B,OAAO,CAAC,cAAc,MACtBE,UAAU,CAAC,OAAO,KAClBF,OAAO,CAAC,WAAW;IAEtB,IAAIrC,UAAU;IAEd,IAAIkC,0BAAAA,OAAQM,KAAK,EAAE;QACjB,MAAM,EAAEC,KAAK,EAAE,GAAGP,OAAOM,KAAK;QAC9BxC,UAAU,AAAGsC,oBAAkB,MAAGG,CAAAA,MAAMC,IAAI,GAAG,CAAA,IAAE,MAC/CD,CAAAA,MAAME,MAAM,GAAG,CAAA,IAChB,OAAItC;IACP,OAAO,IAAIiC,mBAAmB;QAC5BtC,UAAU,AAAGsC,oBAAkB,OAAIjC;IACrC,OAAO;QACLL,UAAUK;IACZ;IACAL,WAAW;IAEX,IACEkC,CAAAA,0BAAAA,OAAQM,KAAK,KACbN,OAAOA,MAAM,CAACU,OAAO,IACrB,4EAA4E;IAC5E,CAACC,IAAAA,mBAAU,EAACrC,WACZ;QACA,MAAM,EAAEiC,KAAK,EAAEK,GAAG,EAAE,GAAGZ,OAAOM,KAAK;QACnC,MAAM,EAAEO,gBAAgB,EAAE,GACxBC,QAAQ;QAEVhD,WACE+C,iBACEb,OAAOA,MAAM,CAACU,OAAO,EACrB;YACEH,OAAO;gBACLC,MAAMD,MAAMC,IAAI,GAAG;gBACnBC,QAAQF,MAAME,MAAM,GAAG;YACzB;YACAG,KAAK;gBACHJ,MAAMI,IAAIJ,IAAI,GAAG;gBACjBC,QAAQG,IAAIH,MAAM,GAAG;YACvB;QACF,GACA;YAAEM,YAAY;QAAK,GACnBC,IAAI,KAAK;IACf;IAEA,IAAIvC,aAAa;QACf,IACEA,YAAYwC,IAAI,KAAK,UACrBxC,YAAYyC,KAAK,CAAC9C,QAAQ,CAAE,8BAC5B;YACAN,WACE;YACFA,WAAW;YACXA,WAAW;QACb,OAAO;YACLA,WAAWb,8BAA8BwB,eAAe;QAC1D;IACF;IAEA,yEAAyE;IACzE,gBAAgB;IAChB,8DAA8D;IAC9D,IAAI;IAEJ,IAAIwB,gCAAAA,aAAckB,MAAM,EAAE;QACxB,iFAAiF;QACjF,IAAIlB,aAAakB,MAAM,KAAK,GAAG;YAC7B,MAAMC,QAAQnB,YAAY,CAAC,EAAE;YAC7B,oEAAoE;YACpEnC,WAAW,AAAC,oBAAiBuD,iBAAiBD,OAAO,MAAM,CAACE,gBAAgBF;QAC9E,OAAO;YACL,yFAAyF;YACzF,4CAA4C;YAC5CtD,WAAW;YACX,MAAMyD,kCACJ,IAAI/B,IAAIS,aAAauB,GAAG,CAACC,eAAeC,MAAM,CAAC,CAACC,IAAMA,KAAK,OACxD7B,IAAI,KAAKG,aAAakB,MAAM;YACjC,IAAK,IAAIS,IAAI,GAAGA,IAAI3B,aAAakB,MAAM,EAAES,IAAK;gBAC5C,MAAMR,QAAQnB,YAAY,CAAC2B,EAAE;gBAC7B,MAAMC,QAAQJ,cAAcL;gBAC5B,IAAIG,iCAAiC;oBACnCzD,WAAW,AAAC,OAAI+D,QAAM;gBACxB,OAAO;oBACL/D,WAAW,AAAC,QAAK8D,CAAAA,IAAI,CAAA;oBACrB,IAAIC,OAAO;wBACT/D,WAAW,AAAC,OAAI+D,QAAM;oBACxB;oBACA/D,WAAW;gBACb;gBACAA,WAAWuD,iBAAiBD,OAAO,QAAQ,CAACE,gBAAgBF;YAC9D;QACF;IACF;IACA,IAAIlB,mBAAmB;QACrBpC,WAAWoC,oBAAoB;IACjC;IACA,OAAOpC;AACT;AAEA,sDAAsD,GACtD,SAAS2D,cAAcK,KAAuB;IAC5C,KAAK,MAAMC,QAAQD,MAAO;QACxB,MAAMD,QAAQE,KAAKF,KAAK;QACxB,IAAIA,SAAS,MAAM,OAAOA;IAC5B;IACA,OAAOG;AACT;AAEA;;;CAGC,GACD,SAASV,gBAAgBQ,KAAuB;IAC9C,MAAMG,oBAAoBH,MAAMI,SAAS,CAAC,CAACC,IAAMA,EAAEN,KAAK,IAAI;IAC5D,IAAII,sBAAsB,CAAC,GAAG,OAAO,KAAK,wBAAwB;;IAClE,MAAMJ,QAAQC,KAAK,CAACG,kBAAkB,CAACJ,KAAK;IAC5C,IAAK,IAAID,IAAIK,oBAAoB,GAAGL,IAAIE,MAAMX,MAAM,EAAES,IAAK;QACzD,MAAMQ,YAAYN,KAAK,CAACF,EAAE,CAACC,KAAK;QAChC,IAAIO,aAAa,QAAQA,cAAcP,OAAO;YAC5C,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAASR,iBACPS,KAAuB,EACvBO,MAAc,EACdC,WAAoB;IAEpB,OACER,MACGN,GAAG,CAAC,CAACO;QACJ,IAAIQ,IAAIF;QACR,IAAIN,KAAKS,MAAM,KAAK,WAAW;YAC7BD,KAAK,AAAC,MAAGR,KAAKS,MAAM,GAAC;QACvB,OAAO;YACL,2CAA2C;YAC3CD,KAAK;QACP;QACAA,KAAKR,KAAKU,IAAI;QACd,IAAIH,eAAeP,KAAKF,KAAK,EAAE;YAC7BU,KAAK,AAAC,OAAIR,KAAKF,KAAK,GAAC;QACvB;QACA,OAAOU;IACT,GACCxC,IAAI,CAAC,QAAQ;AAEpB;AAEO,SAASjD,kBAAkBmB,KAAY;IAC5C,OAAOA,MAAMI,QAAQ,KAAK,aAAa,CAACqE,mBAAmBzE;AAC7D;AAEA,SAASyE,mBAAmBzE,KAAY;IACtC,IAAIA,MAAMI,QAAQ,KAAK,aAAaJ,MAAM0E,KAAK,KAAK,UAAU;QAC5D,qCAAqC;QACrC,2EAA2E;QAC3E,IACE1F,8BAA8BgB,MAAMC,KAAK,EAAEE,QAAQ,CAAC,sBACpD;YACA,OAAO;QACT;IACF;IAEA,OACEH,MAAMI,QAAQ,KAAK,aAClBJ,CAAAA,MAAMK,QAAQ,CAACsE,KAAK,CAAC,8CAA8C,QAClE,0FAA0F;IAC1F,uBAAuB;IACvB,+DAA+D;IAC/D3E,MAAMK,QAAQ,CAACuE,UAAU,CAAC,2BAA0B;AAE1D;AAEO,SAAS5F,8BAA8B6F,MAAoB;IAChE,SAASC,uBAAuBC,GAAW;QACzC,OAAOA,IAAI3C,UAAU,CAAC4C,uCAAsB,EAAE,CAACC;YAC7C,IAAI;gBACF,OAAOC,IAAAA,mBAAO,EAAC,AAAC,MAAGC,IAAAA,sCAAqB,EAACF,SAAO;YAClD,EAAE,OAAOG,GAAG;gBACV,OAAOF,IAAAA,mBAAO,EAAC,AAAC,MAAGD,QAAM,wBAAqBG,IAAE;YAClD;QACF;IACF;IAEA,OAAQP,OAAO7B,IAAI;QACjB,KAAK;YACH,OAAO8B,uBAAuBD,OAAO5B,KAAK;QAC5C,KAAK;YACH,OAAOoC,IAAAA,gBAAI,EAACC,IAAAA,eAAG,EAACR,uBAAuBD,OAAO5B,KAAK;QACrD,KAAK;YACH,OAAOsC,IAAAA,iBAAK,EAACT,uBAAuBD,OAAO5B,KAAK;QAClD,KAAK;YACH,OAAO4B,OAAO5B,KAAK,CAACM,GAAG,CAACvE,+BAA+B8C,IAAI,CAAC;QAC9D,KAAK;YACH,OAAO+C,OAAO5B,KAAK,CAACM,GAAG,CAACvE,+BAA+B8C,IAAI,CAAC;QAC9D;YACE,MAAM,qBAA8C,CAA9C,IAAI7C,MAAM,6BAA6B4F,SAAvC,qBAAA;uBAAA;4BAAA;8BAAA;YAA6C;IACvD;AACF;AAEO,SAASjG,2BACd4G,MAA0B;QAEnBA;IAAP,OAAOA,EAAAA,uBAAAA,OAAOC,YAAY,qBAAnBD,qBAAqBE,0BAA0B,KAAI;AAC5D", "ignoreList": [0]}