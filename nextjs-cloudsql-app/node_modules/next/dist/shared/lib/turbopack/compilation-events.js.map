{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/compilation-events.ts"], "sourcesContent": ["import type { Project } from '../../../build/swc/types'\nimport * as Log from '../../../build/output/log'\n\n/**\n * Subscribes to compilation events for `project` and prints them using the\n * `Log` library.\n *\n * The `signal` argument is partially implemented. The abort may not happen until the next\n * compilation event arrives.\n */\nexport function backgroundLogCompilationEvents(\n  project: Project,\n  { eventTypes, signal }: { eventTypes?: string[]; signal?: AbortSignal } = {}\n) {\n  ;(async function () {\n    for await (const event of project.compilationEventsSubscribe(eventTypes)) {\n      if (signal?.aborted) {\n        return\n      }\n      switch (event.severity) {\n        case 'EVENT':\n          Log.event(event.message)\n          break\n        case 'TRACE':\n          Log.trace(event.message)\n          break\n        case 'INFO':\n          Log.info(event.message)\n          break\n        case 'WARNING':\n          Log.warn(event.message)\n          break\n        case 'ERROR':\n          Log.error(event.message)\n          break\n        case 'FATAL':\n          Log.error(event.message)\n          break\n        default:\n          break\n      }\n    }\n  })()\n}\n"], "names": ["backgroundLogCompilationEvents", "project", "eventTypes", "signal", "event", "compilationEventsSubscribe", "aborted", "severity", "Log", "message", "trace", "info", "warn", "error"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;;+DATK;AASd,SAASA,+BACdC,OAAgB,EAChB;IAAA,IAAA,EAAEC,UAAU,EAAEC,MAAM,EAAmD,GAAvE,mBAA0E,CAAC,IAA3E;;IAEE,CAAA;QACA,WAAW,MAAMC,SAASH,QAAQI,0BAA0B,CAACH,YAAa;YACxE,IAAIC,0BAAAA,OAAQG,OAAO,EAAE;gBACnB;YACF;YACA,OAAQF,MAAMG,QAAQ;gBACpB,KAAK;oBACHC,KAAIJ,KAAK,CAACA,MAAMK,OAAO;oBACvB;gBACF,KAAK;oBACHD,KAAIE,KAAK,CAACN,MAAMK,OAAO;oBACvB;gBACF,KAAK;oBACHD,KAAIG,IAAI,CAACP,MAAMK,OAAO;oBACtB;gBACF,KAAK;oBACHD,KAAII,IAAI,CAACR,MAAMK,OAAO;oBACtB;gBACF,KAAK;oBACHD,KAAIK,KAAK,CAACT,MAAMK,OAAO;oBACvB;gBACF,KAAK;oBACHD,KAAIK,KAAK,CAACT,MAAMK,OAAO;oBACvB;gBACF;oBACE;YACJ;QACF;IACF,CAAA;AACF", "ignoreList": [0]}