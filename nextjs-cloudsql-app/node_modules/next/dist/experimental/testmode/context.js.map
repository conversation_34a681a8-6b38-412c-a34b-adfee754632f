{"version": 3, "sources": ["../../../src/experimental/testmode/context.ts"], "sourcesContent": ["import { AsyncLocalStorage } from 'node:async_hooks'\n\nexport interface TestReqInfo {\n  url: string\n  proxyPort: number\n  testData: string\n}\n\nexport interface TestRequestReader<R> {\n  url(req: R): string\n  header(req: R, name: string): string | null\n}\n\nconst testStorage = new AsyncLocalStorage<TestReqInfo>()\n\nfunction extractTestInfoFromRequest<R>(\n  req: R,\n  reader: TestRequestReader<R>\n): TestReqInfo | undefined {\n  const proxyPortHeader = reader.header(req, 'next-test-proxy-port')\n  if (!proxyPortHeader) {\n    return undefined\n  }\n  const url = reader.url(req)\n  const proxyPort = Number(proxyPortHeader)\n  const testData = reader.header(req, 'next-test-data') || ''\n  return { url, proxyPort, testData }\n}\n\nexport function withRequest<R, T>(\n  req: R,\n  reader: TestRequestReader<R>,\n  fn: () => T\n): T {\n  const testReqInfo = extractTestInfoFromRequest(req, reader)\n  if (!testReqInfo) {\n    return fn()\n  }\n  return testStorage.run(testReqInfo, fn)\n}\n\nexport function getTestReqInfo<R>(\n  req?: R,\n  reader?: TestRequestReader<R>\n): TestReqInfo | undefined {\n  const testReqInfo = testStorage.getStore()\n  if (testReqInfo) {\n    return testReqInfo\n  }\n  if (req && reader) {\n    return extractTestInfoFromRequest(req, reader)\n  }\n  return undefined\n}\n"], "names": ["getTestReqInfo", "withRequest", "testStorage", "AsyncLocalStorage", "extractTestInfoFromRequest", "req", "reader", "proxyPortHeader", "header", "undefined", "url", "proxyPort", "Number", "testData", "fn", "testReqInfo", "run", "getStore"], "mappings": ";;;;;;;;;;;;;;;IAyCgBA,cAAc;eAAdA;;IAZAC,WAAW;eAAXA;;;iCA7BkB;AAalC,MAAMC,cAAc,IAAIC,kCAAiB;AAEzC,SAASC,2BACPC,GAAM,EACNC,MAA4B;IAE5B,MAAMC,kBAAkBD,OAAOE,MAAM,CAACH,KAAK;IAC3C,IAAI,CAACE,iBAAiB;QACpB,OAAOE;IACT;IACA,MAAMC,MAAMJ,OAAOI,GAAG,CAACL;IACvB,MAAMM,YAAYC,OAAOL;IACzB,MAAMM,WAAWP,OAAOE,MAAM,CAACH,KAAK,qBAAqB;IACzD,OAAO;QAAEK;QAAKC;QAAWE;IAAS;AACpC;AAEO,SAASZ,YACdI,GAAM,EACNC,MAA4B,EAC5BQ,EAAW;IAEX,MAAMC,cAAcX,2BAA2BC,KAAKC;IACpD,IAAI,CAACS,aAAa;QAChB,OAAOD;IACT;IACA,OAAOZ,YAAYc,GAAG,CAACD,aAAaD;AACtC;AAEO,SAASd,eACdK,GAAO,EACPC,MAA6B;IAE7B,MAAMS,cAAcb,YAAYe,QAAQ;IACxC,IAAIF,aAAa;QACf,OAAOA;IACT;IACA,IAAIV,OAAOC,QAAQ;QACjB,OAAOF,2BAA2BC,KAAKC;IACzC;IACA,OAAOG;AACT", "ignoreList": [0]}