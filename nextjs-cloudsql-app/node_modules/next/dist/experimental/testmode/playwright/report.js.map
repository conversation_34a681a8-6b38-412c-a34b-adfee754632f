{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/report.ts"], "sourcesContent": ["import type { TestInfo } from '@playwright/test'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './next-worker-fixture'\nimport { step } from './step'\n\nasync function parseBody(\n  r: Pick<Request, 'headers' | 'json' | 'text' | 'arrayBuffer' | 'formData'>\n): Promise<Record<string, string>> {\n  const contentType = r.headers.get('content-type')\n  let error: string | undefined\n  let text: string | undefined\n  let json: unknown\n  let formData: FormData | undefined\n  let buffer: ArrayBuffer | undefined\n  if (contentType?.includes('text')) {\n    try {\n      text = await r.text()\n    } catch (e) {\n      error = 'failed to parse text'\n    }\n  } else if (contentType?.includes('json')) {\n    try {\n      json = await r.json()\n    } catch (e) {\n      error = 'failed to parse json'\n    }\n  } else if (contentType?.includes('form-data')) {\n    try {\n      formData = await r.formData()\n    } catch (e) {\n      error = 'failed to parse formData'\n    }\n  } else {\n    try {\n      buffer = await r.array<PERSON><PERSON>er()\n    } catch (e) {\n      error = 'failed to parse array<PERSON>uffer'\n    }\n  }\n  return {\n    ...(error ? { error } : null),\n    ...(text ? { text } : null),\n    ...(json ? { json: JSON.stringify(json) } : null),\n    ...(formData ? { formData: JSON.stringify(Array.from(formData)) } : null),\n    ...(buffer && buffer.byteLength > 0\n      ? { buffer: `base64;${Buffer.from(buffer).toString('base64')}` }\n      : null),\n  }\n}\n\nfunction parseHeaders(headers: Headers): Record<string, string> {\n  return Object.fromEntries(\n    Array.from(headers)\n      .sort(([key1], [key2]) => key1.localeCompare(key2))\n      .map(([key, value]) => {\n        return [`header.${key}`, value]\n      })\n  )\n}\n\nexport async function reportFetch(\n  testInfo: TestInfo,\n  req: Request,\n  handler: FetchHandler\n): Promise<Awaited<ReturnType<FetchHandler>>> {\n  return step(\n    testInfo,\n    {\n      title: `next.onFetch: ${req.method} ${req.url}`,\n      category: 'next.onFetch',\n      apiName: 'next.onFetch',\n      params: {\n        method: req.method,\n        url: req.url,\n        ...(await parseBody(req.clone())),\n        ...parseHeaders(req.headers),\n      },\n    },\n    async (complete) => {\n      const res = await handler(req)\n      if (res === undefined || res == null) {\n        complete({ error: { message: 'unhandled' } })\n      } else if (typeof res === 'string' && res !== 'continue') {\n        complete({ error: { message: res } })\n      } else {\n        let body: Record<string, unknown>\n        if (typeof res === 'string') {\n          body = { response: res }\n        } else {\n          const { status, statusText } = res\n          body = {\n            status,\n            ...(statusText ? { statusText } : null),\n            ...(await parseBody(res.clone())),\n            ...parseHeaders(res.headers),\n          }\n        }\n        await step(\n          testInfo,\n          {\n            title: `next.onFetch.fulfilled: ${req.method} ${req.url}`,\n            category: 'next.onFetch',\n            apiName: 'next.onFetch.fulfilled',\n            params: {\n              ...body,\n              'request.url': req.url,\n              'request.method': req.method,\n            },\n          },\n          async () => undefined\n        ).catch(() => undefined)\n      }\n      return res\n    }\n  )\n}\n"], "names": ["reportFetch", "parseBody", "r", "contentType", "headers", "get", "error", "text", "json", "formData", "buffer", "includes", "e", "arrayBuffer", "JSON", "stringify", "Array", "from", "byteLength", "<PERSON><PERSON><PERSON>", "toString", "parseHeaders", "Object", "fromEntries", "sort", "key1", "key2", "localeCompare", "map", "key", "value", "testInfo", "req", "handler", "step", "title", "method", "url", "category", "apiName", "params", "clone", "complete", "res", "undefined", "message", "body", "response", "status", "statusText", "catch"], "mappings": ";;;;+BA2DsBA;;;eAAAA;;;sBAzDD;AAErB,eAAeC,UACbC,CAA0E;IAE1E,MAAMC,cAAcD,EAAEE,OAAO,CAACC,GAAG,CAAC;IAClC,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIP,+BAAAA,YAAaQ,QAAQ,CAAC,SAAS;QACjC,IAAI;YACFJ,OAAO,MAAML,EAAEK,IAAI;QACrB,EAAE,OAAOK,GAAG;YACVN,QAAQ;QACV;IACF,OAAO,IAAIH,+BAAAA,YAAaQ,QAAQ,CAAC,SAAS;QACxC,IAAI;YACFH,OAAO,MAAMN,EAAEM,IAAI;QACrB,EAAE,OAAOI,GAAG;YACVN,QAAQ;QACV;IACF,OAAO,IAAIH,+BAAAA,YAAaQ,QAAQ,CAAC,cAAc;QAC7C,IAAI;YACFF,WAAW,MAAMP,EAAEO,QAAQ;QAC7B,EAAE,OAAOG,GAAG;YACVN,QAAQ;QACV;IACF,OAAO;QACL,IAAI;YACFI,SAAS,MAAMR,EAAEW,WAAW;QAC9B,EAAE,OAAOD,GAAG;YACVN,QAAQ;QACV;IACF;IACA,OAAO;QACL,GAAIA,QAAQ;YAAEA;QAAM,IAAI,IAAI;QAC5B,GAAIC,OAAO;YAAEA;QAAK,IAAI,IAAI;QAC1B,GAAIC,OAAO;YAAEA,MAAMM,KAAKC,SAAS,CAACP;QAAM,IAAI,IAAI;QAChD,GAAIC,WAAW;YAAEA,UAAUK,KAAKC,SAAS,CAACC,MAAMC,IAAI,CAACR;QAAW,IAAI,IAAI;QACxE,GAAIC,UAAUA,OAAOQ,UAAU,GAAG,IAC9B;YAAER,QAAQ,CAAC,OAAO,EAAES,OAAOF,IAAI,CAACP,QAAQU,QAAQ,CAAC,WAAW;QAAC,IAC7D,IAAI;IACV;AACF;AAEA,SAASC,aAAajB,OAAgB;IACpC,OAAOkB,OAAOC,WAAW,CACvBP,MAAMC,IAAI,CAACb,SACRoB,IAAI,CAAC,CAAC,CAACC,KAAK,EAAE,CAACC,KAAK,GAAKD,KAAKE,aAAa,CAACD,OAC5CE,GAAG,CAAC,CAAC,CAACC,KAAKC,MAAM;QAChB,OAAO;YAAC,CAAC,OAAO,EAAED,KAAK;YAAEC;SAAM;IACjC;AAEN;AAEO,eAAe9B,YACpB+B,QAAkB,EAClBC,GAAY,EACZC,OAAqB;IAErB,OAAOC,IAAAA,UAAI,EACTH,UACA;QACEI,OAAO,CAAC,cAAc,EAAEH,IAAII,MAAM,CAAC,CAAC,EAAEJ,IAAIK,GAAG,EAAE;QAC/CC,UAAU;QACVC,SAAS;QACTC,QAAQ;YACNJ,QAAQJ,IAAII,MAAM;YAClBC,KAAKL,IAAIK,GAAG;YACZ,GAAI,MAAMpC,UAAU+B,IAAIS,KAAK,GAAG;YAChC,GAAGpB,aAAaW,IAAI5B,OAAO,CAAC;QAC9B;IACF,GACA,OAAOsC;QACL,MAAMC,MAAM,MAAMV,QAAQD;QAC1B,IAAIW,QAAQC,aAAaD,OAAO,MAAM;YACpCD,SAAS;gBAAEpC,OAAO;oBAAEuC,SAAS;gBAAY;YAAE;QAC7C,OAAO,IAAI,OAAOF,QAAQ,YAAYA,QAAQ,YAAY;YACxDD,SAAS;gBAAEpC,OAAO;oBAAEuC,SAASF;gBAAI;YAAE;QACrC,OAAO;YACL,IAAIG;YACJ,IAAI,OAAOH,QAAQ,UAAU;gBAC3BG,OAAO;oBAAEC,UAAUJ;gBAAI;YACzB,OAAO;gBACL,MAAM,EAAEK,MAAM,EAAEC,UAAU,EAAE,GAAGN;gBAC/BG,OAAO;oBACLE;oBACA,GAAIC,aAAa;wBAAEA;oBAAW,IAAI,IAAI;oBACtC,GAAI,MAAMhD,UAAU0C,IAAIF,KAAK,GAAG;oBAChC,GAAGpB,aAAasB,IAAIvC,OAAO,CAAC;gBAC9B;YACF;YACA,MAAM8B,IAAAA,UAAI,EACRH,UACA;gBACEI,OAAO,CAAC,wBAAwB,EAAEH,IAAII,MAAM,CAAC,CAAC,EAAEJ,IAAIK,GAAG,EAAE;gBACzDC,UAAU;gBACVC,SAAS;gBACTC,QAAQ;oBACN,GAAGM,IAAI;oBACP,eAAed,IAAIK,GAAG;oBACtB,kBAAkBL,IAAII,MAAM;gBAC9B;YACF,GACA,UAAYQ,WACZM,KAAK,CAAC,IAAMN;QAChB;QACA,OAAOD;IACT;AAEJ", "ignoreList": [0]}