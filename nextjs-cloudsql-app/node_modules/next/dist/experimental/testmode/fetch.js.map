{"version": 3, "sources": ["../../../src/experimental/testmode/fetch.ts"], "sourcesContent": ["import type {\n  ProxyFetchRequest,\n  ProxyFetchResponse,\n  ProxyResponse,\n} from './proxy'\nimport { getTestReqInfo, type TestRequestReader } from './context'\n\ntype Fetch = typeof fetch\ntype FetchInputArg = Parameters<Fetch>[0]\ntype FetchInitArg = Parameters<Fetch>[1]\n\nexport const reader: TestRequestReader<Request> = {\n  url(req) {\n    return req.url\n  },\n  header(req, name) {\n    return req.headers.get(name)\n  },\n}\n\nfunction getTestStack(): string {\n  let stack = (new Error().stack ?? '').split('\\n')\n  // Skip the first line and find first non-empty line.\n  for (let i = 1; i < stack.length; i++) {\n    if (stack[i].length > 0) {\n      stack = stack.slice(i)\n      break\n    }\n  }\n  // Filter out franmework lines.\n  stack = stack.filter((f) => !f.includes('/next/dist/'))\n  // At most 5 lines.\n  stack = stack.slice(0, 5)\n  // Cleanup some internal info and trim.\n  stack = stack.map((s) => s.replace('webpack-internal:///(rsc)/', '').trim())\n  return stack.join('    ')\n}\n\nasync function buildProxyRequest(\n  testData: string,\n  request: Request\n): Promise<ProxyFetchRequest> {\n  const {\n    url,\n    method,\n    headers,\n    body,\n    cache,\n    credentials,\n    integrity,\n    mode,\n    redirect,\n    referrer,\n    referrerPolicy,\n  } = request\n  return {\n    testData,\n    api: 'fetch',\n    request: {\n      url,\n      method,\n      headers: [...Array.from(headers), ['next-test-stack', getTestStack()]],\n      body: body\n        ? Buffer.from(await request.arrayBuffer()).toString('base64')\n        : null,\n      cache,\n      credentials,\n      integrity,\n      mode,\n      redirect,\n      referrer,\n      referrerPolicy,\n    },\n  }\n}\n\nfunction buildResponse(proxyResponse: ProxyFetchResponse): Response {\n  const { status, headers, body } = proxyResponse.response\n  return new Response(body ? Buffer.from(body, 'base64') : null, {\n    status,\n    headers: new Headers(headers),\n  })\n}\n\nexport async function handleFetch(\n  originalFetch: Fetch,\n  request: Request\n): Promise<Response> {\n  const testInfo = getTestReqInfo(request, reader)\n  if (!testInfo) {\n    // Passthrough non-test requests.\n    return originalFetch(request)\n  }\n\n  const { testData, proxyPort } = testInfo\n  const proxyRequest = await buildProxyRequest(testData, request)\n\n  const resp = await originalFetch(`http://localhost:${proxyPort}`, {\n    method: 'POST',\n    body: JSON.stringify(proxyRequest),\n    next: {\n      // @ts-ignore\n      internal: true,\n    },\n  })\n  if (!resp.ok) {\n    throw new Error(`Proxy request failed: ${resp.status}`)\n  }\n\n  const proxyResponse = (await resp.json()) as ProxyResponse\n  const { api } = proxyResponse\n  switch (api) {\n    case 'continue':\n      return originalFetch(request)\n    case 'abort':\n    case 'unhandled':\n      throw new Error(\n        `Proxy request aborted [${request.method} ${request.url}]`\n      )\n    default:\n      break\n  }\n  return buildResponse(proxyResponse)\n}\n\nexport function interceptFetch(originalFetch: Fetch) {\n  global.fetch = function testFetch(\n    input: FetchInputArg,\n    init?: FetchInitArg\n  ): Promise<Response> {\n    // Passthrough internal requests.\n    // @ts-ignore\n    if (init?.next?.internal) {\n      return originalFetch(input, init)\n    }\n    return handleFetch(originalFetch, new Request(input, init))\n  }\n  return () => {\n    global.fetch = originalFetch\n  }\n}\n"], "names": ["handleFetch", "interceptFetch", "reader", "url", "req", "header", "name", "headers", "get", "getTestStack", "stack", "Error", "split", "i", "length", "slice", "filter", "f", "includes", "map", "s", "replace", "trim", "join", "buildProxyRequest", "testData", "request", "method", "body", "cache", "credentials", "integrity", "mode", "redirect", "referrer", "referrerPolicy", "api", "Array", "from", "<PERSON><PERSON><PERSON>", "arrayBuffer", "toString", "buildResponse", "proxyResponse", "status", "response", "Response", "Headers", "originalFetch", "testInfo", "getTestReqInfo", "proxyPort", "proxyRequest", "resp", "JSON", "stringify", "next", "internal", "ok", "json", "global", "fetch", "testFetch", "input", "init", "Request"], "mappings": ";;;;;;;;;;;;;;;;IAoFsBA,WAAW;eAAXA;;IAyCNC,cAAc;eAAdA;;IAlHHC,MAAM;eAANA;;;yBAN0C;AAMhD,MAAMA,SAAqC;IAChDC,KAAIC,GAAG;QACL,OAAOA,IAAID,GAAG;IAChB;IACAE,QAAOD,GAAG,EAAEE,IAAI;QACd,OAAOF,IAAIG,OAAO,CAACC,GAAG,CAACF;IACzB;AACF;AAEA,SAASG;IACP,IAAIC,QAAQ,AAAC,CAAA,IAAIC,QAAQD,KAAK,IAAI,EAAC,EAAGE,KAAK,CAAC;IAC5C,qDAAqD;IACrD,IAAK,IAAIC,IAAI,GAAGA,IAAIH,MAAMI,MAAM,EAAED,IAAK;QACrC,IAAIH,KAAK,CAACG,EAAE,CAACC,MAAM,GAAG,GAAG;YACvBJ,QAAQA,MAAMK,KAAK,CAACF;YACpB;QACF;IACF;IACA,+BAA+B;IAC/BH,QAAQA,MAAMM,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEC,QAAQ,CAAC;IACxC,mBAAmB;IACnBR,QAAQA,MAAMK,KAAK,CAAC,GAAG;IACvB,uCAAuC;IACvCL,QAAQA,MAAMS,GAAG,CAAC,CAACC,IAAMA,EAAEC,OAAO,CAAC,8BAA8B,IAAIC,IAAI;IACzE,OAAOZ,MAAMa,IAAI,CAAC;AACpB;AAEA,eAAeC,kBACbC,QAAgB,EAChBC,OAAgB;IAEhB,MAAM,EACJvB,GAAG,EACHwB,MAAM,EACNpB,OAAO,EACPqB,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACf,GAAGT;IACJ,OAAO;QACLD;QACAW,KAAK;QACLV,SAAS;YACPvB;YACAwB;YACApB,SAAS;mBAAI8B,MAAMC,IAAI,CAAC/B;gBAAU;oBAAC;oBAAmBE;iBAAe;aAAC;YACtEmB,MAAMA,OACFW,OAAOD,IAAI,CAAC,MAAMZ,QAAQc,WAAW,IAAIC,QAAQ,CAAC,YAClD;YACJZ;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;QACF;IACF;AACF;AAEA,SAASO,cAAcC,aAAiC;IACtD,MAAM,EAAEC,MAAM,EAAErC,OAAO,EAAEqB,IAAI,EAAE,GAAGe,cAAcE,QAAQ;IACxD,OAAO,IAAIC,SAASlB,OAAOW,OAAOD,IAAI,CAACV,MAAM,YAAY,MAAM;QAC7DgB;QACArC,SAAS,IAAIwC,QAAQxC;IACvB;AACF;AAEO,eAAeP,YACpBgD,aAAoB,EACpBtB,OAAgB;IAEhB,MAAMuB,WAAWC,IAAAA,uBAAc,EAACxB,SAASxB;IACzC,IAAI,CAAC+C,UAAU;QACb,iCAAiC;QACjC,OAAOD,cAActB;IACvB;IAEA,MAAM,EAAED,QAAQ,EAAE0B,SAAS,EAAE,GAAGF;IAChC,MAAMG,eAAe,MAAM5B,kBAAkBC,UAAUC;IAEvD,MAAM2B,OAAO,MAAML,cAAc,CAAC,iBAAiB,EAAEG,WAAW,EAAE;QAChExB,QAAQ;QACRC,MAAM0B,KAAKC,SAAS,CAACH;QACrBI,MAAM;YACJ,aAAa;YACbC,UAAU;QACZ;IACF;IACA,IAAI,CAACJ,KAAKK,EAAE,EAAE;QACZ,MAAM,qBAAiD,CAAjD,IAAI/C,MAAM,CAAC,sBAAsB,EAAE0C,KAAKT,MAAM,EAAE,GAAhD,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEA,MAAMD,gBAAiB,MAAMU,KAAKM,IAAI;IACtC,MAAM,EAAEvB,GAAG,EAAE,GAAGO;IAChB,OAAQP;QACN,KAAK;YACH,OAAOY,cAActB;QACvB,KAAK;QACL,KAAK;YACH,MAAM,qBAEL,CAFK,IAAIf,MACR,CAAC,uBAAuB,EAAEe,QAAQC,MAAM,CAAC,CAAC,EAAED,QAAQvB,GAAG,CAAC,CAAC,CAAC,GADtD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;YACE;IACJ;IACA,OAAOuC,cAAcC;AACvB;AAEO,SAAS1C,eAAe+C,aAAoB;IACjDY,OAAOC,KAAK,GAAG,SAASC,UACtBC,KAAoB,EACpBC,IAAmB;YAIfA;QAFJ,iCAAiC;QACjC,aAAa;QACb,IAAIA,yBAAAA,aAAAA,KAAMR,IAAI,qBAAVQ,WAAYP,QAAQ,EAAE;YACxB,OAAOT,cAAce,OAAOC;QAC9B;QACA,OAAOhE,YAAYgD,eAAe,IAAIiB,QAAQF,OAAOC;IACvD;IACA,OAAO;QACLJ,OAAOC,KAAK,GAAGb;IACjB;AACF", "ignoreList": [0]}