{"version": 3, "sources": ["../../../src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .createRenderParamsFromClient\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).createRenderParamsFromClient\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,+BACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB,AAACC,QAAQ,wBACNJ,4BAA4B,GAC/B,AACEI,QAAQ,yBACRJ,4BAA4B", "ignoreList": [0]}