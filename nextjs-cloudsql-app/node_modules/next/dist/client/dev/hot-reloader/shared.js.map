{"version": 3, "sources": ["../../../../src/client/dev/hot-reloader/shared.ts"], "sourcesContent": ["import type { HMR_ACTION_TYPES } from '../../../server/dev/hot-reloader-types'\n\nexport const REACT_REFRESH_FULL_RELOAD =\n  '[Fast Refresh] performing full reload\\n\\n' +\n  \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n  'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n  'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n  'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n  'Fast Refresh requires at least one parent function component in your React tree.'\n\nexport const REACT_REFRESH_FULL_RELOAD_FROM_ERROR =\n  '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n\nexport function reportInvalidHmrMessage(\n  message: HMR_ACTION_TYPES | MessageEvent<unknown>,\n  err: unknown\n) {\n  console.warn(\n    '[HMR] Invalid message: ' +\n      JSON.stringify(message) +\n      '\\n' +\n      ((err instanceof Error && err?.stack) || '')\n  )\n}\n"], "names": ["REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "reportInvalidHmrMessage", "message", "err", "console", "warn", "JSON", "stringify", "Error", "stack"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,yBAAyB;eAAzBA;;IAQAC,oCAAoC;eAApCA;;IAGGC,uBAAuB;eAAvBA;;;AAXT,MAAMF,4BACX,8CACA,mIACA,qIACA,+GACA,8HACA;AAEK,MAAMC,uCACX;AAEK,SAASC,wBACdC,OAAiD,EACjDC,GAAY;IAEZC,QAAQC,IAAI,CACV,4BACEC,KAAKC,SAAS,CAACL,WACf,OACC,CAAA,AAACC,eAAeK,UAASL,uBAAAA,IAAKM,KAAK,KAAK,EAAC;AAEhD", "ignoreList": [0]}