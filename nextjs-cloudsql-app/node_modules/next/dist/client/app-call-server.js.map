{"version": 3, "sources": ["../../src/client/app-call-server.ts"], "sourcesContent": ["import { startTransition } from 'react'\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types'\nimport { dispatchAppRouterAction } from './components/use-action-queue'\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  return new Promise((resolve, reject) => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_SERVER_ACTION,\n        actionId,\n        actionArgs,\n        resolve,\n        reject,\n      })\n    })\n  })\n}\n"], "names": ["callServer", "actionId", "actionArgs", "Promise", "resolve", "reject", "startTransition", "dispatchAppRouterAction", "type", "ACTION_SERVER_ACTION"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;uBAJU;oCACK;gCACG;AAEjC,eAAeA,WAAWC,QAAgB,EAAEC,UAAiB;IAClE,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BC,IAAAA,sBAAe,EAAC;YACdC,IAAAA,uCAAuB,EAAC;gBACtBC,MAAMC,wCAAoB;gBAC1BR;gBACAC;gBACAE;gBACAC;YACF;QACF;IACF;AACF", "ignoreList": [0]}