{"version": 3, "sources": ["../../../../src/client/components/builtin/global-not-found.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nfunction GlobalNotFound() {\n  return (\n    <html>\n      <body>\n        <HTTPAccessErrorFallback\n          status={404}\n          message={'This page could not be found.'}\n        />\n      </body>\n    </html>\n  )\n}\n\nexport default GlobalNotFound\n"], "names": ["GlobalNotFound", "html", "body", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;;+BAeA;;;eAAA;;;;+BAfwC;AAExC,SAASA;IACP,qBACE,qBAACC;kBACC,cAAA,qBAACC;sBACC,cAAA,qBAACC,sCAAuB;gBACtBC,QAAQ;gBACRC,SAAS;;;;AAKnB;MAEA,WAAeL", "ignoreList": [0]}