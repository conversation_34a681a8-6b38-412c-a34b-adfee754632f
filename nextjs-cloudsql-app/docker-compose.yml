version: '3.8'

services:
  # MySQL Database for local development
  mysql:
    image: mysql:8.0
    container_name: nextjs-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: nextjs_cloudsql_db
      MYSQL_USER: nextjs_user
      MYSQL_PASSWORD: nextjs_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - nextjs-network

  # Next.js Application
  nextjs-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nextjs-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://nextjs_user:nextjs_password@mysql:3306/nextjs_cloudsql_db
      - PORT=8080
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    networks:
      - nextjs-network
    volumes:
      - ./prisma:/app/prisma

  # Development version (for local development)
  nextjs-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: nextjs-dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://nextjs_user:nextjs_password@mysql:3306/nextjs_cloudsql_db
      - PORT=3000
    ports:
      - "3000:3000"
    depends_on:
      - mysql
    networks:
      - nextjs-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next

volumes:
  mysql_data:

networks:
  nextjs-network:
    driver: bridge
