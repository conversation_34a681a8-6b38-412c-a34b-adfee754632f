#!/bin/bash

# Setup Google Cloud secrets for the Next.js app
# Usage: ./setup-secrets.sh [PROJECT_ID]

set -e

PROJECT_ID=${1:-"your-project-id"}
echo "🔐 Setting up secrets for project: $PROJECT_ID"

# Set the project
gcloud config set project $PROJECT_ID

# Enable Secret Manager API
echo "🔧 Enabling Secret Manager API..."
gcloud services enable secretmanager.googleapis.com

# Function to create or update secret
create_or_update_secret() {
    local secret_name=$1
    local secret_value=$2
    
    echo "📝 Processing secret: $secret_name"
    
    # Check if secret exists
    if gcloud secrets describe $secret_name >/dev/null 2>&1; then
        echo "  ↻ Updating existing secret: $secret_name"
        echo -n "$secret_value" | gcloud secrets versions add $secret_name --data-file=-
    else
        echo "  ✨ Creating new secret: $secret_name"
        echo -n "$secret_value" | gcloud secrets create $secret_name --data-file=-
    fi
}

# Prompt for database configuration
echo ""
echo "📋 Please provide your Cloud SQL database configuration:"

read -p "Database User: " DB_USER
read -s -p "Database Password: " DB_PASS
echo ""
read -p "Database Name: " DB_NAME
read -p "Instance Connection Name (project:region:instance): " INSTANCE_CONNECTION_NAME

# Construct DATABASE_URL
DATABASE_URL="mysql://$DB_USER:$DB_PASS@localhost/$DB_NAME?host=/cloudsql/$INSTANCE_CONNECTION_NAME"

echo ""
echo "🔐 Creating secrets..."

# Create secrets
create_or_update_secret "database-url" "$DATABASE_URL"
create_or_update_secret "db-user" "$DB_USER"
create_or_update_secret "db-password" "$DB_PASS"
create_or_update_secret "db-name" "$DB_NAME"
create_or_update_secret "instance-connection-name" "$INSTANCE_CONNECTION_NAME"

echo ""
echo "✅ Secrets created successfully!"
echo ""
echo "📝 Created secrets:"
echo "  - database-url"
echo "  - db-user"
echo "  - db-password"
echo "  - db-name"
echo "  - instance-connection-name"
echo ""
echo "🔗 To view secrets:"
echo "  gcloud secrets list"
echo ""
echo "🔗 To access secret values:"
echo "  gcloud secrets versions access latest --secret=\"database-url\""
