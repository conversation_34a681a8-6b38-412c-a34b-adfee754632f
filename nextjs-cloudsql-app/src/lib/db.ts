import { PrismaClient } from '@prisma/client'
import { Connector } from '@google-cloud/cloud-sql-connector'
import mysql from 'mysql2/promise'

declare global {
  var __prisma: PrismaClient | undefined
}

// Cloud SQL Connector instance
let connector: Connector | null = null

// Function to create database connection for Cloud SQL
async function createCloudSqlConnection() {
  if (!connector) {
    connector = new Connector()
  }

  const clientOpts = await connector.getOptions({
    instanceConnectionName: process.env.INSTANCE_CONNECTION_NAME!,
    ipType: 'PUBLIC',
  })

  const pool = mysql.createPool({
    ...clientOpts,
    user: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME,
  })

  return pool
}

// Prisma Client with connection pooling
function createPrismaClient() {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })
}

// Global Prisma instance
const prisma = globalThis.__prisma || createPrismaClient()

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect()
  if (connector) {
    connector.close()
  }
})

export { prisma, createCloudSqlConnection }
export default prisma
