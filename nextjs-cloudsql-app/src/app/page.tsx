'use client'

import { useEffect, useState } from 'react'

interface HealthStatus {
  success: boolean
  status: string
  database: string
  timestamp: string
  stats?: {
    users: number
    posts: number
  }
  error?: string
}

export default function Home() {
  const [health, setHealth] = useState<HealthStatus | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkHealth = async () => {
      try {
        const response = await fetch('/api/health')
        const data = await response.json()
        setHealth(data)
      } catch (error) {
        console.error('Failed to check health:', error)
        setHealth({
          success: false,
          status: 'error',
          database: 'unknown',
          timestamp: new Date().toISOString(),
          error: 'Failed to connect to API'
        })
      } finally {
        setLoading(false)
      }
    }

    checkHealth()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Next.js + Cloud SQL Application
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              A modern web application with Google Cloud SQL integration
            </p>
          </div>

          {/* Health Status */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              System Health
            </h2>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600 dark:text-gray-300">Checking system health...</span>
              </div>
            ) : health ? (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    health.success ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className="font-medium text-gray-900 dark:text-white">
                    Status: {health.status}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    health.database === 'connected' ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className="font-medium text-gray-900 dark:text-white">
                    Database: {health.database}
                  </span>
                </div>
                {health.stats && (
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {health.stats.users}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">Users</div>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {health.stats.posts}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">Posts</div>
                    </div>
                  </div>
                )}
                {health.error && (
                  <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                    <p className="text-red-600 dark:text-red-400">{health.error}</p>
                  </div>
                )}
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Last checked: {new Date(health.timestamp).toLocaleString()}
                </div>
              </div>
            ) : null}
          </div>

          {/* API Endpoints */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              Available API Endpoints
            </h2>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Users API</h3>
                <ul className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300">
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">GET /api/users</code> - Get all users</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">POST /api/users</code> - Create a new user</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">GET /api/users/[id]</code> - Get user by ID</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">PUT /api/users/[id]</code> - Update user</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">DELETE /api/users/[id]</code> - Delete user</li>
                </ul>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Posts API</h3>
                <ul className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300">
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">GET /api/posts</code> - Get all posts</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">POST /api/posts</code> - Create a new post</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">GET /api/posts/[id]</code> - Get post by ID</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">PUT /api/posts/[id]</code> - Update post</li>
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">DELETE /api/posts/[id]</code> - Delete post</li>
                </ul>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">System API</h3>
                <ul className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300">
                  <li><code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">GET /api/health</code> - Health check</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
