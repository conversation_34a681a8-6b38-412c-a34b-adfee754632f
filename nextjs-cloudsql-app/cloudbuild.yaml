# Google Cloud Build configuration for Next.js app
steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/nextjs-cloudsql-app:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/nextjs-cloudsql-app:latest',
      '.'
    ]

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/nextjs-cloudsql-app:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/nextjs-cloudsql-app:latest']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', 'nextjs-cloudsql-app',
      '--image', 'gcr.io/$PROJECT_ID/nextjs-cloudsql-app:$COMMIT_SHA',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--port', '8080',
      '--memory', '1Gi',
      '--cpu', '1',
      '--max-instances', '10',
      '--set-env-vars', 'NODE_ENV=production',
      '--add-cloudsql-instances', '$_INSTANCE_CONNECTION_NAME',
      '--set-secrets', 'DATABASE_URL=database-url:latest',
      '--set-secrets', 'DB_USER=db-user:latest',
      '--set-secrets', 'DB_PASS=db-password:latest',
      '--set-secrets', 'DB_NAME=db-name:latest'
    ]

# Store images in Google Container Registry
images:
  - 'gcr.io/$PROJECT_ID/nextjs-cloudsql-app:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/nextjs-cloudsql-app:latest'

# Substitutions for build variables
substitutions:
  _INSTANCE_CONNECTION_NAME: 'your-project:us-central1:your-instance'

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
